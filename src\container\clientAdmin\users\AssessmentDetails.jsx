/* eslint-disable react/jsx-key */
/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable react/prop-types */
/* eslint-disable no-unused-vars */
/* eslint-disable arrow-body-style */
import React, { useState, useEffect } from 'react';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import PersonAddIcon from '@mui/icons-material/PersonAdd';
import { makeStyles } from '@mui/styles';
import { Grid, Typography, Button, Snackbar, Alert, Chip,Table, TableBody, TableCell,
  TableContainer, TableHead, TableRow, Paper,TablePagination } from '@mui/material';
  import { Search, Add, Delete } from '@mui/icons-material';
import MUIDataTable from 'mui-datatables';
import { Link as RouterLink, useLocation } from 'react-router-dom';
import moment from 'moment';
import IconButton from '@mui/material/IconButton';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import MenuItem from '@mui/material/MenuItem';
import Menu from '@mui/material/Menu';
import { LoadingButton } from '@mui/lab';
import { createTheme, useTheme, ThemeProvider } from '@mui/material/styles';

// import AddSubscriptionForm from './AddSubscriptionForm';
// import AddUsersForm from './AddUsers';

import ClientAdminServices from '../../../services/clientAdmin/course/clientAdminServices';
import Page from '../../../components/Page';
import PageHeader from '../../../components/PageHeader';

export default function EnrolledUser() {  
  const classes = useStyles();
  const location = useLocation();
  const [handleOpenUserModal, setHandleOpenUserModal] = React.useState(false);
  const [handleOpenUserTable, setHandleOpenUserTable] = React.useState(false);
  const [openSnackbar, setOpenSnackbar] = React.useState(false);
  const [snackbarTitle, setSnackbarTitle] = React.useState('');
  const [anchorEl, setAnchorEl] = React.useState(null);
  const [subscriptionId, setSubscriptionId] = React.useState();
  const [clientPlanId, setClientPlanId] = React.useState();
  const [loading, setLoading] = React.useState(true);
  const [values, setValues] = React.useState('');
  const [coursePlanId, setCoursePlanId] = React.useState();
  const [subscriptionData, setSubscriptionData] = React.useState([]);
  const [error, setError] = React.useState([]);
  const [enrolledUser, setEnrolledUser] = useState([]);
  const [openAlertModal, setOpenAlertModal] = React.useState(false);
  const [deleteAlert, setDeleteAlert] = React.useState(false);
  const [planeId, setPlaneId] = React.useState(0);
  const [userId, setUserId] = React.useState(0);

    const [page, setPage] = useState(0);
    const [rowsPerPage, setRowsPerPage] = useState(10);
    const [sortKey, setSortKey] = useState('');
    const [sortOrder, setSortOrder] = useState('asc');

console.log(location,'locationlocation');

   const handleDelete = (userId, planeId) =>{
     setPlaneId(planeId);
     setUserId(userId)
     setDeleteAlert(true)
   }
  const getEnrolledUserById = async (planId) => {
    setLoading(true);
    console.log(subscriptionId, 'subscriptionId');
    try {
      const result = await ClientAdminServices.getEnrolledUser(location.state.id,location.state.planId);
      if (result.ok) {
        setEnrolledUser(result.data);
        setLoading(false);
      }
    } catch (error) {
      console.log(error);
    }
  };

    useEffect(() => {
    getEnrolledUserById(page, rowsPerPage, sortOrder, sortKey);
  }, [page, rowsPerPage, sortKey, sortOrder]);

  useEffect(() => {
    if(location.state.id&&location.state.planId)
    getEnrolledUserById();
  }, []);
   


  return (
    <Page title="Assessment-list">
      <PageHeader pageTitle="Assessments Details" />
      <Grid container spacing={3}>
        <Grid item xs="12">
          <Paper>
            <TableContainer>
              <Table>
                <TableHead sx={{ backgroundColor: '#f4f6f8' }}>
                  <TableRow>
                    <TableCell>Name</TableCell>
                    <TableCell>Email</TableCell>
                    <TableCell>Phone No</TableCell>
                    <TableCell>Last Login</TableCell>
                    <TableCell align="center">Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {/* {console.log(enrolledUser, 'enrolledUser12222')} */}
                  {enrolledUser?.map((entry) => (
                    <TableRow key={entry.id}>
                      <TableCell>{entry.fullName}</TableCell>
                      <TableCell>{entry.email}</TableCell>
                      <TableCell>{entry.phone ? entry.phone:'-'}</TableCell>
                      <TableCell>{entry?.lastLoginDateTime ? moment(entry?.lastLoginDateTime).format('MM-DD-YYYY HH:mm:ss'):'-'}</TableCell>
                      <TableCell align="center">
                        <IconButton color="error" onClick={() => { handleDelete(entry.id, entry.planId) }}>
                          <Delete />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
            <TablePagination
                    component="div"
                    count={enrolledUser.length}
                    page={page}
                    onPageChange={(e, newPage) => setPage(newPage)}
                    rowsPerPage={rowsPerPage}
                    onRowsPerPageChange={(e) => {
                      setRowsPerPage(parseInt(e.target.value, 10));
                      setPage(0);
                    }}
                    rowsPerPageOptions={[10, 25, 50, 75, 100]}
                  />

          </Paper>

        </Grid>


      </Grid>
    </Page>
  );
}
const useStyles = makeStyles((theme) => ({
  button: {
    display: 'flex',
    justifyContent: 'end',
    [theme.breakpoints.down(514)]: {
      marginTop: '14px',
    },
  },
  tableData: {
    border: '1px solid #dddddd',
    textAlign: 'left',
    padding: '8px',
  },
}));

const customeTheme = createTheme({
  components: {
    MuiBackdrop: {
      styleOverrides: {
        root: {
          '&.MuiModal-backdrop': {
            background: 'transparent',
          },
        },
      },
    },
    MuiTypography: {
      styleOverrides: {
        root: {
          fontFamily: 'Inter,SemiBold',
        },
      },
    },
    MuiLoadingButton: {
      styleOverrides: {
        root: {
          color: '#FE7000',
          border: '1px solid #FE7000',
          '&:hover': {
            border: '1px solid #FE7000',
          },
        },
      },
    },
    MuiPaper: {
      styleOverrides: {
        root: {
          '&.MuiPopover-paper': {
            boxShadow: 'rgba(0, 0, 0, 0.1) 0px 4px 12px;',
          },
        },
      },
    },
  },
});
