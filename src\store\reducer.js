import { createSlice } from '@reduxjs/toolkit';
import * as Actions from '../Redux/Constant';

export const initialState = {
  isLogin: false,
  userInfo: null,
  openSubscriptionModal: false,
  openSubscriptionModalDetails: null,
  planId: null,
  userSubscribedCourses: [],
  snackbarOpen: false,
  snackbarTitle: '',
  openWebInfoModal: false,
  noOfTimeOpenWebInfo: 2,
  acceptCookies: true,
  isGuiding: false,
  keyIntelligence: false,
  isBannerVisible: true,
  ExcelDetails: [],
  WordDetails: [],
  AccountDetails: [],
  CodeDetails: [],
  ErgDetails: [],
  RecommendedDetails: [],
  CertificateDetails: [],
  FreeCourseDetails:[],
  AllCourseDetails:[],
  CourseProgress:{},
  WordCourse: [],
  GetAssessmentNEETDetails:[],
  GetNEETAssessmentIndividual:[],
  CourseModuleDetails:[],
  SearchCourse:[],
  CategoryDetails:[],
  PowerPointDetails:[],
  BundleDetails:[],
  currency: 'USD',
  GetOfferDetails:[],
  GetLearnerDetails:[],
  GetAssessmentDetails:[],
  GetAssessmentConitiveDetails:[],
  GetAssessmentIndividual:[],
  GetRecomendedAssessment:[],
  GetAssessmentResult:[],
  comingfrom:"Courses",
  comingfromsub:"",
  pageName:"paid",
  SubscribedAssessment:[],
  FromGeneralAssessment:false,
  GetSatDetails:[],
  GetSatIndividualDetails:[],
  languagecode:'en',
  languagecodevalue:[],
  clientuserDetails:[],
  clientTableDetails:[],
  clientAnalytics:[],
  courseListB2b:[],
  SatListB2b:[],
  NeetListB2b:[],
  UserAnalytics:[]

};

export const counterSlice = createSlice({
  name: 'root',
  initialState,

  reducers: {
    loginSuccess: (state, action) => {
      state.isLogin = true;
      state.userInfo = action.payload;
    },

    logoutSuccess: (state) => {
      state.isLogin = false;
      state.userInfo = null;
      state.userSubscribedCourses = [];
      state.planId = null;
      state.noOfTimeOpenWebInfo = 2;
      state.isBannerVisible = true;
      state.courseListB2b =[];
      state.SatListB2b = [];
      state.NeetListB2b = [];
      
    },
    EmptyModule: (state) => {
     state.CourseModuleDetails=[]
    },
    EmptyUserDetails: (state) => {
      state.clientuserDetails = [];
     },
 


    

    openSubscriptionModal: (state, action) => {
      state.openSubscriptionModal = true;
      state.planId = action.payload;
    },

    closeSubscriptionModal: (state) => {
      state.openSubscriptionModal = false;
      state.planId = null;
      state.openSubscriptionModalDetails = null;
    },

    setSubscribedCourses: (state, action) => {
      state.userSubscribedCourses = action.payload;
    },

    setOpenSubscriptionModalDetails: (state, action) => {
      state.openSubscriptionModalDetails = action.payload;
    },

    openSnackbar: (state, action) => {
      state.snackbarTitle = action.payload;
      state.snackbarOpen = true;
    },

    handleOpenWebInfoModal: (state, action) => {
      state.openWebInfoModal = action.payload;
    },

    handleTimerWebInfoModal: (state, action) => {
      state.noOfTimeOpenWebInfo = action.payload;
    },

    closeSnackbar: (state) => {
      state.snackbarTitle = '';
      state.snackbarOpen = false;
    },

    closeCookiesAlert: (state) => {
      state.acceptCookies = false;
    },

    guideHandler: (state, action) => {
      state.isGuiding = action.payload;
    },

    setKeyIntelligence: (state, action) => {
      state.keyIntelligence = action.payload;
    },

    setBanner: (state, action) => {
      state.isBannerVisible = action.payload;
    },
    setCurrencyValue: (state, action) => {
      state.currency = action.payload;
    },
    ComingFrom: (state, action) => {
      state.comingfrom = action.payload;
      state.languagecode = action.payload === 'General Assessment'&& 'en'
    },
    ComingFromSub: (state, action) => {
      state.comingfromsub = action.payload;
    },

    
    setB2BPage: (state, action) => {
      state.pageName = action.payload;
    },
    FromGeneralAssessmentView:(state, action) => {
      state.FromGeneralAssessment = action.payload;
    },
    languagecodevalue:(state, action) => {
      state.languagecode = action.payload;
    },
    

  },

  // Move extraReducers here (outside reducers)
  extraReducers: (builder) => {
    builder
      .addCase(Actions.GetExcelDetailPending, (state) => { state.ExcelDetails = []})
      .addCase(Actions.GetExcelDetailSuccess, (state, action) => { state.ExcelDetails = action.payload})
      .addCase(Actions.GetExcelDetailFailure, (state) => { state.ExcelDetails = []})

      .addCase(Actions.GetWordDetailsPending, (state) => { state.WordDetails = []})
      .addCase(Actions.GetWordDetailsSuccess, (state, action) => { state.WordDetails = action.payload})
      .addCase(Actions.GetWordDetailsFailure, (state) => { state.WordDetails = []})

      .addCase(Actions.GetAccDetailsPending, (state) => { state.AccountDetails = []})
      .addCase(Actions.GetAccDetailsSuccess, (state, action) => {state.AccountDetails = action.payload;})
      .addCase(Actions.GetAccDetailsFailure, (state) => { state.AccountDetails = []})

      .addCase(Actions.GetCodeDetailsPending, (state) => { state.CodeDetails = []})
      .addCase(Actions.GetCodeDetailsSuccess, (state, action) => {state.CodeDetails = action.payload;})
      .addCase(Actions.GetCodeDetailsFailure, (state) => { state.CodeDetails = []})

      .addCase(Actions.GetRecomendedSuccess, (state, action) => {state.RecommendedDetails = action.payload;})
      
      
      .addCase(Actions.GetCertificateSuccess, (state, action) => {state.CertificateDetails = action.payload;})
  
      .addCase(Actions.GetFreeCoursePending, () => {})
      .addCase(Actions.GetFreeCourseSuccess, (state, action) => {state.FreeCourseDetails = action.payload;})
      .addCase(Actions.GetFreeCourseFailure, (state) => { state.FreeCourseDetails = []})

      .addCase(Actions.GetAllCourseSuccess, (state, action) => {state.AllCourseDetails = action.payload;})

      .addCase(Actions.GetProgressSuccess, (state, action) => {state.CourseProgress = action.payload;})

      .addCase(Actions.GetCourseSuccess, (state, action) => {state.CourseModuleDetails = action.payload;})


      .addCase(Actions.GetSearchCourseSuccess, (state, action) => {state.SearchCourse = action.payload;})

      .addCase(Actions.GetCategorySuccess, (state, action) => {state.CategoryDetails = action.payload;})
      .addCase(Actions.GetPowerPointSuccess, (state, action) => {state.PowerPointDetails = action.payload;})

      
      .addCase(Actions.GetERGSuccess, (state, action) => {state.ErgDetails = action.payload;})
      .addCase(Actions.GetWordSucces, (state, action) => {state.WordCourse = action.payload;})

      .addCase(Actions.GetBundleSucces, (state, action) => {state.BundleDetails = action.payload;})
      
      .addCase(Actions.GetOfferSucces, (state, action) => {state.GetOfferDetails = action.payload;})

      .addCase(Actions.GetLearnerSuccess, (state, action) => {state.GetLearnerDetails = action.payload;})
      
      .addCase(Actions.getSubscribedAssessmentSuccess, (state, action) => {state.SubscribedAssessment = action.payload;})

      .addCase(Actions.GetAssessmentSuccess, (state, action) => {state.GetAssessmentDetails = action.payload;})
      .addCase(Actions.GetAssessmentCognitiveSuccess, (state, action) => {state.GetAssessmentConitiveDetails = action.payload;})
      .addCase(Actions.GetAssessmentIndividualSuccess, (state, action) => {state.GetAssessmentIndividual = action.payload;})
      .addCase(Actions.GetRecomendedAssessmentSuccess, (state, action) => {state.GetRecomendedAssessment = action.payload;})
      .addCase(Actions.GetAssessmentResultSuccess, (state, action) => {state.GetAssessmentResult = action.payload;})
      .addCase(Actions.getSatSuccess, (state, action) => {state.GetSatDetails = action.payload;})
      .addCase(Actions.getIndividualSatSuccess, (state, action) => {state.GetSatIndividualDetails = action.payload;})

      .addCase(Actions.getLanguageSuccess, (state, action) => {state.languagecodevalue = action.payload;})
      .addCase(Actions.getClientUserSuccess, (state, action) => {state.clientuserDetails = action.payload;})
      .addCase(Actions.getClientTableSuccess, (state, action) => {state.clientTableDetails = action.payload;})
      .addCase(Actions.getClientAnalyticsSuccess, (state, action) => {state.clientAnalytics = action.payload;})
      .addCase(Actions.getB2BCourseSuccess, (state, action) => {state.courseListB2b = action.payload;})
      .addCase(Actions.getB2BSatSuccess, (state, action) => {state.SatListB2b = action.payload;})
      .addCase(Actions.getB2BNeetSuccess, (state, action) => {state.NeetListB2b = action.payload;})


      .addCase(Actions.GetAssessmentNEETSuccess, (state, action) => {state.GetAssessmentNEETDetails = action.payload;})
      .addCase(Actions.GetNEETAssessmentIndividualSuccess, (state, action) => {state.GetNEETAssessmentIndividual = action.payload;})

      .addCase(Actions.getAnalyticsSuccess, (state, action) => {state.UserAnalytics = action.payload;})
    },
});

export const {
  loginSuccess,
  logoutSuccess,
  EmptyModule,
  EmptyUserDetails,
  openSubscriptionModal,
  closeSubscriptionModal,
  setSubscribedCourses,
  setOpenSubscriptionModalDetails,
  closeSnackbar,
  openSnackbar,
  handleOpenWebInfoModal,
  handleTimerWebInfoModal,
  closeCookiesAlert,
  guideHandler,
  setKeyIntelligence,
  setBanner,
  setCurrencyValue,
  ComingFrom,
  ComingFromSub,
  setB2BPage,
  FromGeneralAssessmentView,
  languagecodevalue
} = counterSlice.actions;

export default counterSlice.reducer;