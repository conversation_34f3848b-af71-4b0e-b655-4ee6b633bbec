import * as Actions from './Constant'
import courseApi from '../services/users/courseApi'
import iqServices from '../services/iqTest/index';
import adminServices from '../services/adminServices';
import userServices from '../services/clientAdmin/userServices'
import analytics from '../services/analytics/analytics';


export const getExcelDetails = (cardViewSubMenu,language) => async(dispatch) => {
    dispatch({type:Actions.GetExcelDetailPending})
    await courseApi.getExcelDetails(cardViewSubMenu,language)
    .then((response)=>{        
    dispatch({ type: Actions.GetExcelDetailSuccess, payload: response.data })

    })
    .catch((error) => {
        console.error(error);
        dispatch({ type: Actions.GetExcelDetailFailure, payload: [] })
    })
}



export const getOfficeDetails = (cardViewSubMenu,language) => async(dispatch) => {
    dispatch({type:Actions.GetWordDetailsPending})
    await courseApi.getOfficeDetails(cardViewSubMenu,language)
    .then((response)=>{
    dispatch({ type: Actions.GetWordDetailsSuccess, payload: response.data })

    })
    .catch((error) => {
        console.error(error);
        dispatch({ type: Actions.GetWordDetailsFailure, payload: [] })
    })
}

export const getAccFinDetails = (cardViewSubMenu,language) => async(dispatch) => {
    dispatch({type:Actions.GetAccDetailsPending})
    await courseApi.getAccFinDetails(cardViewSubMenu,language)
    .then((response)=>{
    dispatch({ type: Actions.GetAccDetailsSuccess, payload: response.data })

    })
    .catch((error) => {
        console.error(error);
        dispatch({ type: Actions.GetAccDetailsFailure, payload: [] })
    })
}

export const getCodingDetails = (cardViewSubMenu,language) => async(dispatch) => {
    dispatch({type:Actions.GetCodeDetailsPending})
    await courseApi.getCodingDetails(cardViewSubMenu,language)
    .then((response)=>{
    dispatch({ type: Actions.GetCodeDetailsSuccess, payload: response.data })

    })
    .catch((error) => {
        console.error(error);
        dispatch({ type: Actions.GetCodeDetailsFailure, payload: [] })
    })
}


export const getPowerPointDetails = (cardViewSubMenu,language) => async(dispatch) => {
    dispatch({type:Actions.GetPowerPointPending})
    await courseApi.gePowerPointDetails(cardViewSubMenu,language)
    .then((response)=>{
    dispatch({ type: Actions.GetPowerPointSuccess, payload: response.data })

    })
    .catch((error) => {
        console.error(error);
        dispatch({ type: Actions.GetPowerPointFailure, payload: [] })
    })
}


export const getErgDetails = (language) => async(dispatch) => {
    dispatch({type:Actions.GetERGPending})
    await courseApi.getErgCourseDetails(language)
    .then((response)=>{
    dispatch({ type: Actions.GetERGSuccess, payload: response.data })

    })
    .catch((error) => {
        console.error(error);
        dispatch({ type: Actions.GetERGFailure, payload: [] })
    })
}


export const getRecomdedCourseList = (id,language) => async(dispatch) => {
    dispatch({type:Actions.GetRecomendedPending})
    await courseApi.getRecomdedCourseList(id,language)
    .then((response)=>{
    dispatch({ type: Actions.GetRecomendedSuccess, payload: response.data })

    })
    .catch((error) => {
        console.error(error);
        dispatch({ type: Actions.GetRecomendedFailure, payload: [] })
    })
}

export const getCertificateList = (language) => async(dispatch) => {
    dispatch({type:Actions.GetCertificatePending})
    await courseApi.getCertificateList(language)
    .then((response)=>{
    dispatch({ type: Actions.GetCertificateSuccess, payload: response.data })

    })
    .catch((error) => {
        console.error(error);
        dispatch({ type: Actions.GetCertificateFailure, payload: [] })
    })
}

export const getAllFreeCourses = (language) => async(dispatch) => {
    dispatch({type:Actions.GetFreeCoursePending})
    await courseApi.getFreeCourseList(language)
    .then((response)=>{
    dispatch({ type: Actions.GetFreeCourseSuccess, payload: response.data })

    })
    .catch((error) => {
        console.error(error);
        dispatch({ type: Actions.GetFreeCourseFailure, payload: [] })
    })
}


export const getAllcourseDetails = () => async(dispatch) => {
    dispatch({type:Actions.GetAllCoursePending})
    await courseApi.getCourseList()
    .then((response)=>{
    dispatch({ type: Actions.GetAllCourseSuccess, payload: response.data })

    })
    .catch((error) => {
        console.error(error);
        dispatch({ type: Actions.GetAllCourseFailure, payload: [] })
    })
}

export const getCourseProgress = (id) => async(dispatch) => {
    dispatch({type:Actions.GetProgressPending})
    await iqServices.getIqAndCourseProgress(id)
    .then((response)=>{
    dispatch({ type: Actions.GetProgressSuccess, payload: response.data })

    })
    .catch((error) => {
        console.error(error);
        dispatch({ type: Actions.GetProgressFailure, payload: [] })
    })
}

export const getCourseDetails1 = (id,subscriptionPlanId,language) => async(dispatch) => {
    dispatch({type:Actions.GetCoursePending})
    await courseApi.getCourseModulesAndSubmodules(id,subscriptionPlanId,language)
    .then((response)=>{
        
    dispatch({ type: Actions.GetCourseSuccess, payload: response.data })

    })
    .catch((error) => {
        console.error(error);
        dispatch({ type: Actions.GetCourseFailure, payload: [] })
    })
}

export const getSearchedCourse = (searchQuery) => async(dispatch) => {
    dispatch({type:Actions.GetSearchCoursePending})
    await adminServices.getSerchedCourse(searchQuery)
    .then((response)=>{
    dispatch({ type: Actions.GetSearchCourseSuccess, payload: response.data })

    })
    .catch((error) => {
        console.error(error);
        dispatch({ type: Actions.GetSearchCourseFailure, payload: [] })
    })
}


export const getCategoryDetails = () => async (dispatch) => {
    dispatch({ type: Actions.GetCategoryPending })
    await adminServices.getBasicDetails()
        .then((response) => {
            dispatch({ type: Actions.GetCategorySuccess, payload: response.data })
 
        })
        .catch((error) => {
            console.error(error);
            dispatch({ type: Actions.GetCategoryFailure, payload: [] })
        })
}


export const getWordCourse = (data,language) => async(dispatch) => {
    dispatch({type:Actions.GetWordPending})
    await courseApi.getWordCours(data,language)
    .then((response)=>{
    dispatch({ type: Actions.GetWordSucces, payload: response.data })

    })
    .catch((error) => {
        console.error(error);
        dispatch({ type: Actions.GetWordFailure, payload: [] })
    })
}


export const getBundleCourses1 = (data,language) => async(dispatch) => {
    dispatch({type:Actions.GetBundlePending})
    await courseApi.getWebsiteBundleCourses(data,language)
    .then((response)=>{
    dispatch({ type: Actions.GetBundleSucces, payload: response.data })

    })
    .catch((error) => {
        console.error(error);
        dispatch({ type: Actions.GetBundleFailure, payload: [] })
    })
}

export const getOfferbaner = () => async(dispatch) => {
    dispatch({type:Actions.GetOfferPending})
    await adminServices.getBannerDetails()
    .then((response)=>{
    dispatch({ type: Actions.GetOfferSucces, payload: response.data })

    })
    .catch((error) => {
        console.error(error);
        dispatch({ type: Actions.GetOfferFailure, payload: [] })
    })
}

export const getLernerView = (language) => async(dispatch) => {
    dispatch({type:Actions.GetLearnerPending})
    await adminServices.getLearnerViewing(language)
    .then((response)=>{
    dispatch({ type: Actions.GetLearnerSuccess, payload: response.data })

    })
    .catch((error) => {
        console.error(error);
        dispatch({ type: Actions.GetLearnerFailure, payload: [] })
    })
}


export const getAssessmentList = (id) => async(dispatch) => {
    dispatch({type:Actions.GetAssessmentPending})
    await courseApi.getSatDashboard(id)
    .then((response)=>{
    dispatch({ type: Actions.GetAssessmentSuccess, payload: response.data })

    })
    .catch((error) => {
        console.error(error);
        dispatch({ type: Actions.GetAssessmentFailure, payload: [] })
    })
}


export const getCognitiveAssessment= (id) => async(dispatch) => {
    dispatch({type:Actions.GetAssessmentCognitivePending})
    await courseApi.getCognitiveAssessment(id)
    .then((response)=>{
    dispatch({ type: Actions.GetAssessmentCognitiveSuccess, payload: response.data })

    })
    .catch((error) => {
        console.error(error);
        dispatch({ type: Actions.GetAssessmentCognitiveFailure, payload: [] })
    })
}

export const getNEETAssessment= (id) => async(dispatch) => {
    dispatch({type:Actions.GetAssessmentNEETPending})
    await courseApi.getNEETAssessment(id)
    .then((response)=>{
    dispatch({ type: Actions.GetAssessmentNEETSuccess, payload: response.data })

    })
    .catch((error) => {
        console.error(error);
        dispatch({ type: Actions.GetAssessmentNEETFailure, payload: [] })
    })
}


export const getSubscribedAssessment= (id) => async(dispatch) => {
    dispatch({type:Actions.getSubscribedAssessmentPending})
    await courseApi.getSubscribedAssessment(id)
    .then((response)=>{
    dispatch({ type: Actions.getSubscribedAssessmentSuccess, payload: response.data })
    })
    .catch((error) => {
        console.error(error);
        dispatch({ type: Actions.getSubscribedAssessmentFailure, payload: [] })
    })
}

export const getIndividualAssessment= (id,userid) => async(dispatch) => {
    dispatch({type:Actions.GetAssessmentIndividualPending})
    await courseApi.getIndividualAssessment(id,userid)
    .then((response)=>{
    dispatch({ type: Actions.GetAssessmentIndividualSuccess, payload: response.data })

    })
    .catch((error) => {
        console.error(error);
        dispatch({ type: Actions.GetAssessmentIndividualFailure, payload: [] })
    })
}

export const getIndividualNEETAssessment= (id,userId) => async(dispatch) => {
    dispatch({type:Actions.GetNEETAssessmentIndividualPending})
    await courseApi.getIndividualNEETAssessment(id,userId)
    .then((response)=>{
    dispatch({ type: Actions.GetNEETAssessmentIndividualSuccess, payload: response.data })
    })
    .catch((error) => {
        console.error(error);
        dispatch({ type: Actions.GetAssessmentIndividualFailure, payload: [] })
    })
}


export const getRecommendedAssessment= (userid) => async(dispatch) => {
    dispatch({type:Actions.GetRecomendedAssessmentPending})
    await courseApi.getRecommendedAssessment(userid)
    .then((response)=>{
    dispatch({ type: Actions.GetRecomendedAssessmentSuccess, payload: response.data })

    })
    .catch((error) => {
        console.error(error);
        dispatch({ type: Actions.GetRecomendedAssessmentFailure, payload: [] })
    })
}



export const getIndividualResult= (userid,assessmentId) => async(dispatch) => {
    dispatch({type:Actions.GetAssessmentResultPending})
    await courseApi.getIndividualResult(userid,assessmentId)
    .then((response)=>{
    dispatch({ type: Actions.GetAssessmentResultSuccess, payload: response.data })

    })
    .catch((error) => {
        console.error(error);
        dispatch({ type: Actions.GetAssessmentResultFailure, payload: [] })
    })
}


export const getSatDetails= (userid) => async(dispatch) => {
    dispatch({type:Actions.getSatPending})
    await courseApi.getSatDashboard(userid)
    .then((response)=>{        
    dispatch({ type: Actions.getSatSuccess, payload: response.data })

    })
    .catch((error) => {
        console.error(error);
        dispatch({ type: Actions.getSatFailure, payload: [] })
    })
}

export const getIndividualResultForSat= (userid,assessmentId) => async(dispatch) => {
    dispatch({type:Actions.getIndividualSatPending})
    await courseApi.getIndividualResultForSat(userid,assessmentId)
    .then((response)=>{
    dispatch({ type: Actions.getIndividualSatSuccess, payload: response.data })

    })
    .catch((error) => {
        console.error(error);
        dispatch({ type: Actions.getIndividualSatFailure, payload: [] })
    })
}



export const getLanguageCode= () => async(dispatch) => {
    dispatch({type:Actions.getLanguagePending})
    await adminServices.getLanguageCode()
    .then((response)=>{
    dispatch({ type: Actions.getLanguageSuccess, payload: response.data })

    })
    .catch((error) => {
        console.error(error);
        dispatch({ type: Actions.getLanguageFailure, payload: [] })
    })
}


export const getIndividualClientUser = (userId) => async(dispatch) => {
    dispatch({type:Actions.getClientUserPending})
    await userServices.getUserDetalsByIdIndividual(userId)
    .then((response)=>{
    dispatch({ type: Actions.getClientUserSuccess, payload: response.data })

    })
    .catch((error) => {
        console.error(error);
        dispatch({ type: Actions.getClientUserFailure, payload: [] })
    })
}


export const getIndividualClientTable = (page, rowsPerPage, search) => async(dispatch) => {
    dispatch({type:Actions.getClientTablePending})
    await userServices.getUsersDetails(page, rowsPerPage, search)
    .then((response)=>{
    dispatch({ type: Actions.getClientTableSuccess, payload: response.data })

    })
    .catch((error) => {
        console.error(error);
        dispatch({ type: Actions.getClientTableFailure, payload: [] })
    })
}

export const getAnalyticsView = (courseId) => async(dispatch) => {
    dispatch({type:Actions.getClientAnalyticsPending})
    await analytics.getAnalytics(courseId)
    .then((response)=>{
    dispatch({ type: Actions.getClientAnalyticsSuccess, payload: response.data })

    })
    .catch((error) => {
        console.error(error);
        dispatch({ type: Actions.getClientAnalyticsFailure, payload: [] })
    })
}


export const getB2BCourseList = (id,language) => async(dispatch) => {
    dispatch({type:Actions.getB2BCoursesPending})
    await courseApi.getCourseList1(id,language)
    .then((response)=>{
    dispatch({ type: Actions.getB2BCourseSuccess, payload: response.data })

    })
    .catch((error) => {
        console.error(error);
        dispatch({ type: Actions.getB2BCourseFailure, payload: [] })
    })
}

export const getB2BSAT = (id) => async(dispatch) => {
    dispatch({type:Actions.getB2BSatsPending})
    await courseApi.getSATList(id)
    .then((response)=>{
    dispatch({ type: Actions.getB2BSatSuccess, payload: response.data })

    })
    .catch((error) => {
        console.error(error);
        dispatch({ type: Actions.getB2BSatFailure, payload: [] })
    })
}


export const getB2BNeet = () => async(dispatch) => {
    dispatch({type:Actions.getB2BNeetsPending})
    await courseApi.getNEETAssessment(361)
    .then((response)=>{
    dispatch({ type: Actions.getB2BNeetSuccess, payload: response.data })

    })
    .catch((error) => {
        console.error(error);
        dispatch({ type: Actions.getB2BNeetFailure, payload: [] })
    })
}


export const getactiveUsersAnalytics = (courseId) => async(dispatch) => {
    dispatch({type:Actions.getAnalyticsPending})
    await analytics.activeUsers(courseId)
    .then((response)=>{
    dispatch({ type: Actions.getAnalyticsSuccess, payload: response.data })

    })
    .catch((error) => {
        console.error(error);
        dispatch({ type: Actions.getAnalyticsFailure, payload: [] })
    })
}

