
import * as React from 'react';
import AppBar from '@mui/material/AppBar';
import Box from '@mui/material/Box';
import CssBaseline from '@mui/material/CssBaseline';
import List from '@mui/material/List';
import { useNavigate } from 'react-router-dom';
import ContactEmergencyIcon from '@mui/icons-material/ContactEmergency';
import CastForEducationIcon from '@mui/icons-material/CastForEducation';
import ListItemButton from '@mui/material/ListItemButton';
import ListItemIcon from '@mui/material/ListItemIcon';
import ListItemText from '@mui/material/ListItemText';
import ExpandLess from '@mui/icons-material/ExpandLess';
import ExpandMore from '@mui/icons-material/ExpandMore';
import Toolbar from '@mui/material/Toolbar';

const drawerWidth = 240;

export default function Sidebar({ onMenuChange, onSubMenuChange }) {
  const navigate = useNavigate();
  const [openNewCourses, setOpenNewCourses] = React.useState(false);
  const [selectedMenu, setSelectedMenu] = React.useState('Courses');
  const [selectedSubMenu, setSelectedSubMenu] = React.useState('AllCourses');

  const handleCoursesClick = () => {
    setOpenNewCourses(!openNewCourses);
    setSelectedMenu('Courses');
    onMenuChange('Courses');
    onSubMenuChange('AllCourses');
  };

  const handleMyCourseClick = () => {
    setOpenNewCourses(false);
    setSelectedMenu('MyLearning');
    onMenuChange('MyLearning');
  };

  const handleSubMenuClick = (subMenu) => {
    setSelectedSubMenu(subMenu);
    onSubMenuChange(subMenu);
  };

  const sidebarContent = (
    <div>
      <Toolbar />
      <Box style={{ marginTop: 30 }} />
      <List>
     
        <ListItemButton
          onClick={handleCoursesClick}
          selected={selectedMenu === 'Courses'}
          sx={{
            backgroundColor: selectedMenu === 'Courses' ? '#f0f0f0' : 'inherit',
            '&:hover': { backgroundColor: '#e0e0e0' },
          }}
        >
          <ListItemIcon>
            <ContactEmergencyIcon />
          </ListItemIcon>
          <ListItemText primary="Courses" />
          {openNewCourses ? <ExpandLess /> : <ExpandMore />}
        </ListItemButton>

        {openNewCourses && (
          <List component="div" disablePadding>
            <ListItemButton
              onClick={() => handleSubMenuClick('AllCourses')}
              selected={selectedSubMenu === 'AllCourses'}
              sx={{
                pl: 4,
                backgroundColor: selectedSubMenu === 'AllCourses' ? '#e0e0e0' : 'inherit',
                '&:hover': { backgroundColor: '#d0d0d0' },
              }}
            >
              <ListItemText primary="All Courses" />
            </ListItemButton>
            <ListItemButton
              onClick={() => handleSubMenuClick('FreeCourses')}
              selected={selectedSubMenu === 'FreeCourses'}
              sx={{
                pl: 4,
                backgroundColor: selectedSubMenu === 'FreeCourses' ? '#e0e0e0' : 'inherit',
                '&:hover': { backgroundColor: '#d0d0d0' },
              }}
            >
              <ListItemText primary="Free Courses" />
            </ListItemButton>
            <ListItemButton
              onClick={() => handleSubMenuClick('CertificationCourses')}
              selected={selectedSubMenu === 'CertificationCourses'}
              sx={{
                pl: 4,
                backgroundColor: selectedSubMenu === 'CertificationCourses' ? '#e0e0e0' : 'inherit',
                '&:hover': { backgroundColor: '#d0d0d0' },
              }}
            >
              <ListItemText primary="Certification Courses" />
            </ListItemButton>

            
          </List>
        )}
        <ListItemButton
          onClick={handleMyCourseClick}
          selected={selectedMenu === 'MyLearning'}
          sx={{
            backgroundColor: selectedMenu === 'MyLearning' ? '#f0f0f0' : 'inherit',
            '&:hover': { backgroundColor: '#e0e0e0' },
          }}
        >
          <ListItemIcon>
            <CastForEducationIcon />
          </ListItemIcon>
          <ListItemText primary="My Learning" />
        </ListItemButton>
      
      </List>
    </div>
  );

  return (
    <Box sx={{ display: 'flex' }}>
      <CssBaseline />
      <Box
        component="aside"
        sx={{
          width: { sm: drawerWidth },
          flexShrink: { sm: 0 },
          display: { xs: 'none', sm: 'block' },
          bgcolor: 'background.paper',
          padding: 2,
          border: '1px solid #ddd', // Add border here
          borderColor: 'grey.300',   // Optional: sets a soft grey color
          borderRadius: 2,           // Optional: rounds the corners slightly
          borderRight: '11px solid #f5f5f5'
        }}
      >
        {sidebarContent}
      </Box>
    </Box>
  );
}