<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8" />
  <meta name="google-signin-client_id" content="974287529513-82gvs1qmv45bfrss69i15svoqdso37mg.apps.googleusercontent.com"></meta>
  <link rel="canonical" href="https://www.keyskillset.com/" />
  <meta property="og:locale" content="en_US" />
  <meta property="og:type" content="website" />
  <meta property="og:title" content="Home - keySkillset" />
  <meta property="og:description"
    content="Simulation based Learning environment & Assessments. Master Python, SQL, Financial Modelling, MS Office, and other certification courses on our award-winning platform." />
  <meta property="og:url" content="https://keyskillset.com/" />
  <meta property="og:site_name" content="keySkillset" />
  <meta property="article:modified_time" content="2022-08-19T14:51:29+00:00" />
  <meta property="og:image"
    content="https://keyskillset.com/wp-content/uploads/2019/10/Снимок-экрана-2019-10-18-в-12.33-1.png" />
  <meta property="og:image:width" content="879" />
  <meta property="og:image:height" content="978" />
  <meta property="og:image:type" content="image/png" />
  <meta name="twitter:card" content="summary_large_image" />
  <meta name="twitter:label1" content="Est. reading time" />
  <meta name="twitter:data1" content="10 minutes" />

  <!-- Favicon -->
  <link rel="apple-touch-icon" sizes="180x180" href="%PUBLIC_URL%/favicon/apple-touch-icon.png">
  <link rel="icon" type="image/png" sizes="32x32" href="%PUBLIC_URL%/favicon/favicon-32x32.png">
  <link rel="icon" type="image/png" sizes="180x180" href="%PUBLIC_URL%/favicon/favicon-180x180.png">

  <meta name="theme-color" content="#000000" />
  <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0, shrink-to-fit=no" />

  <!-- Using Google Font -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link
    href="https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700&family=Poppins:ital,wght@0,100;0,200;0,300;0,400;1,100;1,200;1,300&display=swap"
    rel="stylesheet">

  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">

  <link
    href="https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:ital,wght@0,200;0,300;0,400;0,500;0,600;0,700;1,200;1,300;1,400;1,500;1,600;1,700;1,800&display=swap"
    rel="stylesheet">
  <!-- Using Local Font -->
  <link rel="stylesheet" type="text/css" href="%PUBLIC_URL%/fonts/index.css" />
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script type="text/javascript" async
    src="https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.7/MathJax.js?config=TeX-MML-AM_CHTML">
  </script>
  <title>keySkillSet</title>
</head>


<body>
  <noscript>You need to enable JavaScript to run this app.</noscript>
  <div id="root"></div>
</body>

</html>
<style>
  input::-ms-reveal,
  input::-ms-clear {
    display: none;
  }
</style>