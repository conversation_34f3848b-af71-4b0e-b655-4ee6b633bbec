/* eslint-disable no-unused-vars */
/* eslint-disable react-hooks/exhaustive-deps */
import React, { useState, useEffect } from "react";
import {
    Grid, Button, IconButton, TextField, Select, MenuItem,
    InputAdornment, Table, TableBody, TableCell, TableContainer, Container, FormControl, InputLabel,
    TableHead, TableRow, Paper, TablePagination, Switch, Tooltip, DialogActions, DialogContent, DialogTitle, Dialog, Tabs, Tab,
} from '@mui/material';

import DOMPurify from 'dompurify';
import { useTranslation } from 'react-i18next';
import moment from 'moment'
import { useNavigate, useLocation } from 'react-router-dom';
import EditIcon from '@mui/icons-material/Edit';
import BookmarkIcon from '@mui/icons-material/Bookmark';
import DeleteIcon from '@mui/icons-material/Delete';
import SearchIcon from '@mui/icons-material/Search';
import Page from '../../../components/Page'
import PageHeader from '../../../components/PageHeader';
import DeleteAlert from '../../../components/modal/DeleteModal';
import SnackBar from '../../../components/snackbar/snackbar';
import adminServices from '../../../services/adminServices';
import CreateMcqQuestionModel from "./CreateMcqQuestionModel";
import EditQuestionModule from "./EditMcqQuestionsModule";



const NEET = () => {
    const [page, setPage] = useState(0);
    const [rowsPerPage, setRowsPerPage] = useState(10);
    const [pageAssessment, setPageAssessment] = useState(0);
    const [rowsPerPageAssessment, setRowsPerPageAssessment] = useState(10);
    const [pageQuesBank, setPageQuesBank] = useState(0);
    const [rowsPerPageQuesBank, setRowsPerPageQuesBank] = useState(10);

    const [openCreateMcq, setOpenCreateMcq] = useState(false);
    const [openEditMcq, setOpenEditMcq] = useState(false);
    const [snackbarTitle, setSnackbarTitle] = useState('');
    const [openSnackbar, setOpenSnackbar] = useState(false);
    const [searchedDetails, setSearchedDetails] = useState('');
    const [satDetails, setSatDetails] = useState([]);
    const [deleteAlert, setDeleteAlert] = useState(false);
    const [deleteModalTitle, setDeleteModalTitle] = useState('');
    const [deleteId, setDeleteId] = useState('')
    const [deleteType, setDeleteType] = useState('')
    const [totalCount, setTotalCount] = useState('')
    const [totalCountNeet, setTotalCountNeet] = useState('')

    const [openUserMessage, setOpenUserMessage] = useState(false)
    const [userMessageData, setUserMessageData] = useState('')
    const [userMessageFromDateAndTime, setUserMessageFromDateAndTime] = useState('')
    const [userMessageToDateAndTime, setUserMessageToDateAndTime] = useState('')
    const [currentToggleId, setCurrentToggleId] = useState('')
    const [currentToggleValue, setCurrentToggleValue] = useState('')
    const location = useLocation();
    const queryParams = new URLSearchParams(location.search);
    const tabFromQuery = queryParams.get('tab');
    const [tabValue, setTabValue] = useState(location.state?.comingfrom ? location.state?.comingfrom : 0);
    const [NeetAssessmentData, setNeetAssessmentData] = useState([]);
    const [loading, setLoading] = useState(false);
    const [questionList, setQuestionList] = useState([]);
    const [maxCount, setMaxCount] = useState(0);
    const [searchText, setSearchText] = useState('');
    const [keyObjects, setKeyObjects] = useState([]);
    const [newData, setNewData] = React.useState('');
    const [deleteOpen, setDeleteOpen] = React.useState(false);
    const [questionType, setQuestionType] = useState('Chemistry');

    const handleTypeChange = (event) => {
        setQuestionType(event.target.value);
        setPageQuesBank(0);
    };

    const handleCreateMcqQuestion = () => {
        setOpenCreateMcq(true)
    };


    const handleChange = (event, newValue) => {
        setTabValue(newValue);
        setDeleteOpen(false);
        setDeleteAlert(false);
    };
    const { t } = useTranslation('translation');
    const navigate = useNavigate();

    const handleChangePage = (event, newPage) => {
        setPage(newPage);
    };
    const handleChangePageAssess = (event, newPage) => {
        setPageAssessment(newPage);
    };
    const handleChangePageQuestion = (event, newPage) => {
        setPageQuesBank(newPage);
    };

    const handleChangeRowsPerPage = (event) => {
        setRowsPerPage(parseInt(event.target.value, 10));
        setPage(0);
    };
    const handleChangeRowsPerPageAssess = (event) => {
        setRowsPerPageAssessment(parseInt(event.target.value, 10));
        setPageAssessment(0);

    };
    const handleChangeRowsPerPageQuest = (event) => {
        setRowsPerPageQuesBank(parseInt(event.target.value, 10));
        setPageQuesBank(0);
    };

    useEffect(() => {
        if (tabValue === 0) {
            getSATDetails();
        }
    }, [page, rowsPerPage, searchedDetails, tabValue])


    useEffect(() => {
        if (tabValue === 1) {
            getNEETAssessmentDetails();
        }
    }, [tabValue, pageAssessment, rowsPerPageAssessment, searchedDetails]);

    useEffect(() => {
        if (tabValue === 2) {
            setQuestionList([]);
            getAllQuestions();
        }
    }, [tabValue, questionType, pageQuesBank, rowsPerPageQuesBank, searchText]);

    const getSATDetails = async () => {
        const result = await adminServices.getNEETAssessmentAdmin(searchedDetails, page, rowsPerPage);
        if (result.ok) {
            setSatDetails(result.data.assessments)
            setTotalCount(result.data.pagination)
        }

    }
    const getNEETAssessmentDetails = async () => {
        const result = await adminServices.getNEETAssessmentData(searchedDetails, pageAssessment, rowsPerPageAssessment);
        if (result.ok) {
            setNeetAssessmentData(result.data.data)
            setTotalCountNeet(result.data.pagination)
        }

    }


    const handleToggleButton = (id, value, type) => {
        if (value === false) {
            setOpenUserMessage(true);
            setCurrentToggleId(id);
            setCurrentToggleValue(value);
            return;
        }
        callUpdateStatusAPI(id, value);
    };

    const callUpdateStatusAPI = async (id, value, userData) => {
        try {
            const result = await adminServices.UpdateGeneralAssessmentStatus(id, value, userData, 'Status');
            if (result.ok) {
                setSnackbarTitle(t('NEET Assessment status updated successfully'));
                setOpenSnackbar(true);
                getSATDetails();
                getNEETAssessmentDetails();
            }
        } catch (error) {
            console.log(error, 'error');
        }
    };

    const formatDate = (dateString) => {
        const date = new Date(dateString);
        const day = date.getDate().toString().padStart(2, '0');
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const year = date.getFullYear();
        return `${day}/${month}/${year}`;
    };

    const handleSubmitUserMessage = () => {
        const userData = {
            message: userMessageData,
            fromDate: formatDate(userMessageFromDateAndTime),
            toDate: formatDate(userMessageToDateAndTime),
        };

        callUpdateStatusAPI(currentToggleId, currentToggleValue, userData);

        setOpenUserMessage(false);
        setUserMessageData('');
        setUserMessageFromDateAndTime('');
        setUserMessageToDateAndTime('');
    };


    const handleCreateNeetAssessment = () => {
        navigate("/app/ADDNEETAssessment")
    }


    const handleCreateNEET = () => {
        navigate("/app/ADDNEET")
    }
    const handleToggleButtonMaintenance = async (id, value, type) => {
        setNeetAssessmentData(prevRows => prevRows.map(row =>
            row.id === id ? { ...row, on_maintenance: value } : row
        ));

        try {
            const result = await adminServices.UpdateGeneralAssessmentMaintenance(id, value, type);
            if (result.ok) {
                setSnackbarTitle(t('NEET Assessment status updated successfully'));
                setOpenSnackbar(true);
                getSATDetails();
            } else {
                setNeetAssessmentData(prevRows => prevRows.map(row =>
                    row.id === id ? { ...row, on_maintenance: !value } : row
                ));
            }
        } catch (error) {
            setNeetAssessmentData(prevRows => prevRows.map(row =>
                row.id === id ? { ...row, on_maintenance: !value } : row
            ));
            console.log(error, 'error');
        }
    }

    const handleDelete = (data) => {
        setDeleteAlert(true);
        setDeleteId(data.id);
        setDeleteType(data?.question_type);
        setDeleteModalTitle('Are you sure you want delete this NEET Assessment?');
    }

    const handleEditAssessment = (data) => {
        navigate('/app/EditNEET', { state: data?.assessment?.id })
    }
    const handleEditNEETAssessment = (data) => {
        console.log(data, "datadata");

        navigate('/app/EditNEETAssessment', { state: data })
    }

    const handleDeleteNEET = async () => {
        try {
            const response = await adminServices.deleteNEETAssessment(deleteId);
            if (response.ok) {
                setSnackbarTitle('NEET Assessment deleted successfully');
                setOpenSnackbar(true);
                getSATDetails();
                getAllQuestions();
                getNEETAssessmentDetails();
                setDeleteAlert(false)
                setDeleteId('')
                setDeleteType('')
                setDeleteModalTitle("");
            }
        } catch (error) {
            console.log(error);
        }
    }
    const getAllQuestions = async () => {
        setLoading(true);
        const result = await adminServices.getAllNEETQuestion(rowsPerPageQuesBank, pageQuesBank, searchText, questionType);
        if (result.ok) {
            setMaxCount(result.data.total)
            const sortedQuestions = result.data.newQuestions.sort((a, b) =>
                new Date(b.created_date) - new Date(a.created_date)
            );
            setQuestionList(sortedQuestions);
            setLoading(false);
        }
        else
            setLoading(false);
    };

    const handleCreateQuestion = async (object, values) => {
        setLoading(true);
        try {
            const keysData = new FormData();
            keysData.append('question', values.question);
            keysData.append('explanation', values.explanation);
            keysData.append('level', values.level);
            keysData.append('questionType', values.questionType);
            if (values.selectedSkills) {
                keysData.append('cognitive_skills', values.selectedSkills);
            }
            if (values.points !== null) {
                keysData.append('points', values.points);
            }
            keysData.append('courseCategory', values.courseCategory ? values.courseCategory : null);
            keysData.append('Tag', JSON.stringify(values.keyword ? values.keyword : null));
            keysData.append('questionTag', JSON.stringify(values.studyMaterials || []));
            if (values.questionType === "IQ") {
                const updatedObject = {
                    ...object,
                    keyType: values.keyType
                };
                keysData.append('keyObj', JSON.stringify(updatedObject));
            }
            else if (values.questionType === "MCQ" || values.questionType === "screenLevelMcq" || values.questionType === 'Chemistry' || values.questionType === 'Physics' || values.questionType === 'Biology') {
                const mcqdata = {
                    question: values.question,
                    mcqOptions: values.mcqOptions.map(option => option.option),
                    correctAnswer: values.mcqOptions.map(option => option.isCorrect),
                };
                keysData.append('mcqData', JSON.stringify(mcqdata));
            }
            const response = await adminServices.createQuestions(keysData);
            if (response.ok) {
                const newQuestion = {
                    question_text: values.question,
                    question_type: values.questionType,
                    is_active: true,
                    created_date: new Date().toISOString(),
                    mcqOptions: values.mcqOptions,
                };
                setQuestionList(prevList => [newQuestion, ...prevList]);
                setOpenSnackbar(true);
                setSnackbarTitle(response.data.message);
                setOpenCreateMcq(false);
                setLoading(false);
                await getAllQuestions()

            } else {
                console.error("Error:", response);
                setLoading(false);
            }
        } catch (error) {
            console.error("An error occurred:", error);
        }
    };

    const handleSubmitForm = async (values) => {
        const array = keyObjects;
        const object = {
            windows: {
                keyCode: values.combinedWindowKeys,
                keyName: values.combinedWindowKeyName,
            },
            mac: {
                keyCode: values.combinedMacKeys,
                keyName: values.combinedMacKeyName,
            },
            positionIndex: array.length,
        };
        array.push(object);
        setKeyObjects(array);
        handleCreateQuestion(object, values)

    }
    const handleEditCallBack = (values) => {
        const array = keyObjects;
        const object = {
            windows: {
                keyCode: values.combinedWindowKeys,
                keyName: values.combinedWindowKeyName,
            },
            mac: {
                keyCode: values.combinedMacKeys,
                keyName: values.combinedMacKeyName,
            },
            positionIndex: array.length,
        };
        array.push(object);
        handleEditCallBack1(object, values);
    }
    const handleEditCallBack1 = async (object, values) => {
        setLoading(true)
        const mergedArray = values.mcqOptions?.map((option, index) => ({
            option,
            isCorrect: values.curectAnswer[index] || false,
        }));

        try {
            const requestData = {
                question: values.question,
                level: values.level,
                questionType: values.questionType,
                explanation: values.explanation,
                points: values.points !== null ? values.points : undefined,
                courseCategory: values.courseCategory || undefined,
                Tag: values.courseCategory ? JSON.stringify(values.keyword) : JSON.stringify(values.keyword),
                cognitive_skills: values.selectedSkills,
                questionTag: JSON.stringify(values.studyMaterials || [])

            };
            if (values.selectedSkills) {
                requestData.cognitive_skills = values.selectedSkills
            }
            if (values.questionType === "IQ") {
                const updatedObject = {
                    ...object,
                    keyType: values.keyType,
                };
                requestData.keyObj = JSON.stringify(updatedObject);
            }
            else if (values.questionType === "MCQ" || values.questionType === "screenLevelMcq" || values.questionType === 'Neet' || values.questionType === 'Chemistry' || values.questionType === 'Physics' || values.questionType === 'Biology') {
                requestData.mcqData = {
                    question: values.question,
                    mcqOptions: mergedArray.map(option => option.option),
                    correctAnswer: mergedArray.map(option => option.isCorrect),
                };
            }
            const questionId = newData;
            const response = await adminServices.updateQuestion(questionId, requestData);
            if (response.ok) {
                await getAllQuestions();
                setOpenSnackbar(true);
                setSnackbarTitle(response.data.message);
                setOpenEditMcq(false);
                setLoading(false);

            } else {
                console.error("Error:", response);
                setLoading(false);
            }
        } catch (error) {
            console.error("An error occurred:", error);
        }
    };
    const handleInputChange = (event) => {
        const newSearch = event.target.value;
        setSearchText(newSearch);
    };
    const handleEdit = (values) => {
        setNewData(values)
        setOpenEditMcq(true)
    }


    const handleDeleteNeetQuestion = async () => {
        try {
            const response = await adminServices.deleteSATDetails(deleteId, deleteType);
            setDeleteOpen(false)
            if (response.ok) {

                if (response.data.message === 'Only created user can delete the Questions') {
                    setSnackbarTitle('Only created user can delete the Questions');
                    setOpenSnackbar(true)
                } else {
                    setSnackbarTitle('NEET Question deleted successfully');
                    await getAllQuestions();
                    setDeleteOpen(false);
                    setOpenSnackbar(true);
                    setDeleteId('');
                    setDeleteType('');
                    setDeleteModalTitle("");
                }
            }
        } catch (error) {
            console.log(error);
        }
    }

    const handleDeletOpen = (data) => {
        setDeleteOpen(true);
        setDeleteId(data.id)
    }
    return (
        <Page title="NEET Assessment">
            <PageHeader pageTitle="NEET Assessment" submodule="submodule" />
            <>
                <Tabs value={tabValue} onChange={handleChange} aria-label="NEET tabs">
                    <Tab label="Sources" />
                    <Tab label="Assessments" />
                    <Tab label="Question Bank" />
                </Tabs>
                {tabValue === 0 && <Grid container spacing={2}>
                    <Grid align="end" item xs={12}>
                        <TextField
                            variant="outlined"
                            placeholder="Search Name"
                            id="searchassessment"
                            size="small"
                            sx={{ width: '250px', height: '40px' }}
                            value={searchedDetails}
                            onChange={(e) => setSearchedDetails(e.target.value)}
                            InputProps={{
                                startAdornment: (
                                    <InputAdornment position="start">
                                        <SearchIcon id="searchAssessmentIconbtn" />
                                    </InputAdornment>
                                ),
                                sx: { height: '100%' },
                            }}
                        />
                        <Button
                            style={{ marginLeft: '15px', height: '40px' }}
                            id="createassessmentButtongeneral"
                            variant="contained"
                            color="primary"
                            onClick={handleCreateNEET}
                        >
                            Create NEET
                        </Button>
                    </Grid>
                    <TableContainer component={Paper} sx={{ marginTop: 2 }}>
                        <Table sx={{ marginLeft: '15px', width: '98%' }}>
                            <TableHead>
                                <TableRow>
                                    {/* <TableCell sx={{ fontWeight: 'bold', backgroundColor: '#f5f5f5', paddingLeft: '8px !important' }}>Name</TableCell> */}
                                    <TableCell sx={{ fontWeight: 'bold', backgroundColor: '#f5f5f5' }}>Name</TableCell>

                                    {/* <TableCell sx={{ fontWeight: 'bold', backgroundColor: '#f5f5f5' }}>Live</TableCell> */}
                                    {/* <TableCell sx={{ fontWeight: 'bold', backgroundColor: '#f5f5f5' }}>Questions</TableCell> */}
                                    <TableCell sx={{ fontWeight: 'bold', backgroundColor: '#f5f5f5' }}>Level</TableCell>
                                    <TableCell sx={{ fontWeight: 'bold', backgroundColor: '#f5f5f5' }}>Status</TableCell>
                                    <TableCell sx={{ fontWeight: 'bold', backgroundColor: '#f5f5f5' }}>Maintenance</TableCell>
                                    <TableCell sx={{ fontWeight: 'bold', backgroundColor: '#f5f5f5' }}>Author</TableCell>
                                    <TableCell sx={{ fontWeight: 'bold', backgroundColor: '#f5f5f5' }}>Date</TableCell>
                                    <TableCell sx={{ fontWeight: 'bold', backgroundColor: '#f5f5f5' }}>Actions</TableCell>
                                </TableRow>
                            </TableHead>
                            <TableBody>
                                {/* {loading && (
                                    <TableRow>
                                        <TableCell colSpan={5} style={{ textAlign: 'center' }}>
                                            Loading...
                                        </TableCell>
                                    </TableRow>
                                )} */}

                                {satDetails && satDetails?.length > 0 && (
                                    satDetails?.map((row) => (
                                        <TableRow key={row?.assessment?.id} sx={{ '&:hover': { backgroundColor: '#f1f1f1' } }}>
                                            {/* <TableCell sx={{ padding: '10px' }}>{row?.assessment?.title}</TableCell> */}
                                            {/* <TableCell sx={{ marginLeft: '40px' }}>{row?.question_type}</TableCell> */}


                                            <TableCell sx={{ marginLeft: '40px' }}>{row?.assessment?.title}</TableCell>
                                            <TableCell sx={{ marginLeft: '40px' }}>{row?.assessment?.complexity_level}</TableCell>
                                            <TableCell sx={{ marginLeft: '40px' }}>
                                                <Switch id={`statusSwitch${row?.assessment?.id}`}
                                                    checked={row?.assessment.is_published === true}
                                                    sx={{
                                                        '& .MuiSwitch-switchBase': {
                                                            color: row?.assessment.is_published ? 'green' : 'default',
                                                        },
                                                        '& .Mui-checked': {
                                                            color: 'green',
                                                        },
                                                        '& .Mui-Track': {
                                                            backgroundColor: row?.assessment.is_published ? 'green' : 'default',
                                                        },
                                                    }}
                                                    onChange={() => {
                                                        const newValue = !row?.assessment.is_published;
                                                        handleToggleButton(row.assessment.id, newValue, 'Status');
                                                    }}
                                                />
                                            </TableCell>
                                            <TableCell sx={{ marginLeft: '40px' }}>
                                                <Switch id={`statusSwitch${row?.assessment?.id}`}
                                                    checked={row?.assessment.on_maintenance === true}
                                                    sx={{
                                                        '& .MuiSwitch-switchBase': {
                                                            color: row?.assessment.on_maintenance ? 'green' : 'default',
                                                        },
                                                        '& .Mui-checked': {
                                                            color: 'green',
                                                        },
                                                        '& .Mui-Track': {
                                                            backgroundColor: row?.assessment.on_maintenance ? 'green' : 'default',
                                                        },
                                                    }}
                                                    onChange={() => {
                                                        const newValue = !row?.assessment.on_maintenance;
                                                        handleToggleButtonMaintenance(row.assessment.id, newValue, 'Maintenance');
                                                    }}
                                                />
                                            </TableCell>
                                            <TableCell>{row?.assessment?.author_first_name}</TableCell>

                                            <TableCell>
                                                {moment(row.assessment?.created_date_time).format("DD/MM/YYYY")}
                                            </TableCell>
                                            <TableCell sx={{ padding: '10px' }}>
                                                <IconButton
                                                    disabled={row?.assessment.is_published === true}
                                                    id={`editsat${row?.assessment?.id}`}
                                                    onClick={() => handleEditAssessment(row)}
                                                    color="primary">
                                                    <EditIcon />
                                                </IconButton>
                                                <IconButton
                                                    disabled={row?.assessment.is_published === true}
                                                    id={`deletesat${row?.assessment?.id}`}
                                                    onClick={() => handleDelete(row?.assessment)}
                                                    color="primary">
                                                    <DeleteIcon />
                                                </IconButton>
                                            </TableCell>
                                        </TableRow>
                                    ))
                                )
                                    // : (
                                    //     <TableRow>
                                    //         <TableCell colSpan={8} style={{ textAlign: 'center' }}>
                                    //             {!loading && openCreateSat && openCreateSat?.length === 0 && "No Data Found"}
                                    //         </TableCell>
                                    //     </TableRow>
                                    // )
                                }
                            </TableBody>




                        </Table>
                    </TableContainer>

                    <TablePagination
                        component="div"
                        count={totalCount?.total_count}
                        page={page}
                        onPageChange={handleChangePage}
                        rowsPerPage={rowsPerPage}
                        onRowsPerPageChange={handleChangeRowsPerPage}
                        rowsPerPageOptions={[5, 10, 15, 20, 25,50]}
                        sx={{ marginTop: 2 }}
                    />
                    <DeleteAlert
                        open={deleteAlert}
                        title={deleteModalTitle}
                        confirm={handleDeleteNEET}
                        close={() => setDeleteAlert(false)}
                    />
                    {/* <SnackBar open={openSnackbar} snackbarTitle={snackbarTitle} close={() => setOpenSnackbar(false)} /> */}

                </Grid>}
                {tabValue === 1 && <Grid container spacing={2}>
                    <Grid align="end" item xs={12}>
                        <TextField
                            variant="outlined"
                            placeholder="Search Name"
                            id="searchassessment"
                            size="small"
                            sx={{ width: '250px', height: '40px' }}
                            value={searchedDetails}
                            onChange={(e) => setSearchedDetails(e.target.value)}
                            InputProps={{
                                startAdornment: (
                                    <InputAdornment position="start">
                                        <SearchIcon id="searchAssessmentIconbtn" />
                                    </InputAdornment>
                                ),
                                sx: { height: '100%' },
                            }}
                        />
                        <Button
                            style={{ marginLeft: '15px', height: '40px' }}
                            id="createassessmentButtongeneral"
                            variant="contained"
                            color="primary"
                            onClick={handleCreateNeetAssessment}
                        >
                            Create Assessment
                        </Button>
                    </Grid>
                    <TableContainer component={Paper} sx={{ marginTop: 2 }}>
                        <Table sx={{ marginLeft: '15px', width: '98%' }}>
                            <TableHead>
                                <TableRow>
                                    {/* <TableCell sx={{ fontWeight: 'bold', backgroundColor: '#f5f5f5', paddingLeft: '8px !important' }}>Name</TableCell> */}
                                    <TableCell sx={{ fontWeight: 'bold', backgroundColor: '#f5f5f5' }}>Name</TableCell>

                                    {/* <TableCell sx={{ fontWeight: 'bold', backgroundColor: '#f5f5f5' }}>Live</TableCell> */}
                                    {/* <TableCell sx={{ fontWeight: 'bold', backgroundColor: '#f5f5f5' }}>Questions</TableCell> */}
                                    <TableCell sx={{ fontWeight: 'bold', backgroundColor: '#f5f5f5' }}>Level</TableCell>
                                    <TableCell sx={{ fontWeight: 'bold', backgroundColor: '#f5f5f5' }}>Status</TableCell>
                                    <TableCell sx={{ fontWeight: 'bold', backgroundColor: '#f5f5f5' }}>Maintenance</TableCell>
                                    <TableCell sx={{ fontWeight: 'bold', backgroundColor: '#f5f5f5' }}>Author</TableCell>
                                    <TableCell sx={{ fontWeight: 'bold', backgroundColor: '#f5f5f5' }}>Date</TableCell>
                                    <TableCell sx={{ fontWeight: 'bold', backgroundColor: '#f5f5f5' }}>Actions</TableCell>
                                </TableRow>
                            </TableHead>
                            <TableBody>
                                {/* {loading && (
                                    <TableRow>
                                        <TableCell colSpan={5} style={{ textAlign: 'center' }}>
                                            Loading...
                                        </TableCell>
                                    </TableRow>
                                )} */}

                                {NeetAssessmentData && NeetAssessmentData?.length > 0 && (
                                    NeetAssessmentData?.map((row) => (
                                        <TableRow key={row?.id} sx={{ '&:hover': { backgroundColor: '#f1f1f1' } }}>
                                            {/* <TableCell sx={{ padding: '10px' }}>{row?.title}</TableCell> */}
                                            {/* <TableCell sx={{ marginLeft: '40px' }}>{row?.question_type}</TableCell> */}


                                            <TableCell sx={{ marginLeft: '40px' }}>{row?.title}</TableCell>
                                            <TableCell sx={{ marginLeft: '40px' }}>{row?.complexity_level}</TableCell>
                                            <TableCell sx={{ marginLeft: '40px' }}>
                                                <Switch id={`statusSwitch${row?.id}`}
                                                    checked={row?.is_published === true}
                                                    sx={{
                                                        '& .MuiSwitch-switchBase': {
                                                            color: row.is_published ? 'green' : 'default',
                                                        },
                                                        '& .Mui-checked': {
                                                            color: 'green',
                                                        },
                                                        '& .Mui-Track': {
                                                            backgroundColor: row.is_published ? 'green' : 'default',
                                                        },
                                                    }}
                                                    onChange={() => {
                                                        const newValue = !row.is_published;
                                                        handleToggleButton(row.id, newValue, 'Status');
                                                    }}
                                                />
                                            </TableCell>
                                            <TableCell sx={{ marginLeft: '40px' }}>
                                                <Switch id={`statusSwitch${row?.id}`}
                                                    checked={row.on_maintenance === true}
                                                    sx={{
                                                        '& .MuiSwitch-switchBase': {
                                                            color: row.on_maintenance ? 'green' : 'default',
                                                        },
                                                        '& .Mui-checked': {
                                                            color: 'green',
                                                        },
                                                        '& .Mui-Track': {
                                                            backgroundColor: row.on_maintenance ? 'green' : 'default',
                                                        },
                                                    }}
                                                    onChange={() => {
                                                        const newValue = !row.on_maintenance;
                                                        handleToggleButtonMaintenance(row.id, newValue, 'Maintenance');
                                                    }}
                                                />
                                            </TableCell>
                                            <TableCell>{row?.author_first_name}</TableCell>

                                            <TableCell>
                                                {moment(row?.created_date_time).format("DD/MM/YYYY")}
                                            </TableCell>
                                            <TableCell sx={{ padding: '10px' }}>
                                                <IconButton
                                                    disabled={row.is_published === true}
                                                    id={`editsat${row?.id}`}
                                                    onClick={() => handleEditNEETAssessment(row)}
                                                    color="primary">
                                                    <EditIcon />
                                                </IconButton>
                                                <IconButton
                                                    disabled={row.is_published === true}
                                                    id={`deletesat${row?.id}`}
                                                    onClick={() => handleDelete(row)}
                                                    color="primary">
                                                    <DeleteIcon />
                                                </IconButton>
                                            </TableCell>
                                        </TableRow>
                                    ))
                                )
                                    // : (
                                    //     <TableRow>
                                    //         <TableCell colSpan={8} style={{ textAlign: 'center' }}>
                                    //             {!loading && openCreateSat && openCreateSat?.length === 0 && "No Data Found"}
                                    //         </TableCell>
                                    //     </TableRow>
                                    // )
                                }
                            </TableBody>




                        </Table>
                    </TableContainer>

                    <TablePagination
                        component="div"
                        count={totalCountNeet?.total_count}
                        page={pageAssessment}
                        onPageChange={handleChangePageAssess}
                        rowsPerPage={rowsPerPageAssessment}
                        onRowsPerPageChange={handleChangeRowsPerPageAssess}
                        rowsPerPageOptions={[5, 10, 15, 20, 25,50]}
                        sx={{ marginTop: 2 }}
                    />
                    <DeleteAlert
                        open={deleteAlert}
                        title={deleteModalTitle}
                        confirm={handleDeleteNEET}
                        close={() => setDeleteAlert(false)}
                    />
                    <SnackBar open={openSnackbar} snackbarTitle={snackbarTitle} autoHideDuration={3000} close={() => setOpenSnackbar(false)} anchorOrigin={{ vertical: "top", horizontal: "center" }} sx={{ zIndex: 2000 }}
                    />

                </Grid>}
                {tabValue === 2 &&
                    <>
                        <Container maxWidth={false} sx={{ padding: '0 !important' }}>
                            <Grid container spacing={2}>
                                <Grid align="end" item xs={12}>
                                    <FormControl size="small" sx={{ width: '150px', marginRight: '10px', height: '46px' }}>
                                        <InputLabel id="question-type-label" style={{ maxHeight: '46px' }}>Type</InputLabel>
                                        <Select
                                            sx={{
                                                textAlign: 'left',
                                                '& .MuiSelect-select': {
                                                    padding: '8px 12px'
                                                }
                                            }}
                                            labelId="question-type-label"
                                            id="question-type-select"
                                            value={questionType}
                                            label="Type"
                                            onChange={handleTypeChange}
                                        >

                                            <MenuItem value="Chemistry">Chemistry</MenuItem>
                                            <MenuItem value="Physics">Physics</MenuItem>
                                            <MenuItem value="Biology">Biology</MenuItem>
                                        </Select>
                                    </FormControl>
                                    <TextField
                                        variant="outlined"
                                        placeholder="Search questions"
                                        size="small"
                                        sx={{ width: '250px', height: '40px' }}
                                        InputProps={{
                                            startAdornment: (
                                                <InputAdornment position="start">
                                                    <SearchIcon id="searchQuestionIconbtn" />
                                                </InputAdornment>
                                            ),
                                            sx: { height: '100%' },
                                        }}
                                        value={searchText}
                                        onChange={handleInputChange}
                                    />
                      
  <TextField  label="Search Page" variant="outlined" size="small"
    value={pageQuesBank}
    onChange={(e) => {
      const val = e.target.value;
      setPageQuesBank(val === "" || val === null ? 0 : val);
    }}
    sx={{
      width: '200px',
        maxHeight: '39px',
        height: '40px',
      marginRight: '8px',
      marginLeft: "5px",
      '& .MuiInputBase-input::placeholder': {
        position: 'absolute',
        top: '-10px',
        left: '50%',
        transform: 'translateX(-50%)',
        backgroundColor: '#fff',
        padding: '0 4px',
              maxHeight: '39px',
        height: '40px',
        fontSize: '0.75rem',
        color: '#888',
      },
      '& .MuiInputBase-input': {
      padding: '8.5px 14px', 
    },

    }}

  />

                                    <Button
                                        style={{ marginLeft: '15px', height: '40px' }}
                                        id="createQuestionButton"
                                        variant="contained"
                                        color="primary"
                                        onClick={handleCreateMcqQuestion}
                                    >
                                        Create Questions
                                    </Button>

                                </Grid>
                                <TableContainer component={Paper}>
                                    <Table>
                                        <TableHead>
                                            <TableRow>
                                                <TableCell>Name</TableCell>
                                                <TableCell>Type</TableCell>
                                                <TableCell>Status</TableCell>
                                                <TableCell>Author</TableCell>
                                                <TableCell>Date</TableCell>
                                                <TableCell>Action</TableCell>
                                            </TableRow>
                                        </TableHead>

                                        <TableBody>
                                            {loading && (
                                                <TableRow>
                                                    <TableCell colSpan={5} style={{ textAlign: 'center' }}>
                                                        Loading...
                                                    </TableCell>
                                                </TableRow>
                                            )}

                                            {!loading && questionList?.length === 0 && (
                                                <TableRow>
                                                    <TableCell colSpan={5} style={{ textAlign: 'center' }}>
                                                        Sorry, there is no matching data to display
                                                    </TableCell>
                                                </TableRow>
                                            )}
                                            {!loading && questionList && questionList.map((row, index) => (
                                                <TableRow key={index}>
                                                    <TableCell dangerouslySetInnerHTML={{ __html: DOMPurify.sanitize(row.question_text) }} />
                                                    <TableCell>{row.question_type}</TableCell>
                                                    <TableCell style={{ color: row.is_active ? 'green' : 'red' }}>
                                                        {row.is_active ? 'Active' : 'Inactive'}
                                                    </TableCell>
                                                    <TableCell>{row?.firstname} {row?.lastname}</TableCell>
                                                    <TableCell>{moment(row.created_date).isValid() ? moment(row.created_date).format('MM-DD-YYYY (h:mm A)') : 'Invalid Date'}
                                                    </TableCell>
                                                    <TableCell>
                                                        <IconButton id={`edit${row.id}`} color="primary" onClick={() => handleEdit(row.id)}>
                                                            <EditIcon />
                                                        </IconButton>
                                                        {row?.is_linked_to_assessment === false ?
                                                            <IconButton id={`delete${row.id}`} color="secondary" onClick={() => handleDeletOpen(row)}>
                                                                <DeleteIcon />
                                                            </IconButton>
                                                            :
                                                            <>
                                                                <Tooltip title="This question is part of NeetSource!" arrow>
                                                                    <span>
                                                                        <IconButton id={`delete${row.id}`} color="secondary" disabled>
                                                                            <DeleteIcon />
                                                                        </IconButton>
                                                                    </span>
                                                                </Tooltip>
                                                            </>

                                                        }
                                                    </TableCell>
                                                </TableRow>
                                            ))}
                                        </TableBody>
                                    </Table>
                                </TableContainer>



                                <Grid item xs="12" sm="12" md="12" lg="12" xl="12">
                                    <div >
                                        <TablePagination
                                            component="div"
                                            count={maxCount || 0}
                                            page={pageQuesBank}
                                            onPageChange={handleChangePageQuestion}
                                            rowsPerPage={rowsPerPageQuesBank}
                                            onRowsPerPageChange={handleChangeRowsPerPageQuest}
                                            rowsPerPageOptions={[5, 10, 15, 20, 25,50]}
                                            sx={{ marginTop: 2 }}
                                        />
                                    </div>
                                </Grid>
                            </Grid>
                              <SnackBar 
                        open={openSnackbar} 
                        snackbarTitle={snackbarTitle} 
                        close={() => setOpenSnackbar(false)} 
                        autoHideDuration={3000}
                        anchorOrigin={{ vertical: "top", horizontal: "center" }}
                        sx={{ zIndex: 2000 }}
                    />
                        </Container>

                        <CreateMcqQuestionModel
                            open={openCreateMcq}
                            modelClose={() => setOpenCreateMcq(!openCreateMcq)}
                            title="Create Questions"
                            handleCreateMcq={handleSubmitForm}
                            loading={loading}
                            searchedDetails={searchedDetails}
                        />
                        <EditQuestionModule
                            open={openEditMcq}
                            modelClose={() => setOpenEditMcq(!openEditMcq)}
                            title="Edit Questions"
                            handleEditMcq={handleEditCallBack}
                            loading={loading}
                            searchedDetails={searchedDetails}
                            data={newData}
                        />
                    </>

                }
            </>
            <Dialog open={openUserMessage} onClose={() => setOpenUserMessage(false)}>
                <DialogTitle>Provide Message and Date Range</DialogTitle>
                <DialogContent>
                    <TextField
                        label="Message"
                        value={userMessageData}
                        onChange={(e) => setUserMessageData(e.target.value)}
                        fullWidth
                        multiline
                        rows={3}
                        margin="normal"
                    />
                    <TextField
                        label="From Date"
                        type="datetime-local"
                        value={userMessageFromDateAndTime}
                        onChange={(e) => setUserMessageFromDateAndTime(e.target.value)}
                        fullWidth
                        margin="normal"
                        InputLabelProps={{ shrink: true }}
                    />
                    <TextField
                        label="To Date"
                        type="datetime-local"
                        value={userMessageToDateAndTime}
                        onChange={(e) => setUserMessageToDateAndTime(e.target.value)}
                        fullWidth
                        margin="normal"
                        InputLabelProps={{ shrink: true }}
                    />
                </DialogContent>
                <DialogActions>
                    <Button onClick={() => setOpenUserMessage(false)}>Cancel</Button>
                    <Button onClick={handleSubmitUserMessage} variant="contained" color="primary">
                        Submit
                    </Button>
                </DialogActions>
            </Dialog >
            <DeleteAlert
                open={deleteOpen}
                title="Are you sure you want delete this Question"
                confirm={handleDeleteNeetQuestion}
                close={() => setDeleteOpen(false)}
            />

        </Page>
    )
}

export default NEET;