// eslint-disable-next-line no-unused-vars
import { alpha } from '@mui/material/styles';

// ----------------------------------------------------------------------

export default function Backdrop() {
  // const varLow = alpha(theme.palette.grey[900], 0.48);
  // const varHigh = alpha(theme.palette.grey[900], 1);

  return {
    MuiBackdrop: {
      styleOverrides: {
        root: {
          // background: '#fff',
          '&.MuiBackdrop-invisible': {
            background: 'transparent',
          },
        },
      },
    },
  };
}
