import PropTypes from 'prop-types';
import {styled } from '@mui/material/styles';
import { useLocation } from 'react-router-dom';
import { Box, Stack, AppBar, Toolbar, IconButton } from '@mui/material';
import Iconify from '../../components/Iconify';

import AccountPopover from './AccountPopover';
// import LanguagePopover from './LanguagePopover';
// import LanguageSwitcher from '../../website/pages/LanguageSwitcher';

// ----------------------------------------------------------------------

const APPBAR_MOBILE = 64;
const APPBAR_DESKTOP = 92;

const ToolbarStyle = styled(Toolbar)(({ theme }) => ({
  minHeight: APPBAR_MOBILE,
  [theme.breakpoints.up('lg')]: {
    minHeight: APPBAR_DESKTOP - 24,
    padding: theme.spacing(0, 5),
  },
}));

const RootStyle = styled(AppBar)(({ theme }) => ({
  boxShadow: '0px 3px 6px #********',
  // backdropFilter: 'blur(6px)',
  // WebkitBackdropFilter: 'blur(6px)', // Fix on Mobile
  // backgroundColor: alpha(theme.palette.background.default, 0.72),
  background: '#fdfdfdf2',
  [theme.breakpoints.up('lg')]: {
    // width: `calc(100% - ${collapsed ? 70 : 270}px)`,
    width: '100%',
  },
}));

// ----------------------------------------------------------------------

DashboardNavbar.propTypes = {
  onOpenSidebar: PropTypes.func,
};

export default function DashboardNavbar({ onOpenSidebar }) {
  const location = useLocation();
  const isHidden = location.pathname === '/login' || location.pathname === '/sign-up';
  
  return (
    <RootStyle
      isHidden={isHidden}
      sx={{
        display: isHidden ? 'none !important' : 'flex',
      }}
    >
      <ToolbarStyle>
        <IconButton onClick={onOpenSidebar} sx={{ mr: 1, color: 'text.primary', display: { lg: 'none' } }}>
          <Iconify icon="eva:menu-2-fill" />
        </IconButton>
        {/* <Searchbar /> */}
        <Box sx={{ flexGrow: 1 }} />
        <Stack direction="row" alignItems="center" spacing={{ xs: 0.5, sm: 1.5 }}>
        {/* {
         process.env.REACT_APP_ENV === 'dev' &&
         userRole !== 'SUPER_ADMIN' || userRole !== 'CONTENT_WRITER' &&
         <LanguageSwitcher isDisabled={allDetails?.comingfromsub === 'SAT/ACT'} />
        }  */}
          {/* <NotificationsPopover /> */}
          <AccountPopover />
        </Stack>
      </ToolbarStyle>
    </RootStyle>
  );
}
