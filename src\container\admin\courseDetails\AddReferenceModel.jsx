/* eslint-disable react/prop-types */
import React from 'react';
import {
    Typo<PERSON>,
    FormControl,
    Box,
    Grid,
    FormHelperText,
    Badge,
    Tooltip,
    IconButton,
    Radio
} from '@mui/material';
import { DropzoneArea } from 'material-ui-dropzone';
import { Form, Formik, FieldArray } from 'formik';
import CloseIcon from '@mui/icons-material/Close';
import { makeStyles } from '@mui/styles';
import { LoadingButton } from '@mui/lab';
import ReactQuill from 'react-quill';
import DialogModal from '../../../components/modal/DialogModal';
import 'react-quill/dist/quill.snow.css';
 
const useStyles = makeStyles(() => ({
    imgPreviewRoot: {
        marginBottom: '1rem'
    },
    badgeAlign: {
        marginTop: '5px'
    }
}));
 
const FILE_SIZE = 5 * 1024 * 1024;

 
const AddRererenceModel = ({
    open,
    onClose,
    modelTitle,
    handleSubmit,
    loading,
    data,
    formType,
    setActionType,
    updateBtnLoading,
    deleteBtnLoading
}) => {

    console.log(data,"data");
    
    const classes = useStyles();
    const getInitialValue = () => {
        if (!data) {
            return {
                references: [
                    {
                        referenceFile: null,
                        referenceFileView: null,
                        referenceFileName: null,
                        description: '',
                        isFeatured: false
                    }
                ]
            };
        }
 
        const transformed = data.referenceFiles.map((_, index) => ({
            referenceFile: data.referenceFiles[index],
            referenceFileView: data.referenceFileViews[index],
            referenceFileName: data.referenceFileNames[index],
            description: data.referenceTexts[index],
            isFeatured: data.isFeatured[index] === "true" || data.isFeatured[index] === true
        }));
 
        return {
            references: transformed.length
                ? transformed
                : [{
                    referenceFile: null,
                    referenceFileView: null,
                    referenceFileName: null,
                    description: '',
                    isFeatured: false
                }]
        };
    };
    const customValidationFunction = (values) => {
        const errors = {};
        if (!Array.isArray(values.references)) {
            errors.references = 'References must be an array';
            return errors;
        }

        const referenceErrors = [];
        values.references.forEach((ref, index) => {
            const refError = {};
            const cleanedDescription = (ref.description || '').trim().replace(/<[^>]*>/g, '');
            if (!ref.description || cleanedDescription.length === 0) {
                refError.description = 'Description is required';
            }
            const file = ref.referenceFile;
            if (!file) {
                refError.referenceFile = 'File is required';
            } else if (file.size > FILE_SIZE) {
                refError.referenceFile = 'File too large';
            }
            if (Object.keys(refError).length > 0) {
                referenceErrors[index] = refError;
            }
        });
        if (referenceErrors.length > 0) {
            errors.references = referenceErrors;
        }
        return errors;
    };



 
    const validateDescription = (desc) => {
        const clean = desc.trim().replace(/<[^>]*>/g, '');
        console.log(clean,'cleanclean');
        return clean.length > 0;
    };
 
    return (
         <DialogModal 
            id="ReferenceModal" 
            open={open} 
            handleClose={onClose} 
            title={modelTitle} 
            // maxWidth="md"
            // sx={{
            //     '& .MuiDialog-paper': {
            //         width: '700px',
            //         maxWidth: '700px'
            //     }
            // }}
        >     <Formik
                enableReinitialize
                initialValues={getInitialValue()}
                validate={customValidationFunction} 
                onSubmit={(values) => {
                    handleSubmit(values);
                }}
            >
                {({ values, setFieldValue, touched, errors }) => (
                    <Form>
                        {console.log(values,"values")}
                       
                        <FieldArray name="references">
                            {({ push, remove }) => (
                                <Box>
                                    {values.references.map((ref, index) => (
                                        <Box key={index} mb={3} borderRadius={1} style={{ border: '1px solid #ddd' , padding: '15px 20px', position: 'relative'}}>
                                            <Grid container spacing={2} alignItems="center">
                                                <Grid item xs={12}>
                                                    {!ref.referenceFileView ? (
                                                        <FormControl fullWidth>
                                                     
                                                            <Box display="flex" justifyContent="space-between" alignItems="center">
                                                                 <Typography variant="subtitle1">Reference File</Typography>
                                                          
                                                            </Box>
                                                       
                                                            <DropzoneArea
                                                             acceptedFiles={['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.txt']}
                                                                showPreviews={false}
                                                                dropzoneText="Drag and Drop or Browse"
                                                                showPreviewsInDropzone={false}
                                                                maxFileSize={FILE_SIZE}
                                                                filesLimit={1}
                                                                showAlerts={false}
                                                                onChange={(files) => {
                                                                    const file = files[0];
                                                                    if (file?.name.endsWith('.zip')) {
                                                                        // eslint-disable-next-line no-alert
                                                                        alert('ZIP files are not allowed');
                                                                        return;
                                                                      }
                                                                    if (file?.size <= FILE_SIZE) {
                                                                        const url = URL.createObjectURL(file);
                                                                        setFieldValue(`references[${index}].referenceFile`, file);
                                                                        setFieldValue(`references[${index}].referenceFileName`, file.name);
                                                                        setFieldValue(`references[${index}].referenceFileView`, url);
                                                                    }
                                                                }}
                                                            />
                                                              <FormHelperText style={{ color: '#F44336' }}>
                                                        {touched.references?.[index]?.referenceFile &&
                                                            errors.references?.[index]?.referenceFile}
                                                    </FormHelperText>
                                                        </FormControl>
                                                    ) : (
                                                        <div className={classes.imgPreviewRoot}>
                                                            <Typography variant="subtitle1">Reference</Typography>
                                                            <Badge sx={{marginTop: '12px'}}
                                                                badgeContent={
                                                                    <Tooltip title="Change File">
                                                                        <IconButton
                                                                            size="small"
                                                                            onClick={() => {
                                                                                setFieldValue(`references[${index}].referenceFile`, null);
                                                                                setFieldValue(`references[${index}].referenceFileView`, null);
                                                                                setFieldValue(`references[${index}].referenceFileName`, null);
                                                                            }}
                                                                        >
                                                                            <CloseIcon fontSize="small" />
                                                                        </IconButton>
                                                                    </Tooltip>
                                                                }
                                                            >
                                                                {ref.referenceFile?.type?.startsWith('image/') ? (
                                                                    <img
                                                                        src={ref.referenceFileView}
                                                                        alt="preview"
                                                                        width="320"
                                                                    />
                                                                ) : (
                                                                    <a href={ref.referenceFileView} style={{ overflowWrap: 'anywhere', marginTop: '18px'}} target="_blank" rel="noreferrer">
                                                                        {ref.referenceFileName || 'View File'}
                                                                    </a>
                                                                )}
                                                            </Badge>
                                                        </div>
                                                    )}
                                                </Grid>

                                                <Box>
                                                    <FormControl style={{ display: 'flex', flexDirection: 'row', alignItems: 'center', position: 'absolute', top: '8px', right: '0px' }}>
                                                        <Typography variant="subtitle1">Mark as Featured</Typography>
                                                        <Radio
                                                            checked={ref.isFeatured}
                                                            onChange={() => {
                                                                values.references.forEach((_, i) => {
                                                                    setFieldValue(`references[${i}].isFeatured`, i === index);
                                                                });
                                                            }}
                                                            color="primary"
                                                        />
                                                    </FormControl>
                                                    {index === values.references.length - 1 && errors.references && typeof errors.references === 'string' && (
                                                        <FormHelperText style={{ color: '#F44336' }}>
                                                            {errors.references}
                                                        </FormHelperText>
                                                    )}
                                                </Box>
                                                <Grid item xs={12}>
                                                    <Typography gutterBottom variant="subtitle1">
                                                        Description*
                                                    </Typography>
                                                    <ReactQuill
                                                        theme="snow"
                                                        value={ref.description}
                                                       
                                                        onChange={(content) => {
                                                            const cleanedContent = content === "<p><br></p>" ? "" : content;
                                                            setFieldValue(`references[${index}].description`, cleanedContent);
                                                          }}
 
                                                        // onChange={(content, delta, source, editor) => {
                                                       
                                                        //     // const plainText = editor.getText().trim();
                                                        //     // if (!ref.isFeatured && plainText.length > 45) return;
                                                        //     setFieldValue(`references[${index}].description`, content);
                                                        //   }}
                                                    />
                                                    <FormHelperText style={{ color: '#F44336' }}>
                                                        {touched.references?.[index]?.description &&
                                                            errors.references?.[index]?.description}
                                                    </FormHelperText>
                                                </Grid>
 
                                                {values.references.length > 1 && (
                                                    <Grid item xs={12}>
                                                        <LoadingButton
                                                            color="secondary"
                                                            variant="outlined"
                                                            onClick={() => remove(index)}
                                                            style={{ marginTop: 10 }}
                                                        >
                                                            Remove Reference
                                                        </LoadingButton>
                                                    </Grid>
                                                )}
                                            </Grid>
                                        </Box>
                                    ))}
 
                                    <LoadingButton
                                        variant="outlined"
                                        color="primary"
                                        onClick={() => {
                                            const lastIndex = values.references.length - 1;
                                            const lastDescription = values.references[lastIndex].description;
                                            const isValid = validateDescription(lastDescription);
 
                                            if (!isValid) {
                                                // eslint-disable-next-line no-alert
                                                alert('Please fill in the description for the current reference before adding a new one.');
                                                return;
                                            }
 
                                            push({
                                                referenceFile: null,
                                                referenceFileView: null,
                                                referenceFileName: null,
                                                description: '',
                                                isFeatured: false
                                            });
                                        }}
                                        fullWidth
                                    >
                                        Add Another Reference
                                    </LoadingButton>
                                </Box>
                            )}
                        </FieldArray>
 
                        <Box mt={2}>
                            {formType === 'createRef' && (
                                <LoadingButton
                                    variant="contained"
                                    color="primary"
                                    fullWidth
                                    type="submit"
                                    loading={loading}
                                >
                                    Create
                                </LoadingButton>
                            )}
                            {formType === 'editRef' && (
                                <Box display="flex" justifyContent="space-between" gap={2}>
                                    <LoadingButton
                                        variant="contained"
                                        color="primary"
                                        type="submit"
                                        loading={updateBtnLoading}
                                        onClick={() => setActionType('update')}
                                    >
                                        Update
                                    </LoadingButton>
                                    <LoadingButton
                                        variant="contained"
                                        color="error"
                                        type="submit"
                                        loading={deleteBtnLoading}
                                        onClick={() => setActionType('delete')}
                                    >
                                        Delete
                                    </LoadingButton>
                                </Box>
                            )}
                        </Box>
                    </Form>
                )}
            </Formik>
        </DialogModal>
    );
};
 
export default AddRererenceModel;