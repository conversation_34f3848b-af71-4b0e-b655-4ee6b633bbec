export const replaceS3UrlWithCloudFront = (url) => {

    // const s3UrlPrefix = 'https://testmybucket.s3.amazonaws.com';
    const s3UrlPrefix = process.env.NODE_ENV !== 'production'
    ? 'https://testmybucket.s3.amazonaws.com'  
    : 'https://prodskillsetbucket';
    const cloudFrontUrlPrefix = 'https://dh0utea3ai9xq.cloudfront.net';
    
    if (url?.startsWith(s3UrlPrefix)) {
      return url?.replace(s3UrlPrefix, cloudFrontUrlPrefix);
    }

    return url;
  };
  // https://prodskillsetbucket/courseInfo/0_11707372004.jpg