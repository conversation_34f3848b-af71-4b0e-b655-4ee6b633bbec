import React, { useState, useEffect } from 'react';
import { Box, Typography } from '@mui/material';
import MUIDataTable from 'mui-datatables';
import moment from 'moment';
import LinearProgress from '@mui/material/LinearProgress';
import PageHeader from '../../../components/PageHeader';
import Page from '../../../components/Page';
import iqServices from '../../../services/iqTest/index';
import BasicModel from '../../../components/modal/BasicModel';
import Iconify from '../../../components/Iconify';


const IqAnalytics = () => {
  const [analyticsData, setAnalyticsData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [openModal, setOpenModal] = useState(false);
  const [iqDescription, setIqDescription] = useState([]);

  useEffect(() => {
    getIqAnalytics();
    getOS();
  }, []);

  function getOS() {
    const userAgent = window.navigator.userAgent;
    const platform = window.navigator?.userAgentData?.platform || window.navigator.platform;
    const macosPlatforms = ['Macintosh', 'MacIntel', 'MacPPC', 'Mac68K'];
    const windowsPlatforms = ['Win32', 'Win64', 'Windows', 'WinCE'];
    const iosPlatforms = ['iPhone', 'iPad', 'iPod'];
    let os = null;

    if (macosPlatforms.indexOf(platform) !== -1) {
      os = 'Mac OS';
    } else if (iosPlatforms.indexOf(platform) !== -1) {
      os = 'iOS';
    } else if (windowsPlatforms.indexOf(platform) !== -1) {
      os = 'Windows';
    } else if (/Android/.test(userAgent)) {
      os = 'Android';
    } else if (/Linux/.test(platform)) {
      os = 'Linux';
    }
    return os;
  }

  const getIqAnalytics = () => {
    iqServices.getIqAnalytics().then((response) => {
      if (response.ok) {
        setAnalyticsData(response.data);
      }
      setLoading(false);
    });
  };

  const columns = [
    {
      name: 'userId',
      label: 'User ID',
      options: {
        filter: true,
        sort: false,
      },
    },
    {
      name: 'name',
      label: 'Name',
      options: {
        filter: true,
        sort: false,
      },
    },
    {
      name: 'analytics',
      label: 'Start date',
      options: {
        filter: true,
        sort: false,
        customBodyRender: (value) => {
          return <Typography>{value && moment(value.startDateTime).format('DD-MM-yyyy')}</Typography>;
        },
      },
    },
    {
      name: 'analytics',
      label: 'End date',
      options: {
        filter: true,
        sort: false,
        customBodyRender: (value) => {
          return <Typography>{value && moment(value.endDatetime).format('DD-MM-yyyy')}</Typography>;
        },
      },
    },
    {
      name: 'analytics',
      label: 'Answer in % ',
      options: {
        filter: true,
        sort: false,
        customBodyRender: (value) => {
          return (
            <Box
              onClick={() => {
                if (value && value.userInput.length > 0) {
                  setIqDescription(value.userInput);
                  setOpenModal(true);
                }
              }}
              sx={{ cursor: value.userInput && Object.keys(value.userInput).length  > 0 ? 'pointer' : 'default' }}
            >
              <LinearProgress variant="determinate" value={value && value.answerInPercentage} />
              <Typography fontSize={'12px'} variant="body2">
                {value ? value.answerInPercentage : '0'}%
              </Typography>
            </Box>
          );
        },
      },
    },

    {
      name: 'analytics',
      label: 'Status',
      options: {
        filter: true,
        sort: false,
        customBodyRender: (value) => {
          return <Typography>{value && value.isCompleted ? 'Completed' : 'Incomplete'}</Typography>;
        },
      },
    },
  ];

  return (
    <Page title="IQ-result">
      <PageHeader pageTitle={'IQ Result'} />

      <Box mt={2}>
        <MUIDataTable
          data={analyticsData}
          columns={columns}
          options={{
            responsive: "scroll",
            rowsPerPage: 25,
            rowsPerPageOptions: [25, 50, 75, 100],
            selectableRows: false,
            filter: false,
            download: false,
            print: false,
            textLabels: {
              body: {
                noMatch: loading ? 'Loading...' : 'No data',
              },
            },
          }}
        />
      </Box>
      <BasicModel openModel={openModal} title={'Description'} closeModel={() => setOpenModal(false)}>
        {iqDescription.map((res) => (
          <Box display={'flex'} justifyContent={'space-between'} mb={2}>
            {res.iqQuestion ? (
              <Typography>{res.iqQuestion && res.iqQuestion}</Typography>
            ) : (
              <Typography dangerouslySetInnerHTML={{ __html: res.description }} />
            )}
            {res.iqQuestion && (
              <>
                {res.isCorrect ? (
                  <Iconify icon={'ant-design:check-outlined'} width={20} height={20} color={'#66e315f7'} />
                ) : (
                  <Iconify icon={'gridicons:cross-small'} width={22} height={22} color={'#f51212f7'} />
                )}
              </>
            )}
          </Box>
        ))}
      </BasicModel>
    </Page>
  );
};

export default IqAnalytics;
