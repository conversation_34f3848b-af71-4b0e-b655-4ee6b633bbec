import { useRef, useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Link as RouterLink, useNavigate, useLocation } from 'react-router-dom';
// @mui
import { alpha } from '@mui/material/styles';
import { Box, Divider, Typography, Stack, MenuItem, Avatar, IconButton } from '@mui/material';
import { useSelector, useDispatch } from 'react-redux';
import { BroadcastChannel } from 'broadcast-channel';
// components
import MenuPopover from '../../components/MenuPopover';
import { logoutSuccess, guideHandler,ComingFrom,FromGeneralAssessmentView,setB2BPage,languagecodevalue } from '../../store/reducer';
import loginServices from '../../services/loginServices';


// eslint-disable-next-line react/prop-types
export default function AccountPopover({coming,CallBack}) {
 
  
  const anchorRef = useRef(null);
  const navigate = useNavigate();
  const { t } = useTranslation('translation');
  const { pathname } = useLocation();
  const userInfo = useSelector((state) => state.userInfo);
  const defaultlanguage = useSelector((state) => state.languagecode);
  const userRole = useSelector((state) => state.userInfo && state.userInfo.role);
  const [open, setOpen] = useState(null);
  const dispatch = useDispatch();
  const isLogin = useSelector((state) => state.isLogin);
  const [show, setShow] = useState(false);
  const logoutChannel = new BroadcastChannel('logout');

  const handleOpen = (event) => {
    
    if(coming === 'B2B'){
      CallBack("opened")
    }
    if(coming === 'B2C'){
      CallBack("opened1")
    }
    setOpen(event.currentTarget);
  };


  const handleClose = () => {
    setOpen(null);
    // CallBack("opened")
  };


  const handleLogout = () => {
    logoutChannel.postMessage('Logout');

    dispatch(logoutSuccess());
    dispatch(FromGeneralAssessmentView(false));
    dispatch(setB2BPage('paid'));
    dispatch(ComingFrom("Courses"))
    dispatch(languagecodevalue(defaultlanguage));
  
    localStorage.setItem('selectedToggle', 'paid');

    navigate('/login');
    
    loginServices.logout();
  };

  const guidHandler = () => {
    dispatch(guideHandler(true));
    handleClose();
  };

  const handleGotoDashboard = () => {
    if (isLogin) {
      if (isLogin && (userRole === 'SUPER_ADMIN' || userRole === 'CLIENT_ADMIN')) {
        navigate('/app/dashboard');
        CallBack("closed")

        // window.location.href = '/app/dashboard';
      } else if (isLogin && (userRole === 'CONTENT_WRITER' || userRole === 'AUTH_USER')) {
        dispatch(languagecodevalue('en'))
       
        navigate('/app/course');
        CallBack("closed")

        // window.location.href = '/app/course';
      } else {        
        localStorage.setItem('from', 'Courses');
        navigate('/auth/subscribe');
        dispatch(ComingFrom("Courses"))
        CallBack("closed")

        // navigate('/auth/my-courses');
      }
    }
    setOpen(null);

  };

  function stringAvatar() {
    const fullName = userInfo;
    
    return {
      sx: {
        // bgcolor: stringToColor(name),
        fontSize: '1.4rem',
        fontWeight: '600',
      },
      children: fullName?.lastName !== null && fullName?.lastName !== ""
  ? `${fullName?.firstName.charAt(0).toUpperCase()}${fullName?.lastName.charAt(0).toUpperCase()}`
  : `${fullName?.firstName.charAt(0).toUpperCase()}`

      // children: `${fullName?.firstName.charAt(0).toUpperCase()}${fullName?.lastName.charAt(0).toUpperCase()}`,
    };
  }

  useEffect(() => {
    if (['/app/course-details', '/auth/course-details'].includes(pathname)) {
      setShow(true);
    } else {
      setShow(false);
    }
  }, [pathname]);
  const translatedMenuOptions = [
    {
      label: userRole === 'SUPER_ADMIN' || userRole === 'CLIENT_ADMIN' ? 'Profile': t('Profile'),
      icon: 'eva:person-fill',
      linkTo: '/app/account',
    },
  ];

  return (
    <>
      <IconButton className='userProfile'
        ref={anchorRef}
        onClick={handleOpen}
        sx={{
          marginLeft: 5,
          p: 0,
          '@media (max-width: 900px)': {
            marginLeft: '20px',
        },
        '@media (max-width: 1100px)': {
          marginLeft: '50px',
      },
          ...(open && {
            '&:before': {
              zIndex: 1,
              content: "''",
              width: '100%',
              height: '100%',
              borderRadius: '50%',
              position: 'absolute',
              bgcolor: (theme) => alpha(theme.palette.grey[900], 0.8),
            },
          })
        }}>
        <Box sx={{ display: 'flex', alignItems: 'end', justifyContent: 'flex-end' }}>
          <Avatar sx={{ ml: 20 }} {...stringAvatar()} />
        </Box>
        {/* {userInfo && userInfo.profileImg !== null && userInfo.profileImg !== '' ? (
          <Avatar src={userInfo && userInfo.profileImg} alt="photoURL" />
        ) : (
          <Avatar {...stringAvatar()} />
        )} */}
      </IconButton>
      <MenuPopover
        open={Boolean(open)}
        anchorEl={open}
        onClose={handleClose}
        sx={{
          p: 0,
          mt: 1.5,
          ml: 0.75,
          '& .MuiMenuItem-root': {
            typography: 'body2',
            borderRadius: 0.75,
          },
        }}
      >
        <Box sx={{ my: 1.5, paddingLeft: '8px !important' }} pl={2}>
          <Typography variant="subtitle2" noWrap>
            {userInfo && userInfo.name}
          </Typography>
          <Typography variant="body2" sx={{ color: 'text.secondary' }} noWrap>
            {userInfo && userInfo.email}
          </Typography>
        </Box>

        <Divider sx={{ borderStyle: 'dashed' }} />

        <MenuItem onClick={handleGotoDashboard} sx={{ ml: 1, mt: 1, mr: 1 }}>
        { userRole === 'SUPER_ADMIN' || userRole === 'CLIENT_ADMIN' ? 'Dashboard': t('Dashboard')}
        </MenuItem>

        <Stack sx={{ p: 1 }}>
          {translatedMenuOptions.map((option) => (
            <MenuItem
              key={option.label}
              to={userRole === 'USER_DTC' ? '/auth/account' : option.linkTo}
              component={RouterLink}
              onClick={handleClose}
            >
              {option.label}
            </MenuItem>
          ))}

          {userRole === 'USER_DTC' && (
            <MenuItem to={'/auth/subscriptions'} component={RouterLink} onClick={handleClose} sx={{ mt: 1 }}>
                    {userRole === 'SUPER_ADMIN' || userRole === 'CLIENT_ADMIN' ? 'Subscriptions': t('Subscriptions')}  
            </MenuItem>
          )}
        </Stack>

        {show && (
          <>
            <Divider sx={{ borderStyle: 'dashed' }} />

            <MenuItem onClick={guidHandler} sx={{ m: 1 }}>
            {userRole === 'SUPER_ADMIN' || userRole === 'CLIENT_ADMIN' ? 'Take Tour': t('Take Tour')}
            </MenuItem>
          </>
        )}
        <Divider sx={{ borderStyle: 'dashed' }} />

        <MenuItem onClick={handleLogout} sx={{ m: 1 }}>
        {userRole === 'SUPER_ADMIN' || userRole === 'CLIENT_ADMIN' ? 'Logout': t('Logout')}
        </MenuItem>
      </MenuPopover>
    </>
  );
}
