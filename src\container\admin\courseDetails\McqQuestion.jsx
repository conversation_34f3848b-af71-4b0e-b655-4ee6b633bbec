/* eslint-disable react-hooks/exhaustive-deps */
import React, { useState, useEffect } from "react";
import {
    <PERSON>rid,
    Container,
    Button,
    IconButton,
    TextField,
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableHead,
    TableRow,
    Paper,
    TablePagination, Tooltip,
    Select,
    InputLabel,
    FormControl, MenuItem
} from '@mui/material';
import DOMPurify from 'dompurify';

import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import InputAdornment from '@mui/material/InputAdornment';
import SearchIcon from '@mui/icons-material/Search';
import moment from 'moment';
import DeleteAlert from '../../../components/modal/DeleteModal';


import Page from '../../../components/Page';
import PageHeader from '../../../components/PageHeader';
import CreateMcqQuestionModel from "./CreateMcqQuestionModel";
import EditQuestionModule from "./EditMcqQuestionsModule";
import adminServices from '../../../services/adminServices';
import SnackBar from '../../../components/snackbar/snackbar';


const McqQuestion = () => {
    const [openCreateMcq, setOpenCreateMcq] = useState(false);
    const [openEditMcq, setOpenEditMcq] = useState(false);
    const [keyObjects, setKeyObjects] = useState([]);
    const [snackbarTitle, setSnackbarTitle] = useState('');
    const [openSnackbar, setOpenSnackbar] = useState(false);
    const [loading, setLoading] = useState(false);
    // eslint-disable-next-line no-unused-vars
    const [searchedDetails, setSearchedDetails] = useState('')
    const [questionList, setQuestionList] = useState([]);
    console.log(questionList,"questionList");
    

    const [searchText, setSearchText] = useState('');
    const [maxCount, setMaxCount] = useState(0);
    const [deleteOpen, setDeleteOpen] = React.useState(false);
    const [newData, setNewData] = React.useState('');
    const [deleteId, setDeleteId] = React.useState('');
    const [page, setPage] = useState(0);
    const [rowsPerPage, setRowsPerPage] = useState(10);
    const [questionType, setQuestionType] = useState('MCQ');

    const handleTypeChange = (event) => {
        setQuestionType(event.target.value);
    };
    const handleCreateMcqQuestion = () => {
        setOpenCreateMcq(true)
    };

    const handleChangePage = (event, newPage) => {
        setPage(newPage);
    };
    const handleChangeRowsPerPage = (event) => {
        setRowsPerPage(parseInt(event.target.value, 10));
        setPage(0);
    };

    const getAllQuestions = async () => {
        setLoading(true);
        const result = await adminServices.getAllQuestion(rowsPerPage, page, searchText, questionType);
        if (result.ok) {
            console.log(result);
            setMaxCount(result.data.total)
            const sortedQuestions = result.data.newQuestions.sort((a, b) =>
                new Date(b.created_date) - new Date(a.created_date)
            );
            setQuestionList(sortedQuestions);
            setLoading(false);
        }
        setLoading(false);
    };

    useEffect(() => {
        getAllQuestions();
    }, [page, rowsPerPage, searchText, questionType]);

    const handleInputChange = (event) => {
        const newSearch = event.target.value;
        setSearchText(newSearch);
    };




    const handleSubmitForm = async (values) => {
        const array = keyObjects;
        const object = {
            windows: {
                keyCode: values.combinedWindowKeys,
                keyName: values.combinedWindowKeyName,
            },
            mac: {
                keyCode: values.combinedMacKeys,
                keyName: values.combinedMacKeyName,
            },
            positionIndex: array.length,
        };
        array.push(object);
        setKeyObjects(array);
        handleCreateQuestion(object, values)

    }

    const handleCreateQuestion = async (object, values) => {
        setLoading(true);
        try {
            const keysData = new FormData();
            keysData.append('question', values?.question);
            keysData.append('explanation', values?.explanation);
            keysData.append('level', values?.level);
            keysData.append('questionType', values?.questionType);
            keysData.append('questionTag', JSON.stringify(values?.studyMaterials || []));
            if (values.selectedSkills) {
                keysData.append('cognitive_skills', values.selectedSkills);
            }
            if (values.points !== null) {
                keysData.append('points', values.points);
            }
            keysData.append('courseCategory', values.courseCategory ? values.courseCategory : null);
            keysData.append('Tag', JSON.stringify(values.keyword ? values.keyword : null));

            if (values.questionType === "IQ") {
                const updatedObject = {
                    ...object,
                    keyType: values.keyType
                };
                keysData.append('keyObj', JSON.stringify(updatedObject));
            }
            else if (values.questionType === "MCQ" || values.questionType === "screenLevelMcq" || values.questionType === 'Chemistry' || values.questionType === 'Physics' || values.questionType === 'Biology' ) {
                const mcqdata = {
                    question: values.question,
                    mcqOptions: values.mcqOptions.map(option => option.option),
                    correctAnswer: values.mcqOptions.map(option => option.isCorrect),
                };
                keysData.append('mcqData', JSON.stringify(mcqdata));
            }
            const response = await adminServices.createQuestions(keysData);
            if (response.ok) {
                await getAllQuestions();

                // const newQuestion = {
                //     question_text: values.question,
                //     question_type: values.questionType,
                //     is_active: true,
                //     created_date: new Date().toISOString(),
                //     mcqOptions: values.mcqOptions,
                // };
                // setQuestionList(prevList => [newQuestion, ...prevList]);
                setOpenSnackbar(true);
                setSnackbarTitle(response.data.message);
                setOpenCreateMcq(false);
                setLoading(false);

            } else {
                console.error("Error:", response);
                setLoading(false);
            }
        } catch (error) {
            console.error("An error occurred:", error);
        }
    };

    const handleDeletOpen = (data) => {
        setDeleteId(data.id)
        setDeleteOpen(true)
    }

    const handleDelete = async () => {
        try {
            const data = await adminServices.deleteSATDetails(deleteId,questionType);
            if (data.ok) {
                if (data.data.message === 'Only created user can delete the Questions') {
                    setSnackbarTitle('Only created user can delete the Questions');
                    setOpenSnackbar(true)
                    setDeleteOpen(false)
                } 
                else{
                    setDeleteOpen(false)
                    await getAllQuestions(page, rowsPerPage, searchText, questionType);
                    setOpenSnackbar(true);
                    setSnackbarTitle("Question Deleted Successfully");
                }
               
            } else {
                setDeleteOpen(false);
                setOpenSnackbar(true);
                setSnackbarTitle(data.data.error);
            }
        } catch (error) {
            console.log(error);
        }
    }


    const handleEdit = (values) => {
        setNewData(values)
        setOpenEditMcq(true)
    }
    const handleEditCallBack = (values) => {
        const array = keyObjects;
        const object = {
            windows: {
                keyCode: values.combinedWindowKeys,
                keyName: values.combinedWindowKeyName,
            },
            mac: {
                keyCode: values.combinedMacKeys,
                keyName: values.combinedMacKeyName,
            },
            positionIndex: array.length,
        };
        array.push(object);
        handleEditCallBack1(object, values);
        console.log('object', object);

    }

    const handleEditCallBack1 = async (object, values) => {
        setLoading(true)
        const mergedArray = values.mcqOptions?.map((option, index) => ({
            option,
            isCorrect: values.curectAnswer[index] || false,
        }));

        try {
            const requestData = {
                question: values.question,
                level: values.level,
                questionType: values.questionType,
                explanation: values.explanation,
                points: values.points !== null ? values.points : undefined,
                courseCategory: values.courseCategory || undefined,
                Tag: values.courseCategory ? JSON.stringify(values.keyword) : JSON.stringify(values.keyword),
                cognitive_skills: values.selectedSkills,
                questionTag: values?.studyMaterials,

            };
            if (values.selectedSkills) {
                requestData.cognitive_skills = values.selectedSkills
            }
            if (values.questionType === "IQ") {
                const updatedObject = {
                    ...object,
                    keyType: values.keyType,
                };
                requestData.keyObj = JSON.stringify(updatedObject);
            }
            else if (values.questionType === "MCQ" || values.questionType === "screenLevelMcq" || values.questionType === 'Chemistry' || values.questionType === 'Physics' || values.questionType === 'Biology') {
                requestData.mcqData = {
                    question: values.question,
                    mcqOptions: mergedArray.map(option => option.option),
                    correctAnswer: mergedArray.map(option => option.isCorrect),
                };
            }
            const questionId = newData;
            const response = await adminServices.updateQuestion(questionId, requestData);
            if (response.ok) {
                setOpenSnackbar(true);
                setSnackbarTitle(response.data.message);
                setOpenEditMcq(false);
                setLoading(false);
                await getAllQuestions();
            } else {
                console.error("Error:", response);
                setLoading(false);
            }
        } catch (error) {
            console.error("An error occurred:", error);
        }
    };

    return (
        <>
            <div>
                <Page title="Mcq Question" >
                    <PageHeader pageTitle="MCQ Question" submodule="submodule" />
                    <Container maxWidth={false} sx={{ padding: '0 !important' }}>
                        <Grid container spacing={2}>
                            <Grid align="end" item xs={12}>
                                <FormControl size="small" sx={{ width: '150px', marginRight: '10px', height: '46px' }}>
                                    <InputLabel id="question-type-label" style={{ maxHeight: '46px' }}>Type</InputLabel>
                                    <Select
                                        sx={{
                                            textAlign: 'left',
                                            '& .MuiSelect-select': {
                                                padding: '8px 12px'
                                            }
                                        }}
                                        labelId="question-type-label"
                                        id="question-type-select"
                                        value={questionType}
                                        label="Type"
                                        onChange={handleTypeChange}
                                    >
                                        <MenuItem Item value="MCQ">MCQ</MenuItem>
                                        <MenuItem value="IQ">IQ</MenuItem>
                                        <MenuItem value="screenLevelMcq">Screen Level MCQ</MenuItem>
                                        <MenuItem value="Chemistry">Chemistry</MenuItem>
                                        <MenuItem value="Physics">Physics</MenuItem>
                                        <MenuItem value="Biology">Biology</MenuItem>
                                    </Select>
                                </FormControl>
                                <TextField
                                    variant="outlined"
                                    placeholder="Search questions"
                                    size="small"
                                    sx={{ width: '250px', height: '40px' }}
                                    InputProps={{
                                        startAdornment: (
                                            <InputAdornment position="start">
                                                <SearchIcon id="searchQuestionIconbtn" />
                                            </InputAdornment>
                                        ),
                                        sx: { height: '100%' },
                                    }}
                                    value={searchText}
                                    onChange={handleInputChange}
                                />
                                <Button
                                    style={{ marginLeft: '15px', height: '40px' }}
                                    id="createQuestionButton"
                                    variant="contained"
                                    color="primary"
                                    onClick={handleCreateMcqQuestion}
                                >
                                    Create Questions
                                </Button>

                            </Grid>
                            <TableContainer component={Paper}>
                                <Table>
                                    <TableHead>
                                        <TableRow>
                                            <TableCell>Name</TableCell>
                                            <TableCell>Type</TableCell>
                                            <TableCell>Status</TableCell>
                                            <TableCell>Author</TableCell>
                                            <TableCell>Date</TableCell>
                                            <TableCell>Action</TableCell>
                                        </TableRow>
                                    </TableHead>

                                    <TableBody>
                                        {loading && (
                                            <TableRow>
                                                <TableCell colSpan={5} style={{ textAlign: 'center' }}>
                                                    Loading...
                                                </TableCell>
                                            </TableRow>
                                        )}

                                        {!loading && questionList?.length === 0 && (
                                            <TableRow>
                                                <TableCell colSpan={5} style={{ textAlign: 'center' }}>
                                                    Sorry, there is no matching data to display
                                                </TableCell>
                                            </TableRow>
                                        )}
                                        {!loading && questionList && questionList.map((row, index) => (
                                            <TableRow key={index}>
                                                <TableCell dangerouslySetInnerHTML={{ __html: DOMPurify.sanitize(row.question_text) }} />
                                                <TableCell>{row.question_type}</TableCell>
                                                <TableCell style={{ color: row.is_active ? 'green' : 'red' }}>
                                                    {row.is_active ? 'Active' : 'Inactive'}
                                                </TableCell>
                                                <TableCell>{row?.firstname} {row?.lastname}</TableCell>
                                                <TableCell>{moment(row.created_date).isValid() ? moment(row.created_date).format('MM-DD-YYYY (h:mm A)') : 'Invalid Date'}
                                                </TableCell>
                                                <TableCell>
                                                    <IconButton id={`edit${row.id}`} color="primary" onClick={() => handleEdit(row.id)}>
                                                        <EditIcon />
                                                    </IconButton>
                                                    {/* <IconButton id={`delete${row.id}`} color="secondary" onClick={() =>handleDeletOpen(row)}>
                                                        <DeleteIcon />
                                                    </IconButton> */}
                                                    {row?.is_linked_to_assessment === false ?
                                                        <IconButton id={`delete${row.id}`} color="secondary" onClick={() => handleDeletOpen(row)}>
                                                            <DeleteIcon />
                                                        </IconButton>
                                                        :
                                                        <>
                                                            <Tooltip title="This question is part of NeetSource!" arrow>
                                                                <span>
                                                                    <IconButton id={`delete${row.id}`} color="secondary" disabled>
                                                                        <DeleteIcon />
                                                                    </IconButton>
                                                                </span>
                                                            </Tooltip>
                                                        </>

                                                    }
                                                </TableCell>
                                            </TableRow>
                                        ))}
                                    </TableBody>
                                </Table>
                            </TableContainer>



                            <Grid item xs="12" sm="12" md="12" lg="12" xl="12">
                                <div >
                                    <TablePagination
                                        component="div"
                                        count={maxCount || 0}
                                        page={page}
                                        onPageChange={handleChangePage}
                                        rowsPerPage={rowsPerPage}
                                        onRowsPerPageChange={handleChangeRowsPerPage}
                                        rowsPerPageOptions={[5, 10, 15, 20, 25]}
                                        sx={{ marginTop: 2 }}
                                    />
                                </div>
                            </Grid>
                        </Grid>
                    </Container>

                    <CreateMcqQuestionModel
                        open={openCreateMcq}
                        modelClose={() => setOpenCreateMcq(!openCreateMcq)}
                        title="Create Questions"
                        handleCreateMcq={handleSubmitForm}
                        loading={loading}
                        searchedDetails={searchedDetails}
                    />
                    <EditQuestionModule
                        open={openEditMcq}
                        modelClose={() => setOpenEditMcq(!openEditMcq)}
                        title="Edit Questions"
                        handleEditMcq={handleEditCallBack}
                        loading={loading}
                        searchedDetails={searchedDetails}
                        data={newData}
                    />
                </Page>
                <SnackBar open={openSnackbar} snackbarTitle={snackbarTitle} close={() => setOpenSnackbar(false)} />
            </div>
            <DeleteAlert
                open={deleteOpen}
                title="Are you sure you want delete this Question"
                confirm={handleDelete}
                close={() => setDeleteOpen(false)}
            />
        </>
    )
}
export default McqQuestion;