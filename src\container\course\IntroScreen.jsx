/* eslint-disable no-unused-vars */
/* eslint-disable arrow-body-style */
/* eslint-disable react/no-danger */
/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable react/prop-types */
import React, { useState, useEffect, useRef } from 'react';
import { Box, Typography } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { makeStyles } from '@mui/styles';
import Button from '@mui/material/Button';
import DOMPurify from "dompurify";
import 'katex/dist/katex.min.css';

const IntroScreen = ({ screens, activeStep, type, onClickCallBack, videoLoadCallback,widthChange, questionPanel, completefullScreen, fullScreen, collapsedScreen }) => {
  // console.log(screens,"screens");
  
  const classes = useStyles();
  const containerRef = useRef(null);
  videoLoadCallback(true);
  const horizontal = screens[activeStep]?.horizontalAlign?.code === 'MIDDLE' ? 'center' : 'flex-start';
  const vertical = screens[activeStep]?.verticalAlign?.code === 'CENTER' ? 'center' : 'start';
  const allActionsAreClicks = type?.type === 'simulation' && screens.every(item =>
    (item.type === 'ACTION' && (item.keyType === 'CLICK' || item.keyType === undefined)) ||
    item.type === 'INTRO' ||
    item.type === 'SUCCESS'
  );
  
  const hasMixedHotKeysAndClick = type?.type === 'simulation' && screens.some(item => item.type === 'ACTION' && (item.keyType === 'INDIVIDUAL_HOT_KEYS' || item.keyType === 'COMBINED_HOT_KEYS' || item.keyType === 'CODE')) &&
    screens.some(item => item.type === 'ACTION' && item.keyType === 'CLICK');
  const allActionsAreKeyStroke = screens.every(item =>
    (item.type === 'ACTION' && (item.keyType === 'INDIVIDUAL_HOT_KEYS' || item.keyType === 'COMBINED_HOT_KEYS' || item.keyType === undefined)) ||
    item.type === 'INTRO' ||
    item.type === 'SUCCESS'
  );
  const allActionsAreCode = type?.type === 'simulation' && screens.every(item =>
    (item.type === 'ACTION' ) ||
    item.type === 'INTRO' ||
    item.type === 'SUCCESS'
  );

  console.log(hasMixedHotKeysAndClick,"hasMixedHotKeysAndClick");
  console.log(allActionsAreKeyStroke,"allActionsAreKeyStroke");
  console.log(allActionsAreCode,"allActionsAreCode");
  
  
  

  const nextBtnElement = useRef();
  const [sfxPlayed, setsfxPlayed] = useState(false);
  const detectOS = () => {
    let platform;
    if (navigator.userAgentData) {
      platform = navigator.userAgentData.platform.toLowerCase();
    } else {
      const userAgent = navigator.userAgent.toLowerCase();
      if (userAgent.includes('win')) {
        platform = 'windows';
      } else if (userAgent.includes('mac')) {
        platform = 'mac';
      } else if (/android/.test(userAgent)) {
        platform = 'android';
      } else if (/iphone|ipad|ipod/.test(userAgent)) {
        platform = 'ios';
      } else {
        platform = 'unknown';
      }
    }
    if (platform.includes('mac')) return 'Mac';
    if (platform.includes('win')) return 'Windows';
    return 'Other';
  };
  const { t } = useTranslation('translation');

  useEffect(() => {
    const container = containerRef.current;
    if (container) {
      const elements = container.querySelectorAll('.editor-element');
      const baseWidth = 756;
      const baseHeight = 450;
      const effectiveWidth = questionPanel ? collapsedScreen : fullScreen;
      const effectiveHeight = container.offsetHeight;
      const scaleFactor = effectiveWidth / baseWidth;
      const scaleFactorHeight = effectiveHeight / baseHeight;
      const heightDifference = effectiveHeight - baseHeight;
      const decimalHeightValue = parseFloat(`${heightDifference.toString()[0]}.${heightDifference.toString()[1]}`) || 0;

      let variableValue = 0;
      if (effectiveWidth < 800) {
        variableValue = -1;
      } 
      else if (questionPanel&&effectiveWidth >= 800 && effectiveWidth < 850) {
        variableValue = -2;
      }
       else if (questionPanel&&effectiveWidth >= 850 && effectiveWidth < 900) {
        variableValue = -3;
      } else if (questionPanel&&effectiveWidth >= 900 && effectiveWidth < 950) {
        variableValue = -4;
      }
     else if (questionPanel&&effectiveWidth >= 950 && effectiveWidth < 1000) {
        variableValue = -5;
      }

       else if (questionPanel&&effectiveWidth >= 1000&& effectiveWidth < 1050) {
        variableValue = 3;
      }
        else if (!questionPanel&&effectiveWidth >=  800 && effectiveWidth < 1000) {
        variableValue = -3
      }
      else if (!questionPanel&&effectiveWidth >=  1000 && effectiveWidth < 1050) {
        variableValue = -3
      }
      else if (!questionPanel&&effectiveWidth >=  1050 && effectiveWidth < 1100) {
        variableValue = -5
      }
       else if (!questionPanel&&effectiveWidth >=  1100 && effectiveWidth < 1150) {
        variableValue = -6
      } else if (!questionPanel&&effectiveWidth >=  1150 && effectiveWidth < 1200) {
        variableValue = -7
      }
      //    else if (questionPanel&&effectiveWidth >= 1250 && effectiveWidth < 1400) {
      //   variableValue = 3.8;
      // }
          else if (!questionPanel&&effectiveWidth >= 1250 && effectiveWidth < 1400) {
        variableValue = -6
      }
      //  else if (effectiveWidth >= 1050 && effectiveWidth < 1200) {
      //   variableValue = -6;
      // }

      
      elements.forEach((el) => {
        const computedStyle = getComputedStyle(el);
        if (!el.dataset.originalWidth) {
          el.dataset.originalWidth = computedStyle.width;
          el.dataset.originalHeight = computedStyle.height;
          el.dataset.originalLeft = computedStyle.left;
          el.dataset.originalTop = computedStyle.top;
          el.dataset.originalFontSize = computedStyle.fontSize;
        }

        const originalWidth = parseFloat(el.dataset.originalWidth);
        const originalHeight = parseFloat(el.dataset.originalHeight);
        const originalLeft = parseFloat(el.dataset.originalLeft);
        const originalTop = parseFloat(el.dataset.originalTop);
        const originalFontSize = parseFloat(el.dataset.originalFontSize);

        let leftOffset
        if (questionPanel) {
            leftOffset = el.type === 'shape' ? -20 : 0;
        } else {
            leftOffset = 30;
        }

        // Apply scaled styles based on original values
        el.style.width = `${(originalWidth * scaleFactor)-10}px`;
        el.style.height = `${(originalHeight * scaleFactorHeight) - decimalHeightValue}px`;
        el.style.left = `${originalLeft * scaleFactor + leftOffset}px`;
        el.style.top = `${(originalTop * scaleFactorHeight) - decimalHeightValue + 10}px`;
        const scaledFontSize = (originalFontSize / baseWidth * effectiveWidth)+variableValue ;
        el.style.setProperty('font-size', `${scaledFontSize - 2.1}px`, 'important');
      });
    }
  }, [activeStep, questionPanel, fullScreen, collapsedScreen,screens]);

  // console.log(originalLeft * scaleFactor , originalLeft * (scaleFactor + 18), "value for only SShapes" )
  const handleKeyDownIntro = (event) => {

    if ((event.key === 'Enter')) {
      introBtnClickHandle("successbutton", event);
    }
    else {
      console.log("outside if condition", event.key, allActionsAreClicks)
    }
    if (event.key === ' ') {
      event.preventDefault();
    }
  };
  useEffect(() => {
    const addKeyListener = () => {
      window.addEventListener('keydown', handleKeyDownIntro);
    };
    const removeKeyListener = () => {
      window.removeEventListener('keydown', handleKeyDownIntro);
    };

    if (allActionsAreClicks === false) {
      addKeyListener();
    } else {
      removeKeyListener();
    }
    return () => {
      removeKeyListener();
    };
  }, [handleKeyDownIntro, allActionsAreClicks]);
  useEffect(() => {
    setTimeout(() => {
      setsfxPlayed(true);
    }, 3000);
  }, []);

  const os = detectOS();
  const keyText = os === 'Mac' ? 'return' : 'enter';


  const introBtnClickHandle = (e, event) => {
    onClickCallBack(e, event)
  }
  


  const htmlTemplate = screens[activeStep]?.html_template !== null && screens[activeStep]?.html_template !== "undefined" && screens[activeStep]?.html_template !== undefined &&
    JSON.parse(screens[activeStep].html_template);
  const positionGet = htmlTemplate?.position;
  // console.log(positionGet);

  const getAnimationClass = (position) => {
    switch (position) {
      case 'right-to-left':
        return 'scroll-text-right';
      case 'left-to-right':
        return 'scroll-text-left';
      case 'pop-up':
        return 'pop-up-animation';
      default:
        return 'delay-effect';

    }
  };

  if (screens && screens[activeStep]) {


    // eslint-disable-next-line no-unused-vars
    const jsonString = `{"design":{"counters":{"u_column":1,"u_row":1,"u_content_text":1},"body":{},"positionIs":"","position":"left-to-right"}}`;


    const parsedData = JSON.parse(jsonString);


    // eslint-disable-next-line no-unused-vars
    const { position } = parsedData.design;


  }

  const removeDollarSigns = (html) => {
    // return html.replace(/\$(?=\S)/, '');
    return html?.replace(/\$/g, '');
  };
  const formatOptionnew = (option) => {
    const sanitizedOption = DOMPurify.sanitize(option, {
      ALLOWED_TAGS: ['span', 'div', 'p', 'img', 'a', 'br'],
      ALLOWED_ATTR: ['href', 'src', 'alt', 'style'],
    });
    return sanitizedOption;
  };
  const formattedHtml = formatOptionnew(screens[activeStep]?.html_template !== null && screens[activeStep]?.html_template !== "undefined" && screens[activeStep]?.html_template !== undefined ?
    JSON.parse(screens[activeStep]?.html_template)?.html : "");
  const FinalHtml = removeDollarSigns(formattedHtml);

  useEffect(() => {
    if (window.MathJax) {
      window.MathJax.Hub.Queue(["Typeset", window.MathJax.Hub]);
    }
  }, [FinalHtml]);


  return (
    <div>
      <Box
        className={`${classes.gridContainer} ${getAnimationClass(positionGet)}`}
        sx={{
          height: '80vh',
          justifyContent: screens[activeStep]?.horizontalAlign.code === 'RIGHT' ? 'flex-end' : horizontal,
          alignItems: screens[activeStep]?.verticalAlign.code === 'BOTTOM' ? 'end' : vertical,
          margin: 0,
          // backgroundImage: screens[activeStep].templates ? `url(${screens[activeStep].templates})` : `url(${screens[activeStep].backgroundImg})`,
          ...(screens[activeStep]?.backgroundBase64
            ? {
              backgroundImage: screens.length > 0 ? `url(${screens[activeStep]?.backgroundBase64})` : '',
            }
            : {
            }),
        }}
      // style={{display: 'block'}}

      >
        {screens[activeStep]?.html_template !== null && screens[activeStep]?.html_template !== "undefined" && screens[activeStep]?.html_template !== undefined ? (

          <div ref={containerRef} style={{ width: '100%', height: '100%' }} dangerouslySetInnerHTML={{ __html: JSON.parse(screens[activeStep]?.html_template)?.html }} />

        ) : (
          <>
            {(type?.subCategory?.code !== 'PYTHON' && type?.subCategory?.code !== 'SEQUEL') && (screens[activeStep < screens.length ? activeStep : 0].description.trim().replace(/<[^>]*>/g, '').length > 0) && (screens.length > 0)
              && (screens[activeStep < screens.length ? activeStep : 0].description !== '') ? (
              <Box className={screens[activeStep].templates ? classes.introTemplate : classes.introContainer}>
                <div ref={containerRef} style={{ minHeight: 60, display: "flex", justifyContent: "center", alignItems: "center" }}>

                  <Typography
                    sx={{ fontSize: '1.125rem' }}
                    dangerouslySetInnerHTML={{
                      __html:
                        screens.length > 0
                          ? screens[activeStep < screens.length ? activeStep : 0].description
                          : '&nbsp;'
                    }}
                  />




                </div>

                {screens[activeStep].type === 'SUCCESS' && (
                  <div style={{ display: "flex", justifyContent: "center", alignItems: "center" }}>
                    <Button ref={nextBtnElement} variant="outlined" color="primary" onClick={() => introBtnClickHandle("successbutton")}>
                      {screens[activeStep].buttonLabel || 'okay'}

                    </Button>

                  </div>
                )}
              </Box>) :
              (type?.subCategory?.code === 'PYTHON' || type?.subCategory?.code === 'SEQUEL') &&
              (screens[activeStep < screens.length ? activeStep : 0].description.trim().replace(/<[^>]*>/g, '').length > 0) &&
              (screens.length > 0) &&
              //  (activeStep < screens.length) &&
              (screens[activeStep].description !== '') &&
              (screens[activeStep].templates !== null) && (
                <Box
                  // className={classes.introContainer}
                  className={screens[activeStep].templates ? classes.introTemplate : classes.introContainer}
                >
                  <div ref={containerRef} style={{ minHeight: 60, display: "flex", justifyContent: "center", alignItems: "center" }}>
                    <Typography
                      sx={{ fontSize: '1.125rem' }}
                      dangerouslySetInnerHTML={{
                        __html:
                          screens.length > 0
                            ? screens[activeStep < screens.length ? activeStep : 0].description
                            : '&nbsp;'
                      }}
                    />
                  </div>
                  {screens[activeStep].type === 'SUCCESS' && (
                    <div style={{ display: "flex", justifyContent: "center", alignItems: "center" }}>
                      <Button ref={nextBtnElement} variant="outlined" color="primary" onClick={introBtnClickHandle}>
                        {screens[activeStep].buttonLabel || 'okay'}

                      </Button>

                    </div>
                  )}
                </Box>
              )
            }
          </>
        )}





        {(type?.type !== 'video' || type?.type !== 'combination') && (
          allActionsAreClicks ? (
            <Button
              ref={nextBtnElement}
              variant="contained"
              className={classes.extroTxts}
              onClick={(event) => { introBtnClickHandle("successbuttonwithclick", event) }}
              sx={{
                color: "orange",
                backgroundColor: "rgba(255, 0, 0, 0)",
                fontSize: '0.675rem',
                position: 'absolute',
                bottom: '-35px',
                visibility: sfxPlayed ? 'visible' : 'hidden'
              }}
            >
              {t("click")}&nbsp;<span className={classes.highlightedText}>{t("here")}</span>&nbsp; {t("to continue")}
            </Button>
          ) : (hasMixedHotKeysAndClick || allActionsAreKeyStroke || allActionsAreCode) && (
            <Button
              ref={nextBtnElement}
              variant="contained"
              onClick={() => introBtnClickHandle("pressbutton")}
              className={classes.extroTxts}
              disabled
              sx={{
                color: "orange",
                backgroundColor: "rgba(255, 0, 0, 0)",
                fontSize: '0.675rem',
                position: 'absolute',
                bottom: '-35px',
                visibility: sfxPlayed ? 'visible' : 'hidden'
              }}
            >
              {t("Press")} &nbsp;<span className={classes.highlightedText}>{keyText}</span>&nbsp; {t("to continue")}
            </Button>
          )
        )
        }

        {(type?.type === 'video' || type?.type === 'combination')  && screens[activeStep]?.type !== "SUCCESS" && activeStep !== screens?.length - 1 && (
          <Button
            variant="contained"
            className={classes.extroTxts}
            sx={{
              color: "orange",
              backgroundColor: "rgba(255, 0, 0, 0)",
              fontSize: '0.675rem',
              position: 'absolute',
              bottom: '-35px',
              visibility: sfxPlayed ? 'visible' : 'hidden'
            }}
          >
            {t("Press")}&nbsp;<span className={classes.highlightedText}>{os === 'Mac' ? 'Return' :t("Enter")}</span>&nbsp; {t("to continue")}
          </Button>
        )
        }
        {(type?.type === 'video' || type?.type === 'combination') && screens[activeStep]?.type === "SUCCESS" && (
          <Button
            variant="contained"
            className={classes.extroTxts}
            sx={{
              color: "orange",
              backgroundColor: "rgba(255, 0, 0, 0)",
              fontSize: '0.675rem',
              position: 'absolute',
              bottom: '-35px',
              visibility: sfxPlayed ? 'visible' : 'hidden'
            }}
          >
            
            {t("Press")}&nbsp;<span className={classes.highlightedText}>{os === 'Mac' ? 'Return' :t("Enter")}</span>&nbsp; {t("to continue")}
          </Button>
        )
        }
      </Box>
    </div>
  );
}


const useStyles = makeStyles(() => ({

  backArrow: {
    color: 'black',
    backgroundColor: 'transparent',
    '&:hover': {
      backgroundColor: 'rgb(236,121,48)',
    },
  },
  backArrowEnabled: {
    color: 'white',
    backgroundColor: 'transparent',
    '&:hover': {
      backgroundColor: 'rgb(236,121,48)',
    },
  },
  forwardArrow: {
    color: 'black',
    backgroundColor: 'transparent',
    '&:hover': {
      backgroundColor: 'rgb(236,121,48)',
    },
  },
  forwardArrowEnabled: {
    color: 'white',
    backgroundColor: 'transparent',
    '&:hover': {
      backgroundColor: 'rgb(236,121,48)',
    },
  },

  alertAnimationone: {
    animation: 'alertone 1.5s infinite',
  },
  body: {
    color: '#fff',
    fontFamily: 'Nunito Semibold',
    textAlign: 'center',
  },
  content: {
    display: 'grid'
  },
  gridCell: {
    border: '1px solid transparent',
    cursor: 'pointer',
  },
  selectedButton: {
    background: '#3bbced',
    color: '#fff',
  },
  gridButton: {
    padding: '5px 10px 5px 10px',
    cursor: 'pointer',
    margin: '0 5px',
  },
  gutter: {
    marginBottom: '20px',
  },
  gridContainer: {
    // minHeight: '80vh',   // to match click base screen height due to arrow position get changes each time
    // height: '480px',
    // height: 'auto',
    width: '100%',
    backgroundSize: '100% 100% ',
    backgroundRepeat: 'no-repeat',
    // backgroundPosition: 'center',
    display: 'flex',
    position: 'relative',
  },
  extroTxt: {
    animation: '$pulse 1.5s infinite',
  },
  highlightedText: {
    animation: '$colorChange 1.5s infinite',
    color: 'orange',
    fontWeight: 'bold',
  },
  '@keyframes colorChange': {
    '0%': {
      color: 'orange',
    },
    '50%': {
      color: 'red',
    },
    '100%': {
      color: 'orange',
    },
  },
  introContainer: {
    minWidth: '250px',
    maxWidth: '600px',
    padding: '16px 0 16px',
    borderRadius: 8,
    backgroundColor: '#e7e7e7cf',
    overflow: 'hidden',
    margin: '3rem',
    // boxShadow: `4px 4px 8px 2px #a7a7a7`,
    boxShadow: '0px 2px 6px #00000029',
    // border: '2px solid #ddd',
  },
  introTemplate: {
    minWidth: '250px',
    maxWidth: '600px',
    padding: 16,
    paddingBottom: 20,
    borderRadius: 8,
    backgroundColor: 'transparent',
    margin: '3rem',
    // boxShadow: `4px 4px 8px 2px #a7a7a7`,
    boxShadow: '0px 2px 6px transparent',
    border: '2px solid transparent',
  }

}));
export default IntroScreen;