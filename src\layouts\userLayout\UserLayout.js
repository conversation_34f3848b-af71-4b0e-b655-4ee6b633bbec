/* eslint-disable no-unused-vars */
/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable react/jsx-key */
import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { Outlet, useNavigate,useLocation } from 'react-router-dom';
import classnames from 'classnames';


// material
import { makeStyles } from '@mui/styles';
import { styled } from '@mui/material/styles';
import { Box, Container, Stack, AppBar, Toolbar, Typography, List, ListItem, ListItemText,  ToggleButtonGroup, ToggleButton } from '@mui/material';
import { useSelector, useDispatch } from 'react-redux';

import Logo from '../../assets/logo/logo.png';
import AccountPopover from '../dashboard/AccountPopover';
import palette from '../../theme/palette';
import ThemeProvider from '../../theme';
import { setB2BPage } from '../../store/reducer';
import CourseApi from '../../services/users/courseApi'
import { getB2BCourseList } from '../../Redux/Action'

// ----------------------------------------------------------------------

const APPBAR_MOBILE = 64;
const APPBAR_DESKTOP = 68;

const RootStyle = styled(AppBar)(({ theme }) => ({
  //   boxShadow: 'none',
  background: 'rgb(253 253 253 / 71%);',
  backdropFilter: 'blur(6px)',
  WebkitBackdropFilter: 'blur(6px)', // Fix on Mobile
  boxShadow: '0px -30px 28px -3px #686464',
  borderBottom: '2px solid #00B673',

  //   backgroundColor: alpha(theme.palette.background.default, 0.72),
  [theme.breakpoints.up('lg')]: {
    //     width: `calc(100% - ${DRAWER_WIDTH + 1}px)`,
    width: '100%',
  },
}));

const ToolbarStyle = styled(Toolbar)(({ theme }) => ({
  minHeight: APPBAR_MOBILE,
  [theme.breakpoints.up('lg')]: {
    minHeight: APPBAR_DESKTOP,
    padding: theme.spacing(0, 0),
  },
}));

const MainRootStyle = styled('div')({
  display: 'flex',
  minHeight: '100%',
  overflow: 'hidden',
});

const MainStyle = styled('div')(({ theme }) => ({
  // flexGrow: 1,
  width: '100%',
  // overflow: 'auto',
  minHeight: '100%',
  background: '#FAFAFA',
  // paddingLeft: theme.spacing(2.5),
  // paddingRight: theme.spacing(2.5),
  // paddingTop: APP_BAR_MOBILE,
  // paddingBottom: theme.spacing(2),
  [theme.breakpoints.up('lg')]: {
    //   paddingTop: APP_BAR_DESKTOP - 8,
    // paddingLeft: theme.spacing(2),
    // paddingRight: theme.spacing(2),
  },
}));

// ----------------------------------------------------------------------

UserLayout.propTypes = {
  onOpenSidebar: PropTypes.func,
};

/* const LinkBtn = React.forwardRef((props, ref) => {
  return <NavLink to={props.to} {...props} innerRef={ref} />;
}); */

// eslint-disable-next-line no-unused-vars
export default function UserLayout({ onOpenSidebar }) {
  const classes = useStyles();
  const dispatch = useDispatch();
  const navigate = useNavigate();

  const location = useLocation()
  // console.log(location.pathname /app/SatOverview,"location");


  const [activeList, SetActiveList] = useState('Courses');
  const userInfo = useSelector((state) => state.userInfo);
  const navItem = [{ name: 'Courses', path: '/app/course' }];
  const allDetails = useSelector((state) => state);
  const [satDetails, setSatDetails] = useState([]);
  
  const [pageName, setPageName] = useState('paid');
  const [pageNameView, setPageNameView] = useState('');
  const [alignment, setAlignment] = useState('paid');
  const [categoryList, setCategoryList] = useState([]);

  useEffect(() => {
    if (allDetails?.courseListB2b) {
      const categories = [...new Set(allDetails.courseListB2b.map(item => item.category?.description))];
      setCategoryList(categories);
    }
  }, [allDetails?.courseListB2b]);

  React.useMemo(() => {
    setPageName(localStorage.getItem('selectedToggle'))
    setAlignment(localStorage.getItem('selectedToggle'))
  }, [localStorage.getItem('selectedToggle')]);
  
  // eslint-disable-next-line no-unused-vars
 
  


  useEffect(() => {
    localStorage.setItem('selectedToggle', alignment);
  }, [alignment]);
  
    // React.useEffect(async () => {
    //   const result = await CourseApi.getSATList()
    //   if(result.ok)
    //   setSatDetails(result.data)
    // }, [])

  // const handleChange = (event, newAlignment) => {
  //   dispatch(getB2BCourseList(userInfo.id,newAlignment))
  //   dispatch(setB2BPage(newAlignment));
    
  //   if (newAlignment !== null) {
  //     setAlignment(newAlignment);
  //     setPageName(newAlignment);
  //     localStorage.setItem('selectedToggle', newAlignment);

  //   }
  // };



  const handleChange = (event, newAlignment) => {    
    // dispatch(getB2BCourseList(userInfo.id,newAlignment))
    dispatch(setB2BPage(newAlignment));
    if (newAlignment !== null) {
      setAlignment(newAlignment);
      setPageName(newAlignment);
      localStorage.setItem('selectedToggle', newAlignment);
    }
  };

  const handleSatNavigate = () => {
    setAlignment('sat');
    localStorage.setItem('selectedToggle', 'sat');
    navigate('/app/sat');
  }

  const CallBack = (data) => {
    setPageNameView(data)
  }

  // Add this effect to initialize from localStorage on component mount
  useEffect(() => {
    const storedToggle = localStorage.getItem('selectedToggle');
    const searchParams = new URLSearchParams(location.search);
    const isFree = searchParams.get('free') === 'true';
    
    // URL parameter takes precedence
    if (location.pathname.includes('/app/course')) {
      if (isFree) {
        setAlignment('free');
        setPageName('free');
      } else if (storedToggle && !isFree) {
        setAlignment(storedToggle);
        setPageName(storedToggle);
      }
    } else if (location.pathname.includes('/app/sat')) {
      setAlignment('sat');
      setPageName('sat');
    } else if (storedToggle) {
      setAlignment(storedToggle);
      setPageName(storedToggle);
    }
  }, []);

    useEffect(() => {
    // Extract the path from the URL
    const path = location.pathname;
    const searchParams = new URLSearchParams(location.search);
    const isFree = searchParams.get('free') === 'true';
    
    if (path.includes('/app/sat')) {
      setAlignment('sat');
      setPageName('sat');
      localStorage.setItem('selectedToggle', 'sat');
    } else if (path.includes('/app/course')) {
      if (isFree) {
        setAlignment('free');
        setPageName('free');
        localStorage.setItem('selectedToggle', 'free');
      } else if (!isFree && !localStorage.getItem('selectedToggle')) {
  
        setAlignment('paid');
        setPageName('paid');
        localStorage.setItem('selectedToggle', 'paid');
      }
    }
  }, [location.pathname, location.search]);

  const PathDisable = location?.pathname.includes('/app/NeetSimulation')

  return (
    <>
      <ThemeProvider>
        <MainRootStyle>
          <RootStyle>
            <Container className={classes.container} sx={{
              '@media (min-width: 1536px)': {
                maxWidth: '100% !important' 
              }
            }}>
              <ToolbarStyle>
                <div className={classes.headerContainer}>
            
                  <div className={classes.logoContainer}>
                    <Box
                      disable = {PathDisable}
                      onClick={() => {
                        if (!PathDisable) {
                          navigate('/app/course');
                        }
                      }}
                      sx={{
                        ...(allDetails?.FromGeneralAssessment === true &&
                        (location.pathname.includes('/app/SatOverview') || location.pathname.includes('/app/NeetSimulation') || location.pathname.includes('/app/SATAssessment') || location.pathname.includes('/app/SATExamSimulation'))
                          ? {
                              cursor: 'unset',
                              pointerEvents: 'none',
                            }
                          : {
                              '&:hover': {
                                cursor: 'pointer',
                              },
                            }),
                      }}
                    >
                      <img 
                        src={Logo} 
                        width="110px" 
                        alt="logo" 
                        style={{
                          maxHeight: '48px',
                          width: 'auto',
                        }}
                      />
                    </Box>
                  </div>
                  
                 
                  <div className={classes.toggleContainer}>
                    {!(allDetails?.FromGeneralAssessment === true && location.pathname.includes('/app/SatOverview')
                    ) && location.pathname === '/app/course' && (
                      <ToggleButtonGroup
                        color="primary"
                        value={alignment}
                        exclusive
                        aria-label="Platform"
                        sx={{
                          borderRadius: '40px',
                          boxShadow: 'rgba(50, 50, 93, 0.25) 0px 6px 12px -2px, rgba(0, 0, 0, 0.3) 0px 3px 7px -3px',
                          padding: '4px',
                          marginRight: {lg:'10px', md: '5px', sm: '0px', xs: '0px'},
                          '& .MuiToggleButton-root': {
                            border: 'none',
                            padding: { xs: '6px 10px', sm: '8px 16px' },
                            color: '#bdbdbd',
                            whiteSpace: 'nowrap',
                            fontSize: { xs: '0.8rem', sm: '0.9rem' },
                            '&.Mui-selected': {
                              color: '#FE7000',
                              backgroundColor: 'rgb(247 163 47 / 15%)',
                              borderRadius: '40px',
                              '&:hover': {
                                backgroundColor: 'rgb(255 193 107 / 15%)',
                                borderRadius: '40px',
                              }
                            },
                            '&:hover': {
                              backgroundColor: 'rgb(255 193 107 / 15%)',
                              borderRadius: '40px',
                            }
                          }
                        }}
                      >
                        <ToggleButton 
                          value="paid"
                          onChange={handleChange}
                        >
                          Allotted
                        </ToggleButton>
                        <ToggleButton 
                       
                          value="free"
                          disabled={!allDetails?.courseListB2b.some(course => course.is_free)}
                          onChange={handleChange} 
                          style={{
                              border: 'none',
                              }}>
                          Free
                          {/* {console.log( allDetails?.courseListB2b.length , allDetails?.courseListB2b?.filter(course => course.is_free)?.length, "courseFree1111")} */}
                         </ToggleButton>
                        {satDetails && satDetails.length !== 0 && (
                          <ToggleButton 
                            value="sat"
                            onClick={handleSatNavigate}
                          >
                            SAT              
                          </ToggleButton>
                        )}
                      </ToggleButtonGroup>
                    )}
                  </div>
                  
              
                  <div className={classes.userContainer}>
                    {allDetails?.FromGeneralAssessment === true && location.pathname.includes('/app/SatOverview') ? null :
                      <List sx={{ 
                        display: 'flex',
                        '@media (max-width: 600px)': {
                          padding: '0',
                        }
                      }}>
                        {(userInfo.role === 'AUTH_USER' && !location.pathname.includes('/app/NeetSimulation')) &&
                          navItem.map((res) => (
                            <ListItem
                              key={res.name}
                              disableRipple
                              onClick={() => {
                                navigate(res.path);
                                SetActiveList(res.name);
                              }}
                              className={classnames(classes.linkText, {
                                [classes.linkTextActive]: activeList === res.name,
                              })}
                              sx={{
                                padding: { xs: '0 5px', sm: '0 10px' },
                                '@media (max-width: 600px)': {
                                  minWidth: 'auto',
                                }
                              }}
                            >
                              <ListItemText
                                sx={{
                                  '.css-6h10z0-MuiTypography-root': {
                                    fontSize: { xs: '0.85rem', sm: '0.9rem' },
                                    fontWeight: '400',
                                    letterSpacing: '0.4px',
                                  },
                                }}
                                style={{ color: '#969da3' }}
                              >
                                {userInfo.name}
                              </ListItemText>
                            </ListItem>
                          ))}
                      </List>
                    }
                    {userInfo.role === 'CONTENT_WRITER' && (
                      <Typography
                        variant="subtitle1"
                        color="#969da3 !important"
                        sx={{
                          fontSize: { xs: '0.85rem', sm: '1rem' },
                          fontWeight: '400',
                          letterSpacing: '0.4px',
                          color: '#969da3 !important',
                          marginRight: { xs: '5px', sm: '10px' },
                        }}
                      >
                        {userInfo.name}
                      </Typography>
                    )}

                    {allDetails?.FromGeneralAssessment === true && (location.pathname.includes('/app/SatOverview') || location.pathname.includes('/app/NeetSimulation')) ? null :
                      <Stack 
                        direction="row" 
                        alignItems="center" 
                        spacing={{ xs: 0.5, sm: 1 }} 
                        ml={0}
                      >
                        <AccountPopover
                          coming={'B2B'}
                          CallBack={CallBack}
                        />
                      </Stack>
                    }
                  </div>
                </div>
              </ToolbarStyle>
            </Container>
          </RootStyle>

          <MainStyle>
            <Outlet />
          </MainStyle>
        </MainRootStyle>
      </ThemeProvider>
    </>
  );
}

const useStyles = makeStyles(() => ({
  linkText: {
    color: '#000',
    '&:hover': {
      backgroundColor: 'transparent',
    },
  },
  linkTextActive: {
    color: palette.primary.main,
    fontSize: '1.2rem',
  },
  container: {
    maxWidth: '1536px',
    '@media (min-width: 1536px)': {
      maxWidth: '100% !important',
    },
    '@media (min-width: 1201px)': {
      padding: '0px 2rem',
    },
    '@media (min-width: 601px) and (max-width: 1200px)': {
      padding: '0px 1rem',
    },
    '@media (max-width: 600px)': {
      padding: '0px 0.5rem',
    },
  },
  // Add new styles for responsive header
  headerContainer: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    width: '100%',
    '@media (max-width: 768px)': {
      justifyContent: 'space-between',
    },
  },
  logoContainer: {
    display: 'flex',
    alignItems: 'center',
    '@media (max-width: 600px)': {
      marginRight: '10px',
    },
  },
  toggleContainer: {
    display: 'flex',
    flex: 2,
    justifyContent: 'end',


    alignItems: 'center',
    '@media (max-width: 768px)': {
      marginLeft: 'auto',
    },
  },
  userContainer: {
    display: 'flex',
    alignItems: 'center',
    '@media (max-width: 600px)': {
      marginLeft: '5px',
    },
  },
}));
