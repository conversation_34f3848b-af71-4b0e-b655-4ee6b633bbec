/* eslint-disable no-lonely-if */
/* eslint-disable no-restricted-globals */
/* eslint-disable no-else-return */
/* eslint-disable one-var */
/* eslint-disable object-shorthand */
/* eslint-disable react/prop-types */
/* eslint-disable prefer-const */
/* eslint-disable no-unused-expressions */
/* eslint-disable no-undef */
/* eslint-disable arrow-body-style */
/* eslint-disable no-nested-ternary */
/* eslint-disable react/jsx-key */
/* eslint-disable react/button-has-type */
/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable no-unused-vars */
import React, { useEffect, useState, useMemo, useRef } from 'react';
import { makeStyles } from '@mui/styles';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate, useLocation, Link, useParams } from 'react-router-dom';
import { Swiper, SwiperSlide } from 'swiper/react';
import 'swiper/swiper-bundle.min.css';
import './index.css';
import DOMPurify from 'dompurify';
import ErrorIcon from '@mui/icons-material/Error';

import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import { Button, Typography, Card, Backdrop, CircularProgress, CardContent, Grid, Box, Breadcrumbs, Tabs, Tab, Paper, Table, TableHead, TableRow, TableCell, TableBody, Accordion, AccordionSummary, AccordionDetails, LinearProgress, Select, MenuItem, FormControl, InputLabel, Chip, Stack, Avatar, TableContainer, Tooltip } from '@mui/material';
import { PieChart, BarChart } from '@mui/x-charts';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import CancelIcon from '@mui/icons-material/Cancel';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import TrackChangesIcon from '@mui/icons-material/TrackChanges';
import HourglassBottomIcon from '@mui/icons-material/HourglassBottom';
import Divider from '@mui/material/Divider';
import { getRecommendedAssessment, getIndividualNEETAssessment } from '../../../Redux/Action'
import helper from '../../../utils/helper';
import trialAndStripeSubscriptionService from '../../../services/trialAndStripeSubscriptionService';
import { openSnackbar, ComingFrom, FromGeneralAssessmentView } from '../../../store/reducer';
import adminServices from '../../../services/adminServices';
import Maintenance from '../../../assets/Images/maintenance.png'
import LottieLoading from '../../../components/LottieLoading';

const totalHours = '12';
const getSubjectStats = (data) => {
    const stats = {};
    data.forEach(q => {
        if (!stats[q.subject]) {
            stats[q.subject] = { total: 0, correct: 0, time: 0 };
        }
        stats[q.subject].total += 1;
        if (q.userOption === q.correctOption) stats[q.subject].correct += 1;
        stats[q.subject].time += q.timeTaken;
    });
    return stats;
}
const NeeAssessmentCourseDetails = () => {
    const location = useLocation()
    const { t } = useTranslation('translation');
    // const neetAssessDetails = location.state?.details;
    const [neetAssessDetails, setNeetAssessDetails] = useState(() => {
        if (location.state?.details) {
            return location.state.details;
        }
        try {
            const stored = localStorage.getItem('neet_assess_details');
            return stored ? JSON.parse(stored) : null;
        } catch {
            return null;
        }
    });

    // const subjectStats = getSubjectStats(dummyData);
    const classes = useStyles();
    const dispatch = useDispatch();
    const navigate = useNavigate();
    const allcourseDetails = useSelector((state) => state);
    const userId = useSelector((state) => state.userInfo && state.userInfo.id);
    const { userInfo } = useSelector((state) => state);
    const [currency, setCurrency] = useState(userInfo?.currencyType ? userInfo?.currencyType.code : 'USD');
    const [tabIndex, setTabIndex] = useState(0);
    const [assessmentDetails, setAssessmentDetails] = useState('');
    const [neetAnalysis, setNeetAnalysis] = useState(null);
    const [attemptedDate, setAttemptedDate] = useState(null);
    const [attemptedCount, setAttemptedCount] = useState(0);
    const [parseArray, setParseArray] = useState('');
    const { id } = useParams();
    const [assessmentId, setAssessmentId] = useState(null);
    // const [assessments, setAssessments] = useState([]);
    const [intfrequency, setintfrequency] = React.useState(1);
    const [viewPrice, setViewPrice] = useState(0);
    const [newValueIs, setNewValueIs] = useState(0);
    const [seriesNb, setSeriesNb] = React.useState(2);
    const [itemNb, setItemNb] = React.useState(5);
    const [skipAnimation, setSkipAnimation] = React.useState(false);
    const [value, setValue] = useState(0);
    const [topChapterPerSubject, setTopChapterPerSubject] = useState([]);
    const [weakChapterPerSubject, setWeakChapterPerSubject] = useState([]);
    const [xAxisFontSize, setXAxisFontSize] = useState(12);
    const [currentPageBio, setCurrentPageBio] = useState(0);
    const [currentPageChem, setCurrentPageChem] = useState(0);
    const [currentPagePhy, setCurrentPagePhy] = useState(0);

    const [currentPagePhysicsQuestions, setCurrentPagePhysicsQuestions] = useState(0);
    const [currentPageBiologyQuestions, setCurrentPageBiologyQuestions] = useState(0);
    const [currentPageChemistryQuestions, setCurrentPageChemistryQuestions] = useState(0);
    const itemsPerPage = 6;
    const questionsPerPage = 15;
    const inputRefs = useRef([]);

    const [isShow, setIsShow] = useState(false);
    const startIndexBio = currentPageBio * itemsPerPage;
    const endIndexBio = startIndexBio + itemsPerPage;
    const startIndexChem = currentPageChem * itemsPerPage;
    const endIndexChem = startIndexChem + itemsPerPage;
    const startIndexPhy = currentPagePhy * itemsPerPage;
    const endIndexPhy = startIndexPhy + itemsPerPage;


    const startIndexPhysicsQuestions = currentPagePhysicsQuestions * questionsPerPage;
    const endIndexPhysicsQuestions = startIndexPhysicsQuestions + questionsPerPage;
    const startIndexBiologyQuestions = currentPageBiologyQuestions * questionsPerPage;
    const endIndexBiologyQuestions = startIndexBiologyQuestions + questionsPerPage;
    const startIndexChemistryQuestions = currentPageChemistryQuestions * questionsPerPage;
    const endIndexChemistryQuestions = startIndexChemistryQuestions + questionsPerPage;
    const [initialLoading, setInitialLoading] = useState(true);
    const [expanded, setExpanded] = useState(false);

    const handleAccordionChange = (panel, index) => (event, isExpanded) => {
        setIsShow(false);
        if (isExpanded) {
            if (expanded && expanded !== panel) {
                setExpanded(panel);
                setTimeout(() => {
                    const element = inputRefs.current[index];
                    if (element) {
                        element.scrollIntoView({
                            behavior: 'smooth',
                            block: 'center',
                            inline: 'nearest'
                        });
                    }
                }, 300);
            } else {
                setExpanded(panel);
                setTimeout(() => {
                    const element = inputRefs.current[index];
                    if (element) {
                        element.scrollIntoView({
                            behavior: 'smooth',
                            block: 'center',
                            inline: 'nearest'
                        });
                    }
                }, 300);
            }
        } else {
            setExpanded(false);
        }
    };

    useEffect(() => {
        const timer = setTimeout(() => {
            setInitialLoading(false);
        }, 2500);

        return () => clearTimeout(timer);
    }, []);
    
    const handleChange = (event, newValue) => {
        setValue(newValue);
    };
    React.useMemo(() => {
        const filteredList = assessmentDetails?.stripe_pricing_list?.filter(data =>
            allcourseDetails?.currency === 'USD'
                ? data?.related_currency_type_lk === 2
                : data?.related_currency_type_lk === 1
        );
        let displayPrice;

        if (filteredList && filteredList?.length > 0 && allcourseDetails?.currency === 'USD') {
            displayPrice = `$${filteredList[0].value}`;
        } else if (filteredList && filteredList?.length > 0 && allcourseDetails?.currency === 'INR') {
            displayPrice = `₹${filteredList[0].value}`;
        } else if (filteredList && filteredList?.length === 0) {
            displayPrice = 'NAN'
        }
        setViewPrice(displayPrice)

    }, [allcourseDetails])

    useEffect(() => {
        const handleResize = () => {
            if (window.innerWidth < 600) {
                setXAxisFontSize(8);
            } else if (window.innerWidth < 900) {
                setXAxisFontSize(9);
            } else if (window.innerWidth < 1400) {
                setXAxisFontSize(11);
            } else {
                setXAxisFontSize(12);
            }
        };

        handleResize();
        window.addEventListener("resize", handleResize);

        return () => window.removeEventListener("resize", handleResize);
    }, []);

    useEffect(() => {
        if (neetAssessDetails?.id) {
            setAssessmentId(neetAssessDetails?.id);
        }
    }, [neetAssessDetails?.id]);

    useEffect(() => {
        if (assessmentId) {
            getNEETAnalyticsDetails(assessmentId, attemptedCount);
        }
    }, [attemptedCount, assessmentId])


    useEffect(() => {
        if (assessmentId) {
            dispatch(getIndividualNEETAssessment(assessmentId, allcourseDetails?.userInfo.id))
            dispatch(getRecommendedAssessment(allcourseDetails?.userInfo.id))
            dispatch(FromGeneralAssessmentView(false))
        }
    }, [assessmentId])

    useEffect(() => {
        window.history.scrollRestoration = "manual";
        window.scrollTo(0, 0);
    }, []);

    const physicsChapters = useMemo(() => {
        if (!assessmentDetails) return [];
        return assessmentDetails.chapters.filter(chapter =>
            chapter?.types?.includes("Physics")
        );
    }, [assessmentDetails]);

    const chemistryChapters = useMemo(() => {
        if (!assessmentDetails) return [];
        return assessmentDetails.chapters.filter(chapter =>
            chapter?.types?.includes("Chemistry")
        );
    }, [assessmentDetails]);

    const biologyChapters = useMemo(() => {
        if (!assessmentDetails) return [];
        return assessmentDetails.chapters.filter(chapter =>
            chapter?.types?.includes("Biology")
        );
    }, [assessmentDetails]);


    useEffect(() => {
        setAssessmentDetails(allcourseDetails && allcourseDetails?.GetNEETAssessmentIndividual[0])
    }, [allcourseDetails])

    const nbsp = '\u00A0'; // 4 spaces
    const assessments = [
        { id: 1, title: "Attempt 1", name: `${neetAssessDetails?.title}${nbsp}1` },
        { id: 2, title: "Attempt 2", name: `${neetAssessDetails?.title}${nbsp}2` },
        { id: 3, title: "Attempt 3", name: `${neetAssessDetails?.title}${nbsp}3` },
        { id: 4, title: "Attempt 4", name: `${neetAssessDetails?.title}${nbsp}4` },
        { id: 5, title: "Attempt 5", name: `${neetAssessDetails?.title}${nbsp}5` },
    ];

    const formatTimeTimer = (seconds) => {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = seconds % 60;
        return `${minutes.toString().padStart(1, '0')} Minutes`;

    };
    const handleSwitchAssessment = (id) => {
        setAssessmentId(id)
        window.scrollTo({
            top: 0,
            left: 0,
            bottom: 0,
            behavior: 'smooth',
        });
    }

    const subjectColorMap = { Chemistry: '#fc9f06', Biology: '#52c28c', Physics: '#437bfc' };

    const handleNavigateDetailsMyLearning = () => {
        dispatch(ComingFrom("MyLearning"))

        if (userInfo?.role === 'AUTH_USER') {
            navigate("/app/subscribe")
        }
        else {
            navigate("/auth/subscribe")
        }
    }

    const highlightedSection = assessmentDetails?.highlighted_section;

    const handleStartTest = (id) => {

        // setFromIndex(id)
        // handleClickTrialButton(view?.assessment_details?.subscriptionplanid, true, view?.assessment_details?.id)
        // setComingFrom('start')
        // setAssessmentView(true)
    }

    useMemo(() => {
        if (highlightedSection !== undefined && highlightedSection !== null) {
            const cleanedStr = highlightedSection?.replace(/[{}"]/g, '').split(',')
            const formattedOutput = cleanedStr?.join('\n');

            const data = formattedOutput?.split('\n').map((text) => ({ text }))
            setParseArray(data)
        }
    }, [highlightedSection])

    const handleStartTestAssessment = (index) => {
        if (userInfo?.role === 'AUTH_USER') {
            navigate("/app/NeetSimulation", {
                state: {
                    ...location.state,
                    index: index
                }
            });
        } else {
            navigate("/auth/NeetSimulation", {
                state: {
                    ...location.state,
                    index: index
                }
            });
        }
    };

    const handleClickTrialButton = async (planId, enrollmentType, assessmentId) => {
        try {
            const res = await trialAndStripeSubscriptionService.postTrialAssessmentEnrollmentDetails(
                JSON.stringify({ planId, enrollmentType, assessmentId, authUserId: userInfo.id })
            );
            if (res.ok) {
                const data = {
                    id: location.state?.id,
                    from: location.state?.from
                }
                navigate("/auth/AssessmentOverview", { state: data })
            }
        } catch (error) {
            console.log(error);
        }
    };
    useEffect(() => {
        const rawAttemptedDate = location.state?.attemptedDate;
        if (rawAttemptedDate) {
            const onlyDate = rawAttemptedDate.includes('T')
                ? rawAttemptedDate.split('T')[0]
                : rawAttemptedDate.slice(0, 10);

            const formattedDate = formatDateToDDMMYYYY(onlyDate);
            setAttemptedDate(formattedDate || 'No Attempts');
        } else {
            AttemptedDate(attemptedCount);
        }
    }, []);

    const AttemptedDate = (attemptedCount) => {
        const selectedAttempt = assessmentDetails?.user_result?.[attemptedCount];
        if (selectedAttempt && selectedAttempt?.date_completed) {
            const dateOnly = selectedAttempt?.date_completed?.split('T')[0];
            const formattedDate = formatDateToDDMMYYYY(dateOnly);
            setAttemptedDate(formattedDate);
        } else {
            setAttemptedDate('No Attempts');
        }
    };

    const formatDateToDDMMYYYY = (dateString) => {
        if (!dateString) return null;
        const [year, month, day] = dateString.split('-');
        return `${day}-${month}-${year}`;
    };

    const resetPagination = () => {
        setCurrentPageBio(0);
        setCurrentPageChem(0);
        setCurrentPagePhy(0);
        setCurrentPagePhysicsQuestions(0);
        setCurrentPageBiologyQuestions(0);
        setCurrentPageChemistryQuestions(0);
    };

    const handleTabChange = (event, newValue) => {
        setTabIndex(newValue);
        resetPagination();
        if (newValue === 1) {
            AttemptedDate(attemptedCount);
        }
    };

    const baseTabs = [
        <Tab key="overview" label="Assessment Overview" />,
        <Tab key="analytics" label="Overall Analytics" />,
    ];

    const extraTabs = [
        <Tab key="phy" label="Physics" />,
        <Tab key="bio" label="Biology" />,
        <Tab key="chem" label="Chemistry" />,

    ];


    // const pieSeries = [{
    //     data: Object.entries(subjectStats).map(([subject, stats]) => ({
    //         label: subject,
    //         value: stats.correct
    //     }))
    // }];

    function TabPanel({ children, value, index }) {
        return (
            value === index && (
                <Box p={2}>
                    <Typography>{children}</Typography>
                </Box>
            )
        );
    }


    const highlightScope = {
        highlight: 'series',
        fade: 'global',
    };
    const series = [
        {
            label: 'Biology',
            data: [
                1145, 1214, 975, 2266, 1768, 2341, 747, 1282, 1780, 1766, 2115, 1720, 1057,
                2000, 1716, 2253, 619, 1626, 1209, 1786,
            ],
            color: '#52c28c',
            chapters: 26
        },
        {
            label: 'Chemistry',
            data: [
                2362, 2254, 1962, 1336, 586, 1069, 2194, 1629, 2173, 2031, 1757, 862, 2446,
                910, 2430, 2300, 805, 1835, 1684, 2197,
            ],
            color: '#fc9f06',
            chapters: 18
        },
        {
            label: 'Physics',
            data: [
                2423, 2210, 764, 1879, 1478, 1373, 1891, 2171, 620, 1269, 724, 1707, 1188,
                1879, 626, 1635, 2177, 516, 1793, 1598,
            ],
            color: '#437bfc',
            chapters: 12
        },
    ].map((s) => ({ ...s, highlightScope }));

    function CustomTabPanel(props) {
        const { children, value, index, ...other } = props;

        return (
            <div
                role="tabpanel"
                hidden={value !== index}
                id={`simple-tabpanel-${index}`}
                aria-labelledby={`simple-tab-${index}`}
                {...other}
            >
                {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
            </div>
        );
    }

    function a11yProps(index) {
        return {
            id: `simple-tab-${index}`,
            'aria-controls': `simple-tabpanel-${index}`,
        };
    }

    const getNEETAnalyticsDetails = async (assessmentId, index) => {
        try {
            const result = await adminServices.getNEETAnalyticsDetails(userId, assessmentId, index);
            if (result.ok) {
                setNeetAnalysis(result.data?.result);
            }

        } catch (error) {
            console.error("Error fetching analysis:", error);
        }
    }

    const handleAnalysisButton = (attemptIndex) => {
        setAttemptedCount(attemptIndex);
        AttemptedDate(attemptIndex);
        setTabIndex(1);
        resetPagination();
    }

    const handleAttempts = (attemptIndex) => {
        setAttemptedCount(attemptIndex);
        AttemptedDate(attemptIndex);
        resetPagination();
    };


    // const getChapterStats = (subjectData = []) => {
    //         const grouped = {};

    //         subjectData.forEach(q => {
    //             const chapterId = q.chapter_id;

    //             if (!grouped[chapterId]) {
    //                 grouped[chapterId] = {
    //                     chapterName: q.chapter_details?.module_name || "Unnamed Chapter",
    //                     correctCount: 0,
    //                     totalAnswered: 0,
    //                     totalTime: 0,
    //                     hasAnswered: false,
    //                     totalQuestionsInAssessment: 0
    //                 };
    //             }
    //              grouped[chapterId].totalQuestionsInAssessment += 1;

    //             if (q.response !== null) {
    //                 grouped[chapterId].totalAnswered += 1;
    //                 grouped[chapterId].hasAnswered = true;

    //                 if (q.response === 'correct') {
    //                     grouped[chapterId].correctCount += 1;
    //                 }

    //                 grouped[chapterId].totalTime += parseFloat(q.time_taken || 0);
    //             }
    //         });

    //         return Object.values(grouped).map(chapter => ({
    //             chapterName: chapter?.chapterName,
    //             correct: chapter?.hasAnswered ? chapter?.correctCount : 0,
    //             totalAnswered: chapter?.hasAnswered ? chapter?.totalAnswered : 0,
    //             accuracy: chapter?.hasAnswered
    //                 ? `${((chapter?.correctCount / chapter?.totalAnswered) * 100).toFixed(0)}%`
    //                 : 0,
    //             totalQuestions: chapter?.totalQuestionsInAssessment,
    //             avgTime: chapter?.hasAnswered
    //                 ? (chapter?.totalTime / chapter?.totalAnswered).toFixed(1)
    //                 : 0
    //         }));
    //     };


    const getChapterStats = (subjectData = []) => {
        const grouped = {};

        subjectData.forEach(q => {
            (q.chapters || []).forEach(chapter => {
                const chapterId = chapter.chapter_id;

                if (!grouped[chapterId]) {
                    grouped[chapterId] = {
                        chapterName: chapter?.module_name || "Unnamed Chapter",
                        correctCount: 0,
                        totalAnswered: 0,
                        totalTime: 0,
                        hasAnswered: false,
                        totalQuestionsInAssessment: 0
                    };
                }

                grouped[chapterId].totalQuestionsInAssessment += 1;

                if (q.response !== null) {
                    grouped[chapterId].totalAnswered += 1;
                    grouped[chapterId].hasAnswered = true;

                    if (q.response === "correct") {
                        grouped[chapterId].correctCount += 1;
                    }

                    grouped[chapterId].totalTime += parseFloat(q.time_taken || 0);
                }
            });
        });

        return Object.values(grouped).map(chapter => ({
            chapterName: chapter?.chapterName,
            correct: chapter?.hasAnswered ? chapter?.correctCount : 0,
            totalAnswered: chapter?.hasAnswered ? chapter?.totalAnswered : 0,
            accuracy: chapter?.hasAnswered
                ? `${((chapter?.correctCount / chapter?.totalAnswered) * 100).toFixed(0)}%`
                : "0%",
            totalQuestions: chapter?.totalQuestionsInAssessment,
            avgTime: chapter?.hasAnswered
                ? (chapter?.totalTime / chapter?.totalAnswered).toFixed(1)
                : 0
        }));
    };
    const biologyStats = getChapterStats(neetAnalysis?.biology);

    const chemistryStats = getChapterStats(neetAnalysis?.chemistry);
    const physicsStats = getChapterStats(neetAnalysis?.physics);



    const totalPhysicsQuestions = neetAnalysis?.physics?.length;
    const totalChemistryQuestions = neetAnalysis?.chemistry?.length;
    const totalBiologyQuestions = neetAnalysis?.biology?.length;

    // top and weak chapters from all 3 subjects
    const bioTagged = biologyStats?.map(ch => ({ ...ch, subject: 'Biology' }));
    const chemistryTagged = chemistryStats?.map(ch => ({ ...ch, subject: 'Chemistry' }));
    const physicsTagged = physicsStats?.map(ch => ({ ...ch, subject: 'Physics' }));

    const allChapters = [...bioTagged, ...chemistryTagged, ...physicsTagged];

    const sortedByAccuracy = [...allChapters].sort((a, b) => b.accuracy - a.accuracy);

    const TopChapters = sortedByAccuracy.slice(0, 3);
    const WeakChapters = sortedByAccuracy.slice(-3);

    // top and weak chapters from individual 3 subjects

    const getChaptersByAccuracy = (chapterList, n = 1, sortOrder = 'desc') => {
        const validChapters = chapterList.filter(ch => {
            const accuracyNum = parseFloat(ch.accuracy);
            return !isNaN(accuracyNum) && ch.totalAnswered > 0;
        });

        const sorted = [...validChapters].sort((a, b) => {
            const accA = parseFloat(a.accuracy);
            const accB = parseFloat(b.accuracy);

            if (accA === accB) {
                const timeA = parseFloat(a.avgTime);
                const timeB = parseFloat(b.avgTime);

                if (sortOrder === 'desc') {
                    return timeA - timeB;
                } else {
                    return timeB - timeA;
                }
            }

            if (sortOrder === 'asc') {
                return accA - accB;
            } else {
                return accB - accA;
            }
        });

        return sorted.slice(0, n);
    };

    const topBiologyChapter = getChaptersByAccuracy(bioTagged, 1, 'desc');
    const topChemistryChapter = getChaptersByAccuracy(chemistryTagged, 1, 'desc');
    const topPhysicsChapter = getChaptersByAccuracy(physicsTagged, 1, 'desc');

    const highestAccuracyChaptersPerSubject = [
        ...topBiologyChapter,
        ...topChemistryChapter,
        ...topPhysicsChapter
    ].sort((a, b) => parseFloat(b.accuracy) - parseFloat(a.accuracy));

    const weakestBiologyChapter = getChaptersByAccuracy(bioTagged, 1, 'asc');
    const weakestChemistryChapter = getChaptersByAccuracy(chemistryTagged, 1, 'asc');
    const weakestPhysicsChapter = getChaptersByAccuracy(physicsTagged, 1, 'asc');

    const weakestAccuracyChaptersPerSubject = [
        ...weakestBiologyChapter,
        ...weakestChemistryChapter,
        ...weakestPhysicsChapter
    ].sort((a, b) => parseFloat(a.accuracy) - parseFloat(b.accuracy));


    useEffect(() => {
        if (location.state?.showAnalytics) {
            setTabIndex(1);


            if (location.state?.attemptIndex !== undefined) {
                setAttemptedCount(location.state.attemptIndex);

            }
            const newState = { ...location.state };
            delete newState.showAnalytics;
            delete newState.attemptIndex;

            window.history.replaceState(
                { state: newState },
                '',
                window.location.pathname + window.location.search
            );
        } else {
            setTabIndex(0);
            setAttemptedCount(0);
        }
    }, [location.key]);

    const formatTimeTimerDisplay = (seconds) => {
        const roundedMinutes = Math.floor((seconds % 3600) / 60);
        const roundedSeconds = (seconds % 60).toFixed(1);
        return `${roundedMinutes} min ${roundedSeconds} sec`;
    };





    const prepareBarChartData = (chapterStats) => {
        const sortedChapterStats = chapterStats.slice().sort((a, b) => a.chapterName.localeCompare(b.chapterName));
        const chapterNames = sortedChapterStats.map(stat => stat.chapterName);

        const avgTimes = sortedChapterStats.map(stat => {
            const seconds = parseFloat(stat.avgTime);

            // let value, unit;

            // if (seconds < 60) {
            //     value = seconds.toFixed(1);
            //     unit = 'sec';
            // } else if (seconds < 3600) {
            //     value = (seconds / 60).toFixed(1);
            //     unit = 'min';
            // } else {
            //     value = (seconds / 3600).toFixed(1);
            //     unit = 'hr';
            // }

            const value = seconds.toFixed(1);
            const unit = 'sec';


            return {
                value: parseFloat(value),
                label: `${value} ${unit}`,
            };
        });

        return {
            series: [{ data: avgTimes.map(item => item.value) }],
            xAxisLabels: chapterNames,
            tooltipLabels: avgTimes.map(item => item.label),
        };
    };

    const toggleSection = () => {
        setIsShow(!isShow);
    }

    const sanitizeChartData = (chartData) => {
        const { series, xAxisLabels } = chartData;

        const cleanedData = series[0].data.map((val) =>
            // eslint-disable-next-line no-restricted-globals
            val !== null && !isNaN(val) ? parseFloat(val) : null
        );

        const cleanedTooltipLabels = cleanedData.map((val) => {
            if (val === null) return 'N/A';
            if (val < 60) return `${val} sec`;
            if (val < 3600) return `${(val / 60).toFixed(1)} min`;
            return `${(val / 3600).toFixed(1)} hr`;
        });

        return {
            series: [{ data: cleanedData }],
            xAxisLabels,
            tooltipLabels: cleanedTooltipLabels,
        };
    };



    // Physics
    const physicsChartDataEmpty = prepareBarChartData(physicsStats, '#437bfc');
    const physicsChartData = sanitizeChartData(physicsChartDataEmpty);
    // const totalAccuracy = physicsStats?.reduce((sum, chapter, index) => {
    //     const accStr = chapter?.accuracy;
    //     if (typeof accStr === 'string' && accStr.includes('%')) {
    //         const accNum = parseFloat(accStr.replace('%', ''));
    //         return sum + (isNaN(accNum) ? 0 : accNum);
    //     } else {
    //         console.warn(`Invalid accuracy at index ${index}:`, accStr);
    //         return sum;
    //     }
    // }, 0);

    const totalCorrectPhysics = physicsStats?.reduce((sum, chapter) => {
        return sum + (chapter?.correct || 0);
    }, 0);
    const totalIncorrectPhysics = physicsStats?.reduce((sum, chapter) => {
        const correct = chapter?.correct || 0;
        const total = chapter?.totalAnswered || 0;
        return sum + Math.max(0, total - correct);
    }, 0);
    const totalAnsweredPhysics = physicsStats?.reduce((sum, chapter) => {
        return sum + (chapter?.totalAnswered || 0);
    }, 0);
    const averageAccuracyPhysics = totalAnsweredPhysics > 0 ? (totalCorrectPhysics / totalAnsweredPhysics) * 100 : 0;
    const totalMarksPhysics = physicsStats?.reduce((sum, chapter) => {
        const correct = chapter?.correct || 0;
        const total = chapter?.totalAnswered || 0;
        const incorrect = total - correct;
        return sum + ((4 * correct) - (1 * incorrect));
    }, 0);



    // Chemistry
    const chemistryChartDataEmpty = prepareBarChartData(chemistryStats, '#fc9f06');
    const chemistryChartData = sanitizeChartData(chemistryChartDataEmpty);
    // const totalAccuracyChemistry = chemistryStats?.reduce((sum, chapter, index) => {
    //     const accStrChem = chapter?.accuracy;
    //     if (typeof accStrChem === 'string' && accStrChem.includes('%')) {
    //         const accNum1 = parseFloat(accStrChem.replace('%', ''));
    //         return sum + (isNaN(accNum1) ? 0 : accNum1);
    //     } else {
    //         console.warn(`Invalid accuracy at index ${index}:`, accStrChem);
    //         return sum;
    //     }
    // }, 0);

    const totalCorrectChemistry = chemistryStats?.reduce((sum, chapter) => {
        return sum + (chapter?.correct || 0);
    }, 0);
    const totalIncorrectChemistry = chemistryStats?.reduce((sum, chapter) => {
        const correct = chapter?.correct || 0;
        const total = chapter?.totalAnswered || 0;
        return sum + Math.max(0, total - correct);
    }, 0);
    const totalAnsweredChemistry = chemistryStats?.reduce((sum, chapter) => {
        return sum + (chapter?.totalAnswered || 0);
    }, 0);
    const averageAccuracyChemistry = totalAnsweredChemistry > 0 ? (totalCorrectChemistry / totalAnsweredChemistry) * 100 : 0;
    const totalMarksChemistry = chemistryStats?.reduce((sum, chapter) => {
        const correct = chapter?.correct || 0;
        const total = chapter?.totalAnswered || 0;
        const incorrect = total - correct;
        return sum + ((4 * correct) - (1 * incorrect));
    }, 0);


    // Biology
    const biologyChartDataEmpty = prepareBarChartData(biologyStats, '#52c28c');
    const biologyChartData = sanitizeChartData(biologyChartDataEmpty);

    // const totalAccuracyBiology = biologyStats?.reduce((sum, chapter, index) => {
    //     const accStrBio = chapter?.accuracy;
    //     if (typeof accStrBio === 'string' && accStrBio.includes('%')) {
    //         const accNum2 = parseFloat(accStrBio.replace('%', ''));
    //         return sum + (isNaN(accNum2) ? 0 : accNum2);
    //     } else {
    //         console.warn(`Invalid accuracy at index ${index}:`, accStrBio);
    //         return sum;
    //     }
    // }, 0);

    const totalCorrectBiology = biologyStats?.reduce((sum, chapter) => {
        return sum + (chapter?.correct || 0);
    }, 0);

    const totalIncorrectBiology = biologyStats?.reduce((sum, chapter) => {
        const correct = chapter?.correct || 0;
        const total = chapter?.totalAnswered || 0;
        return sum + Math.max(0, total - correct);
    }, 0);
    const totalAnsweredBiology = biologyStats?.reduce((sum, chapter) => {
        return sum + (chapter?.totalAnswered || 0);
    }, 0);
    const averageAccuracyBiology = totalAnsweredBiology > 0 ? (totalCorrectBiology / totalAnsweredBiology) * 100 : 0;
    const totalMarksBiology = biologyStats?.reduce((sum, chapter) => {
        const correct = chapter?.correct || 0;
        const total = chapter?.totalAnswered || 0;
        const incorrect = total - correct;
        return sum + ((4 * correct) - (1 * incorrect));
    }, 0);


    const subjectStats = {
        Physics: {
            averageAccuracy: averageAccuracyPhysics,
            color: '#437bfc',
        },
        Chemistry: {
            averageAccuracy: averageAccuracyChemistry,
            color: '#fc9f06',
        },
        Biology: {
            averageAccuracy: averageAccuracyBiology,
            color: '#52c28c',
        },
    };

    const overallAccuracy =
        // averageAccuracyPhysics !== 0 && averageAccuracyChemistry !== 0 && averageAccuracyBiology !== 0
        //     ? 
        (averageAccuracyPhysics + averageAccuracyChemistry + averageAccuracyBiology) / 3
    // : 0; 



    const calculateTotalTimeSpent = (subjectData) => {
        if (!subjectData || !Array.isArray(subjectData)) {
            return 0;
        }
        const totalSeconds = subjectData.reduce((sum, item) => {
            const timeInSeconds = parseFloat(item.time_taken) || 0;
            return sum + timeInSeconds;
        }, 0);

        return totalSeconds;
    };
    // ===========

    const totalPhysicsTimeInSeconds = calculateTotalTimeSpent(neetAnalysis?.physics);
    const physicsMinutes = Math.floor(totalPhysicsTimeInSeconds / 60);
    const physicsSeconds = Math.floor(totalPhysicsTimeInSeconds % 60);

    let formattedPhysicsTime = '';
    if (physicsMinutes > 0) {
        formattedPhysicsTime += `${physicsMinutes}min `;
    }
    if (physicsSeconds > 0 || (physicsMinutes === 0 && physicsSeconds === 0)) {
        formattedPhysicsTime += `${physicsSeconds}s`;
    }
    formattedPhysicsTime = formattedPhysicsTime.trim();

    const totalChemistryTimeInSeconds = calculateTotalTimeSpent(neetAnalysis?.chemistry);
    const chemistryMinutes = Math.floor(totalChemistryTimeInSeconds / 60);
    const chemistrySeconds = Math.floor(totalChemistryTimeInSeconds % 60);

    let formattedChemistryTime = '';
    if (chemistryMinutes > 0) {
        formattedChemistryTime += `${chemistryMinutes}min `;
    }
    if (chemistrySeconds > 0 || (chemistryMinutes === 0 && chemistrySeconds === 0)) {
        formattedChemistryTime += `${chemistrySeconds}s`;
    }
    formattedChemistryTime = formattedChemistryTime.trim();

    const totalBiologyTimeInSeconds = calculateTotalTimeSpent(neetAnalysis?.biology);
    const biologyMinutes = Math.floor(totalBiologyTimeInSeconds / 60);
    const biologySeconds = Math.floor(totalBiologyTimeInSeconds % 60);

    let formattedBiologyTime = '';
    if (biologyMinutes > 0) {
        formattedBiologyTime += `${biologyMinutes}min `;
    }
    if (biologySeconds > 0 || (biologyMinutes === 0 && biologySeconds === 0)) {
        formattedBiologyTime += `${biologySeconds}s`;
    }
    formattedBiologyTime = formattedBiologyTime.trim();

    const totalOverallTimeInSeconds = totalPhysicsTimeInSeconds + totalChemistryTimeInSeconds + totalBiologyTimeInSeconds;

    const overallHours = Math.floor(totalOverallTimeInSeconds / 3600);
    const overallRemainingSecondsAfterHours = totalOverallTimeInSeconds % 3600;
    const overallMinutes = Math.floor(overallRemainingSecondsAfterHours / 60);
    const overallSeconds = Math.floor(overallRemainingSecondsAfterHours % 60);

    let formattedOverallTime = '';
    if (overallHours > 0) {
        formattedOverallTime += `${overallHours}hr `;
    }
    if (overallMinutes > 0) {
        formattedOverallTime += `${overallMinutes}min `;
    }
    if (overallSeconds > 0 || (overallHours === 0 && overallMinutes === 0 && overallSeconds === 0)) {
        formattedOverallTime += `${overallSeconds}s`;
    }
    formattedOverallTime = formattedOverallTime.trim();


    const subjectColors = {
        Physics: '#007bff',
        Chemistry: '#fd7e14',
        Biology: '#20c997',
    };

    const TimeSpentOnSubject = [
        {
            label: 'Biology',
            rawSeconds: totalBiologyTimeInSeconds,
            displayTime: formattedBiologyTime,
            color: subjectColors.Biology || '#20c997'
        },
        {
            label: 'Chemistry',
            rawSeconds: totalChemistryTimeInSeconds,
            displayTime: formattedChemistryTime,
            color: subjectColors.Chemistry || '#fd7e14'
        },
        {
            label: 'Physics',
            rawSeconds: totalPhysicsTimeInSeconds,
            displayTime: formattedPhysicsTime,
            color: subjectColors.Physics || '#007bff'
        },
    ];

    const totalOverallSeconds = TimeSpentOnSubject.reduce((sum, subject) => sum + subject.rawSeconds, 0);
    const totalHoursForPercentage = totalOverallSeconds / 3600;

    const formattedXAxisLabelsBio = biologyChartData.xAxisLabels.map((label) => {
        if (label.length <= 20) return label;

        const words = label.split(' ');

        if (words.length === 1) {
            return `${label.slice(0, 20)}\n${label.slice(20)}`;
        }

        const midpoint = Math.floor(words.length / 3);
        const firstLine = words.slice(0, midpoint).join(' ');
        let secondLine = words.slice(midpoint).join(' ');
        if (secondLine.length > 10) {
            secondLine = `${secondLine.slice(0, 15)}...`;
        }
        return `${firstLine}\n${secondLine}`;
    });


    const paginatedLabelsBio = formattedXAxisLabelsBio.slice(startIndexBio, endIndexBio);
    const paginatedDataBio = biologyChartData.series[0]?.data.slice(startIndexBio, endIndexBio);
    const paginatedTooltipsBio = biologyChartData.tooltipLabels.slice(startIndexBio, endIndexBio);

    const formattedXAxisLabelsChem = chemistryChartData.xAxisLabels.map((label) => {
        if (label.length <= 20) return label;

        const words = label.split(' ');

        if (words.length === 1) {
            return `${label.slice(0, 20)}\n${label.slice(20)}`;
        }

        const midpoint = Math.floor(words.length / 3);
        const firstLine = words.slice(0, midpoint).join(' ');
        let secondLine = words.slice(midpoint).join(' ');
        if (secondLine.length > 15) {
            secondLine = `${secondLine.slice(0, 15)}...`;
        }
        return `${firstLine}\n${secondLine}`;
    });

    const paginatedLabelsChem = formattedXAxisLabelsChem.slice(startIndexChem, endIndexChem);
    const paginatedDataChem = chemistryChartData.series[0]?.data.slice(startIndexChem, endIndexChem);
    const paginatedTooltipsChem = chemistryChartData.tooltipLabels.slice(startIndexChem, endIndexChem);
    const formattedXAxisLabelsPhy = physicsChartData.xAxisLabels.map((label) => {
        if (label.length <= 20) return label;

        const words = label.split(' ');

        if (words.length === 1) {
            return `${label.slice(0, 20)}\n${label.slice(20)}`;
        }

        const midpoint = Math.floor(words.length / 2);
        const firstLine = words.slice(0, midpoint).join(' ');
        let secondLine = words.slice(midpoint).join(' ');
        if (secondLine.length > 10) {
            secondLine = `${secondLine.slice(0, 15)}...`;
        }
        return `${firstLine}\n${secondLine}`;
    });


    const paginatedLabelsPhy = formattedXAxisLabelsPhy.slice(startIndexPhy, endIndexPhy);
    const paginatedDataPhy = physicsChartData.series[0]?.data.slice(startIndexPhy, endIndexPhy);
    const paginatedTooltipsPhy = physicsChartData.tooltipLabels.slice(startIndexPhy, endIndexPhy);

    const handleNavigation = () => {
        const comingfrom = "Neetsrc"
        if (userInfo?.role === 'AUTH_USER') {
            navigate('/app/course', { state: comingfrom });
        }
        else {
            navigate('/auth/subscribe', { state: comingfrom });
        }
    }

    useEffect(() => {
        window.addEventListener('popstate', (event) => {
            handleNavigation()
        });
    }, [])
    /* Added by Radhika  BEGIN */
    const from = assessmentDetails?.user_message?.fromDate;
    const to = assessmentDetails?.user_message?.toDate;

    const renderMaintenanceView = () => {
        return (
            <Box>
                <Box sx={{ margin: '100px auto 15px', width: '93%' }}>
                    <Breadcrumbs
                        aria-label="breadcrumb"
                        sx={{
                            padding: '15px 0 0 0',
                            display: 'flex',
                            justifyContent: 'space-between',
                            alignItems: 'center',
                        }}
                        separator=">"
                    >
                        <button
                            style={{
                                cursor: 'pointer',
                                textDecoration: 'none',
                                border: 'none',
                                background: 'none',
                                color: '#0000ee',
                                fontSize: '16px',
                                fontWeight: '500',
                            }}
                            onClick={handleNavigation}
                        >
                            NEET Assessment
                        </button>
                        <Typography color="black">NEET Assessment Overview</Typography>
                    </Breadcrumbs>
                </Box>

                <Box
                    style={{
                        height: '60vh',
                        display: 'flex',
                        flexDirection: 'column',
                        justifyContent: 'center',
                        alignItems: 'center',
                    }}
                >
                    <img
                        style={{ maxWidth: '350px', marginBottom: '30px' }}
                        src={Maintenance}
                        alt="Under Maintenance"
                    />
                    <Typography variant="h6" style={{ color: '#FE780F', fontSize: '30px' }}>
                        Your Learning Might Have to Wait!
                    </Typography>
                    <Typography variant="h6">
                        This Assessment is under maintenance from {from} - {to}. We’ll be back shortly!
                    </Typography>
                </Box>
            </Box>
        );
    };

    if (initialLoading || !assessmentDetails) {
        return (
            <Backdrop
                sx={{
                    color: '#fff',
                    zIndex: (theme) => theme.zIndex.drawer + 1,
                    backgroundColor: 'rgba(255, 255, 255, 0.9)'
                }}
                // eslint-disable-next-line react/jsx-boolean-value
                open={true}
            >
                <Box
                    display="flex"
                    flexDirection="column"
                    alignItems="center"
                    justifyContent="center"
                >
                    <CircularProgress color="primary" size={60} thickness={4} />
                    <Typography variant="h6" sx={{ mt: 2, color: 'text.primary' }}>
                        Loading Assessment...
                    </Typography>
                </Box>
            </Backdrop>
        )
    }

    if (assessmentDetails?.on_maintenance) {
        return renderMaintenanceView();
    }

    return (

        <Box sx={{ width: '92%', margin: 'auto' }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Typography variant="h4" fontWeight="bold">Neet Assessment</Typography>
                <Typography variant="body2" color="textSecondary">Neet Overview</Typography>
            </Box>

            <Box sx={{ marginTop: '60px', }}>
                <Breadcrumbs
                    aria-label="breadcrumb"
                    sx={{
                        padding: '15px',
                        paddingLeft: '0px',
                        paddingBottom: '0px',
                        paddingTop: '0px',
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                    }}
                    separator=">"
                >
                    {/* {location.state?.from === 'NEET' ? */}
                    {/* <Link style={{
                            cursor: 'pointer',
                            color: "#212B36 !important",
                            textDecoration: 'none',
                            "&:hover": {
                                textDecoration: 'underline',
                            }
                        }}comingfrom
                            underline="hover"
                            color="#212B36"
                            // onClick={handleNavigation}
                            to="/auth/subscribe"
                        >
                            {t("NEET Assessment")}
                        </Link> */}

                    <button style={{
                        cursor: 'pointer',
                        textDecoration: 'none',
                        border: 'none',

                        background: 'none',
                        color: '#0000ee',
                        fontSize: '16px',
                        fontWeight: '500'
                    }}
                        // underline="hover"
                        color="#212B36"
                        // to="/auth/AssessmentCourseDetails"
                        onClick={handleNavigation}
                    >
                        {t("NEET Assessment")}
                    </button>



                    <Typography color="text.primary">
                        {t("Assessment Overview")}
                    </Typography>

                </Breadcrumbs>
            </Box>

            <Box sx={{ marginTop: '20px', }}>
                <Card sx={{
                    mx: "auto", boxShadow: 3, borderRadius: '10px',
                    border: '1px solid #dfdede'
                }}>
                    <Grid sx={{ padding: '15px' }} container spacing={2}>
                        <Grid item xs={12} sm={3} sx={{ paddingRight: '15px' }}>
                            <Box
                                component="img"
                                src={neetAssessDetails?.image_name}
                                // src="https://s3-alpha-sig.figma.com/img/0656/9cc3/1739478f82289ef1c25b236830e244e3?Expires=1739145600&Key-Pair-Id=APKAQ4GOSFWCW27IBOMQ&Signature=pe8zQWB3DSA4lwK-pjWJIzsvBsrp7n0t9g1~fbnrnkp26hsEgLKwq92N8CR1BNA4qwvokjUo-hlpT3HbZtrraFjNWvLwGIPsuMPCPVfygNlwWNdcpQX3mgAe9YTp2C~w7SUllRbrNK0R1TIVK9sjllitNOydMM4Z8LKmoZIR3AuGj6KwKljFN9Lz~UlpGmL2xaYcpSbVnvHIdzbou0cJCxQbZlBhz-JLElqzA~mgjq33SY5rcscFRNXPC4NSDCuYn4XOJj2Uj44LG5-L1umDc0opJRIatNBXmHDEVkHdpyeO2g9WYrouwFwQMZtTKvRb4guXbeJ~C6bk9ZL31kmY5g__"
                                alt="Assessment"
                                sx={{ width: "100%", height: "220px", borderRadius: 2 }}
                            />
                        </Grid>

                        <Grid item xs={12} sm={9} sx={{ padding: 0, paddingRight: '15px' }}>
                            <CardContent sx={{
                                padding: 0, height: '100%', paddingBottom: '0px !important', display: 'flex',
                                flexDirection: 'column'
                            }}>
                                <Typography variant="h5" fontWeight="bold">
                                    {neetAssessDetails && neetAssessDetails?.title}
                                </Typography>

                                <Typography variant="body2" color="text.secondary" dangerouslySetInnerHTML={{ __html: DOMPurify.sanitize(assessmentDetails && assessmentDetails?.short_description) }} sx={{ mb: 2, flex: '1 !important' }} />
                                {/* {allcourseDetails?.SubscribedAssessment.map((item) => item.assessmentDetails[0].id === assessmentDetails?.id) && */}
                                <Typography >
                                    <span style={{
                                        textDecoration: '',
                                        marginRight: '5px',
                                        background: '#ddd',
                                        padding: '4px 8px',
                                        borderRadius: '5px'
                                    }}>
                                        Price:
                                    </span>
                                    <span style={{
                                        textDecoration: 'line-through',
                                        marginRight: '5px',
                                        background: '#ddd',
                                        padding: '4px 8px',
                                        borderRadius: '5px'
                                    }}>
                                        ₹ 1000
                                    </span>
                                    <strong style={{
                                        background: '#ddd',
                                        padding: '4px 8px',
                                        borderRadius: '5px'
                                    }}>
                                        Free
                                    </strong>
                                </Typography>
                                {/* // } */}
                                <Box className="cardShowEven">

                                    <Box sx={{ flex: 1 }}>

                                        <Typography className={classes.badgeShower} variant="span" sx={{ mt: 1 }}>
                                            Number of Questions: 180
                                            {/* {neetAssessDetails && neetAssessDetails?.module_details[3]?.maxMarks} */}
                                        </Typography>
                                        <Typography className={classes.badgeShower} variant="span" sx={{ mt: 1 }}>
                                            Time: 3 Hours
                                            {/* {formatTimeTimer(neetAssessDetails && neetAssessDetails?.module_details[3]?.maxMarks)} */}
                                        </Typography>

                                        {/* {allcourseDetails?.SubscribedAssessment.map((item) => item.assessmentDetails[0].id === assessmentDetails?.id) &&
                                            <Typography className={classes.badgeShower} variant="span" sx={{ mt: 1 }}>
                                                Enrolled
                                            </Typography>} */}

                                        {/* {neetAssessDetails?.isPaid !== true && neetAssessDetails?.is_free === true && */}
                                        {/* } */}
                                    </Box>

                                </Box>
                            </CardContent>
                        </Grid>
                    </Grid>
                </Card>
            </Box>
            <Box sx={{display: 'flex',justifyContent: 'space-between',marginTop: '15px',marginBottom: '0',
    flexDirection: {xs: 'column-reverse',sm: 'column-reverse', md: 'row',},}} >
                <Tabs
                    value={tabIndex}
                    onChange={handleTabChange}
                    variant="scrollable"
                    scrollButtons="auto"
                    TabIndicatorProps={{
                        style: {
                            backgroundColor: '#5c7fc1',
                            height: '3px',
                        },
                    }}
                    sx={{
                        justifyContent: 'left', mb: 0,
                        '& .MuiTab-root': {
                            color: '#555',
                        },
                        '& .Mui-selected': {
                            color: '#5c7fc1 !important',
                            fontWeight: 'bold',
                        },
                    }}>
                    {baseTabs}
                    {tabIndex >= 1 && extraTabs}


                </Tabs>


                {(tabIndex === 1 || tabIndex === 2 || tabIndex === 3 || tabIndex === 4) &&
                    <>
                        <FormControl fullWidth sx={{ width: 190, position: 'relative' }} variant="outlined">
                            <InputLabel id="attempts" style={{ top: '-24px', fontSize: '13px', transform: 'translate(12px, 15px)' }}>Attempts</InputLabel>
                            <Select labelId="attempts-label" id="attempts-select" value={attemptedCount}
                                onChange={(e) => handleAttempts(e.target.value)} label="Attempts" >
                                <MenuItem value="0">Attempt 1</MenuItem>
                                <MenuItem value="1">Attempt 2</MenuItem>
                                <MenuItem value="2">Attempt 3</MenuItem>
                                <MenuItem value="3">Attempt 4</MenuItem>
                                <MenuItem value="4">Attempt 5</MenuItem>
                            </Select>

                        </FormControl>
                    </>
                }
            </Box>
            {(tabIndex === 1 || tabIndex === 2 || tabIndex === 3 || tabIndex === 4) &&
                (<Typography variant="" sx={{ display: 'block', textAlign: 'end', marginBottom: tabIndex === 1 ? '0px' : '7px' }}>
                    Attempted on : {attemptedDate && attemptedDate}
                </Typography>
                )}
            {tabIndex === 0 &&
                <>
                    <Box xs={12} sm={12} md={10} className="cardShowEvens">
                        <Box xs={12} sm={12} md={12}
                            sx={{ marginRight: '10px', boxShadow: 'none', flex: 2, }} >
                            {assessmentDetails?.chapters?.length > 0 ? (
                                <Box display="flex" flexDirection="column" gap={2} mt={0}>

                                    <TableContainer component={Paper}
                                        sx={{ borderRadius: '12px', overflowY: 'hidden', boxShadow: 1, border: '1px solid #111', overflowX: { xs: 'auto', sm: 'auto', md: 'hidden' } }} >

                                        <Table>
                                            <thead>
                                                <tr style={{ borderBottom: '2px solid rgb(108 108 108)', padding: '5px 10px', textAlign: 'start' }}>
                                                    <th style={{ border: '1px solid #c1c1c1', padding: '5px 10px', textAlign: 'start', width: '33.33%' }}>Physics</th>
                                                    <th style={{ border: '1px solid #c1c1c1', padding: '5px 10px', textAlign: 'start', width: '33.33%' }}>Chemistry</th>
                                                    <th style={{ border: '1px solid #c1c1c1', padding: '5px 10px', textAlign: 'start', width: '33.33%' }}>Biology</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {Array.from({ length: Math.max(physicsChapters?.length, chemistryChapters?.length, biologyChapters?.length) }).map((yes, index) => (
                                                    <tr key={index} style={{ border: '1px solid #c1c1c1', padding: '10px' }}>
                                                        <td style={{ border: '1px solid #c1c1c1', padding: '10px' }}>
                                                            {physicsChapters[index]?.name || physicsChapters[index]?.title || '-'}
                                                        </td>
                                                        <td style={{ border: '1px solid #c1c1c1', padding: '10px' }}>
                                                            {chemistryChapters[index]?.name || chemistryChapters[index]?.title || '-'}
                                                        </td>
                                                        <td style={{ border: '1px solid #c1c1c1', padding: '10px' }}>
                                                            {biologyChapters[index]?.name || biologyChapters[index]?.title || '-'}
                                                        </td>
                                                    </tr>
                                                ))}
                                            </tbody>
                                        </Table>
                                    </TableContainer>
                                </Box>
                            ) : (
                                <Typography variant="body2" color="textSecondary">
                                    <TableContainer component={Paper}
                                        sx={{ borderRadius: '12px', overflow: 'hidden', boxShadow: 1, border: '1px solid #111' }} >
                                        <Table>
                                            <tr style={{ border: '1px solid #c1c1c1', padding: '5px 10px', textAlign: 'start' }}>
                                                <th style={{ border: '1px solid #c1c1c1', padding: '5px 10px', textAlign: 'start' }}>Subject</th>
                                                <th style={{ border: '1px solid #c1c1c1', padding: '5px 10px', textAlign: 'start' }}>Chapters</th>
                                            </tr>
                                            {series.map((s) => (

                                                <tr style={{ border: '1px solid #c1c1c1', padding: '10px' }}>
                                                    <td style={{ border: '1px solid #c1c1c1', padding: '10px' }}>{s.label}</td>
                                                    <td style={{ border: '1px solid #c1c1c1', padding: '10px' }}>{s.chapters}</td>
                                                </tr>
                                            ))}
                                        </Table>
                                    </TableContainer>
                                </Typography>
                            )}

                        </Box>



                    </Box>
                    <Box xs={12} sm={12} md={12}>
                        {/* <Typography variant="h6" fontWeight="bold" sx={{ mb: 2, mt: 2 }}> Highlights </Typography> */}
                        <Box sx={{ width: '100%', typography: 'body1', marginTop: '24px' }}>
                            <Card sx={{
                                mx: "auto", boxShadow: 3, borderRadius: '10px',
                                border: '1px solid #dfdede'
                            }}>
                                <Box className="121" sx={{ width: '100%' }}>
                                    <Box className="122" sx={{ borderBottom: 1, borderColor: 'divider', height: "40px" }}>
                                        <Tabs value={value} onChange={handleChange} className="tabBasic" aria-label="basic tabs example"
                                            sx={{
                                                "& .MuiTabs-indicator": {
                                                    backgroundColor: "#ff0004",
                                                },
                                                "& .MuiTab-root": {
                                                    color: "#424242 !important",
                                                },
                                                "& .Mui-selected": {
                                                    color: "#424242 !important",
                                                },
                                            }}
                                        >
                                            <Tab label={"All"} {...a11yProps(0)} />
                                            <Tab label={"Not Started"} {...a11yProps(1)} />
                                            <Tab label={"Completed"} {...a11yProps(2)} />
                                        </Tabs>
                                    </Box>

                                    <CustomTabPanel value={value} index={0} style={{ padding: '10px 10px 20px' }}>
                                        <Box sx={{ display: "flex", flexDirection: "column", gap: 2, padding: '10px 10px 20px' }}>
                                            {assessments && assessments?.length > 0 && assessments?.map((assessment, index) => {
                                                let borderColor = '';
                                                let Color = '';
                                                let buttonToShow = 'start';

                                                const userResult = assessmentDetails?.user_result?.[index];

                                                const hasAttempted = userResult && userResult.user_result?.length > 0;

                                                if (hasAttempted) {
                                                    const allCorrect = userResult.user_result.every(
                                                        item => item.response_recorded === "correct" && item.response_recorded !== null
                                                    );

                                                    if (allCorrect) {
                                                        borderColor = '#40C057';
                                                        Color = '#EAFBEA';
                                                    } else {
                                                        borderColor = '#FA5252';
                                                        Color = '#FCEBEA';
                                                    }
                                                } else {
                                                    borderColor = '#dddada';
                                                    Color = '#f5f5f5';
                                                }

                                                if (assessmentDetails?.user_result?.length > index) {
                                                    buttonToShow = 'completed';
                                                } else if (index > 0 && !assessmentDetails?.user_result?.[index - 1]) {
                                                    buttonToShow = 'locked';
                                                } else {
                                                    buttonToShow = 'start';
                                                }

                                                return (
                                                    <Box
                                                        key={assessment.id}
                                                        sx={{
                                                            borderRadius: "4px",
                                                            width: '99%',
                                                            padding: '1px 0px 0px !important',
                                                            margin: '5px auto 0px !important',
                                                            minHeight: '63px',
                                                            backgroundColor: Color,
                                                            borderBottom: `3px solid ${borderColor}`,
                                                            flexWrap: "wrap"
                                                        }}>
                                                        <Box sx={{
                                                            display: "flex",
                                                            justifyContent: "space-between",
                                                            padding: '8px 15px !important',
                                                            alignItems: "center",
                                                        }}>
                                                            <Box sx={{ flex: 1 }}>
                                                                <Box sx={{ fontWeight: "bold", fontSize: "16px" }}>
                                                                    {assessment?.title}: <span style={{ fontWeight: "normal" }}>{assessment?.name}</span>
                                                                </Box>
                                                            </Box>

                                                            <Tooltip title={
                                                                buttonToShow === 'locked' ?
                                                                    "To unlock this test, please attend the previous test!" :
                                                                    buttonToShow === 'completed' ?
                                                                        "" :
                                                                        "Click to start the test"
                                                            }>
                                                                <span>
                                                                    {buttonToShow === 'completed' ? (
                                                                        <Button
                                                                            variant="outlined"
                                                                            onClick={() => handleAnalysisButton(index)}
                                                                            sx={{

                                                                                borderColor: "#3B82F6",
                                                                                cursor: "pointer",
                                                                                borderRadius: '4px !important',
                                                                                color: "#3B82F6",
                                                                                padding: '4px 18px !important',
                                                                                "&:hover": {
                                                                                    backgroundColor: "#3B82F6",
                                                                                    color: "#fff",
                                                                                    borderColor: '#3B82F6',
                                                                                },
                                                                            }}
                                                                        >
                                                                            Analysis
                                                                        </Button>
                                                                    ) : (
                                                                        <Button
                                                                            variant="outlined"
                                                                            onClick={() => handleStartTestAssessment(index)}
                                                                            disabled={buttonToShow === 'locked'}
                                                                            sx={{
                                                                                borderColor: "#3B82F6",
                                                                                borderRadius: '4px !important',
                                                                                color: "#3B82F6",
                                                                                padding: '4px 18px !important',
                                                                                "&:hover": {
                                                                                    backgroundColor: "#3B82F6",
                                                                                    color: "#fff",
                                                                                    borderColor: '#3B82F6',
                                                                                },
                                                                                opacity: buttonToShow === 'locked' ? 0.7 : 1,
                                                                            }}
                                                                        >
                                                                            {buttonToShow === 'locked' ? "Locked" : "Start"}
                                                                        </Button>
                                                                    )}
                                                                </span>
                                                            </Tooltip>
                                                        </Box>
                                                    </Box>
                                                );
                                            })}
                                        </Box>
                                    </CustomTabPanel>

                                    <CustomTabPanel value={value} index={1} style={{ padding: '10px 10px 20px' }}>
                                        <Box sx={{ display: "flex", flexDirection: "column", gap: 2, padding: '10px 10px 20px' }}>
                                            {assessments && assessments?.length > 0 && assessments
                                                .filter((_, index) => {
                                                    // Only show assessments where user_result[index] doesn't exist or is empty
                                                    return !assessmentDetails?.user_result?.[index] ||
                                                        assessmentDetails.user_result[index].user_result?.length === 0;
                                                })
                                                .map((assessment, filteredIndex) => {
                                                    // Get the original index in the assessments array
                                                    const originalIndex = assessments.findIndex(a => a.id === assessment.id);

                                                    // Check if previous assessment was completed
                                                    const isPreviousCompleted = originalIndex > 0 &&
                                                        assessmentDetails?.user_result?.[originalIndex - 1]?.user_result?.length > 0;

                                                    // Find first unattempted assessment
                                                    const firstUnattemptedIndex = assessments.findIndex((_, i) => {
                                                        return !assessmentDetails?.user_result?.[i] ||
                                                            assessmentDetails.user_result[i].user_result?.length === 0;
                                                    });

                                                    const isLocked = originalIndex > firstUnattemptedIndex ||
                                                        (originalIndex > 0 && !isPreviousCompleted);

                                                    return (
                                                        <Box
                                                            key={assessment.id}
                                                            sx={{
                                                                borderRadius: "4px",
                                                                width: '99%',
                                                                padding: '1px 0px 0px !important',
                                                                margin: '5px auto 15px !important',
                                                                backgroundColor: '#f5f5f5',
                                                                borderBottom: `3px solid #dddada`,
                                                                flexWrap: "wrap",
                                                            }}
                                                        >
                                                            <Box
                                                                sx={{
                                                                    display: "flex",
                                                                    justifyContent: "space-between",
                                                                    padding: '8px 15px !important',
                                                                    alignItems: "center",
                                                                }}
                                                            >
                                                                <Box sx={{ flex: 1 }}>
                                                                    <Box sx={{ fontWeight: "bold", fontSize: "16px" }}>
                                                                        {assessment.title}: <span style={{ fontWeight: "normal" }}>{assessment.name}</span>
                                                                    </Box>
                                                                </Box>

                                                                <Tooltip title={
                                                                    isLocked
                                                                        ? "To unlock this test, please attend the previous test!"
                                                                        : "Click to start the test"
                                                                }>
                                                                    <span>
                                                                        <Button
                                                                            variant="outlined"
                                                                            onClick={() => handleStartTestAssessment(filteredIndex)}
                                                                            disabled={isLocked}
                                                                            sx={{
                                                                                borderColor: "#3B82F6",
                                                                                borderRadius: '4px',
                                                                                color: "#3B82F6",
                                                                                padding: '4px 18px',
                                                                                "&:hover": {
                                                                                    backgroundColor: "#3B82F6",
                                                                                    color: "#fff",
                                                                                    borderColor: '#3B82F6',
                                                                                },
                                                                                opacity: isLocked ? 0.7 : 1,
                                                                            }}
                                                                        >
                                                                            {isLocked ? "Locked" : "Start"}
                                                                        </Button>
                                                                    </span>
                                                                </Tooltip>
                                                            </Box>
                                                        </Box>
                                                    );
                                                })}
                                        </Box>
                                    </CustomTabPanel>

                                    <CustomTabPanel value={value} index={2} style={{ padding: '10px 10px 20px' }}>
                                        {assessmentDetails?.user_result?.length > 0 ? (
                                            <Box sx={{ display: "flex", flexDirection: "column", gap: 2, padding: '10px 10px 20px' }}>
                                                {assessments && assessments?.length > 0 && assessments?.map((assessment, index) => {
                                                    const userResult = assessmentDetails?.user_result?.[index];
                                                    const hasCompleted = userResult && userResult.user_result?.length > 0;

                                                    if (!hasCompleted) return null;
                                                    let borderColor = '';
                                                    let Color = '';

                                                    const allCorrect = userResult.user_result.every(
                                                        item => item.response_recorded === "correct" && item.response_recorded !== null
                                                    );

                                                    if (allCorrect) {
                                                        borderColor = '#40C057';
                                                        Color = '#EAFBEA';
                                                    } else {
                                                        borderColor = '#FA5252';
                                                        Color = '#FCEBEA';
                                                    }

                                                    return (
                                                        <Box
                                                            key={assessment.id}
                                                            sx={{
                                                                borderRadius: "4px",
                                                                width: '99%',
                                                                minHeight: '63px',
                                                                padding: '1px 0px 0px !important',
                                                                margin: '5px auto 15px !important',
                                                                backgroundColor: Color,
                                                                borderBottom: `3px solid ${borderColor}`,
                                                                flexWrap: "wrap"
                                                            }}
                                                        >
                                                            <Box sx={{
                                                                display: "flex",
                                                                justifyContent: "space-between",
                                                                padding: '8px 15px !important',
                                                                alignItems: "center",
                                                            }}>
                                                                <Box sx={{ flex: 1 }}>
                                                                    <Box sx={{ fontWeight: "bold", fontSize: "16px" }}>
                                                                        {assessment.title}: <span style={{ fontWeight: "normal" }}>{assessment.name}</span>
                                                                    </Box>
                                                                </Box>

                                                                <Button
                                                                    variant="outlined"
                                                                    onClick={() => handleAnalysisButton(index)}
                                                                    sx={{
                                                                        cursor: "pointer",
                                                                        borderColor: "#3B82F6",
                                                                        borderRadius: '4px !important',
                                                                        color: "#3B82F6",
                                                                        padding: '4px 18px !important',
                                                                        "&:hover": {
                                                                            backgroundColor: "#3B82F6",
                                                                            color: "#fff",
                                                                            borderColor: '#3B82F6'
                                                                        },
                                                                    }}
                                                                >
                                                                    Analysis
                                                                </Button>
                                                            </Box>
                                                        </Box>
                                                    );
                                                })}
                                            </Box>
                                        ) : (
                                            <Box style={{ height: '200px' }}>
                                                <Typography style={{
                                                    textAlign: 'center',
                                                    fontWeight: '500',
                                                    paddingTop: '20px'
                                                }}>
                                                    No assessments completed
                                                </Typography>
                                            </Box>
                                        )}
                                    </CustomTabPanel>
                                </Box>
                            </Card>
                        </Box>
                    </Box>
                </>
            }
            {tabIndex === 1 &&

                <Box sx={{ p: 3 }}>
                    <Typography variant="h4" gutterBottom>NEET Exam Analytics</Typography>

                    <Grid container spacing={2} sx={{ mb: 4 }}>
                        <Grid item xs={12} md={5}>
                            <Paper elevation={3}
                                sx={{
                                    padding: '16px !important', borderRadius: '16px', backgroundColor: '#fff', height: '330px',
                                    display: 'flex', flexDirection: 'column', justifyContent: 'space-between'
                                }}>

                                <Typography variant="h6" sx={{ marginBottom: '10px', marginTop: '10px', }}>Time Spent</Typography>
                                <Stack direction="row" alignItems="center" spacing={1} mb={1}>
                                    <Avatar sx={{ bgcolor: '#737cc8', width: 32, height: 32 }}>
                                        <AccessTimeIcon />
                                    </Avatar>
                                    <Typography variant="h6" fontWeight="bold">
                                        {formattedOverallTime}
                                    </Typography>
                                </Stack>
                                <Box>
                                    {TimeSpentOnSubject.map((subject) => {
                                        const percentage = totalHoursForPercentage > 0
                                            ? (subject.rawSeconds / 3600 / totalHoursForPercentage) * 100
                                            : 0;
                                        return (
                                            <Box key={subject.label} mb={3}>
                                                <Stack direction="row" justifyContent="space-between" mb={0}>
                                                    <Typography variant="body2" fontSize="15px" fontWeight={500}>
                                                        {subject.label}
                                                    </Typography>
                                                    <Typography variant="body2" fontWeight={500}>
                                                        {subject.displayTime}
                                                    </Typography>
                                                </Stack>
                                                <LinearProgress
                                                    variant="determinate"
                                                    value={percentage}
                                                    sx={{
                                                        height: 10,
                                                        borderRadius: 5,
                                                        backgroundColor: '#eee',
                                                        '& .MuiLinearProgress-bar': {
                                                            backgroundColor: subject.color,
                                                        },
                                                    }}
                                                />
                                            </Box>
                                        );
                                    })}
                                </Box>
                            </Paper>
                        </Grid>
                        <Grid item xs={12} md={7}>

                            <Paper
                                elevation={3}
                                sx={{
                                    borderRadius: 2,
                                    p: 3,
                                    minHeight: '250px',
                                    display: 'flex',
                                    flexDirection: 'column',
                                    justifyContent: 'space-between',
                                    height: 'Auto'
                                }}
                            >

                                <Typography
                                    variant="h6"
                                    sx={{
                                        mb: 2,
                                        fontWeight: 'bold',
                                        color: '#000000ff',
                                    }}
                                > Accuracy (In Percentage) </Typography>
                                <Grid container spacing={1}
                                    sx={{
                                        justifyContent: 'space-around'
                                    }}>

                                    {averageAccuracyBiology === 0 &&
                                        averageAccuracyChemistry === 0 &&
                                        averageAccuracyPhysics === 0 ? (
                                        <Box
                                            sx={{
                                                flexGrow: 1,
                                                display: 'flex',
                                                alignItems: 'center',
                                                justifyContent: 'center',
                                                height: '150px',
                                            }}
                                        >
                                            <Typography variant="h4" fontWeight={900}>
                                                N/A
                                            </Typography>
                                        </Box>
                                    ) : (
                                        <Grid container spacing={2} justifyContent="space-around">
                                            <Grid item xs={12} sm={4} sx={{ display: 'flex', justifyContent: 'center' }}>

                                                <Box sx={{
                                                    position: 'relative',
                                                    display: 'flex',
                                                    flexDirection: 'column',
                                                    alignItems: 'center',
                                                    justifyContent: 'center',
                                                    height: '100%',
                                                }}>
                                                    <PieChart
                                                        series={[{
                                                            data: [
                                                                { value: averageAccuracyBiology, color: '#52c28c' },
                                                                { value: 100 - averageAccuracyBiology, color: '#e0e0e0' }
                                                            ],
                                                            innerRadius: 40,
                                                            outerRadius: 70,
                                                            paddingAngle: 0,
                                                            cornerRadius: 5,
                                                            startAngle: -90,
                                                            cx: 100,
                                                        }]}
                                                        height={180}
                                                        width={200}
                                                    />

                                                    <Typography variant="body2" sx={{
                                                        position: 'absolute',
                                                        top: '44%',
                                                        left: '53%',
                                                        transform: 'translate(-50%, -50%)',
                                                        fontWeight: 'bold',
                                                    }}>
                                                        {averageAccuracyBiology.toFixed(2)}%
                                                    </Typography>
                                                    <Typography variant="body2" sx={{
                                                        fontWeight: 'bold',
                                                        textAlign: 'center', paddingLeft: '14px'
                                                    }}>
                                                        Biology
                                                    </Typography>

                                                </Box>

                                            </Grid>

                                            <Grid item xs={12} sm={4} sx={{ display: 'flex', justifyContent: 'center' }}>
                                                <Box sx={{
                                                    position: 'relative',
                                                    display: 'flex',
                                                    flexDirection: 'column',
                                                    alignItems: 'center',
                                                    justifyContent: 'center',
                                                    height: '100%',
                                                }}>
                                                    <PieChart
                                                        series={[{
                                                            data: [
                                                                { value: averageAccuracyChemistry, color: '#fc9f06' },
                                                                { value: 100 - averageAccuracyChemistry, color: '#e0e0e0' }
                                                            ],
                                                            innerRadius: 40,
                                                            outerRadius: 70,
                                                            paddingAngle: 0,
                                                            cornerRadius: 5,
                                                            startAngle: -90,
                                                            cx: 100,
                                                        }]}
                                                        height={180}
                                                        width={200}
                                                    />
                                                    <Typography variant="body2" sx={{
                                                        position: 'absolute',
                                                        top: '44%',
                                                        left: '53%',
                                                        transform: 'translate(-50%, -50%)',
                                                        fontWeight: 'bold',
                                                    }}>
                                                        {averageAccuracyChemistry.toFixed(2)}%
                                                    </Typography>
                                                    <Typography variant="body2" sx={{
                                                        fontWeight: 'bold',
                                                        textAlign: 'center', paddingLeft: '14px'
                                                    }}>
                                                        Chemistry
                                                    </Typography>
                                                </Box>

                                            </Grid>

                                            <Grid item xs={12} sm={4} sx={{ display: 'flex', justifyContent: 'center' }}>
                                                <Box sx={{
                                                    position: 'relative',
                                                    display: 'flex',
                                                    flexDirection: 'column',
                                                    alignItems: 'center',
                                                    justifyContent: 'center',
                                                    height: '100%',
                                                }}>
                                                    <PieChart
                                                        series={[{
                                                            data: [{ value: averageAccuracyPhysics, color: '#437bfc' },
                                                            { value: 100 - averageAccuracyPhysics, color: '#e0e0e0' }],
                                                            innerRadius: 40,
                                                            outerRadius: 70,
                                                            paddingAngle: 0,
                                                            cornerRadius: 5,
                                                            startAngle: -90,
                                                            cx: 100,
                                                        }]}
                                                        height={180}
                                                        width={200}
                                                    />
                                                    <Typography
                                                        variant="body2"
                                                        sx={{
                                                            position: 'absolute',
                                                            top: '44%',
                                                            left: '53%',
                                                            transform: 'translate(-50%, -50%)',
                                                            fontWeight: 'bold',
                                                        }}
                                                    >
                                                        {averageAccuracyPhysics.toFixed(2)}%
                                                    </Typography>
                                                    <Typography variant="body2" sx={{
                                                        fontWeight: 'bold',
                                                        textAlign: 'center', paddingLeft: '14px'
                                                    }}>
                                                        Physics
                                                    </Typography>
                                                </Box>
                                            </Grid>
                                        </Grid>
                                    )}

                                    <Grid container>
                                        <Grid item xs={12}>
                                            <Box sx={{ borderTop: '1px solid #e0e0e0', mt: 1, pt: 1, textAlign: 'center', }} >
                                                <Typography
                                                    variant="h6"
                                                    sx={{ fontWeight: 'bold', mt: 1, color: '#0B1C39', }}
                                                >
                                                    Overall Accuracy:{' '}
                                                    {averageAccuracyBiology === 0 && averageAccuracyChemistry === 0 && averageAccuracyPhysics === 0
                                                        ? 'N/A'
                                                        : `${overallAccuracy.toFixed(2)}%`}
                                                </Typography>
                                            </Box>
                                        </Grid>
                                    </Grid>


                                </Grid>
                            </Paper>
                        </Grid>
                    </Grid>


                    <Box id="commonShadow">
                        <Typography variant="h6" gutterBottom>Performace - Top Chapter</Typography>
                        <Table sx={{ mb: 1 }}>
                            <TableHead>
                                <TableRow style={{ backgroundColor: '#ccdbff', borderRadius: '10px' }}>
                                    <TableCell sx={{ width: '23%' }}>Chapter Name</TableCell>
                                    <TableCell align="center">Subject</TableCell>
                                    <TableCell align="center">Correct</TableCell>
                                    <TableCell align="center">Total Answered</TableCell>
                                    <TableCell align="center">Total Questions</TableCell>
                                    <TableCell align="center">Accuracy</TableCell>
                                    <TableCell align="center">Avg Time</TableCell>
                                </TableRow>
                            </TableHead>
                            <TableBody>
                                {highestAccuracyChaptersPerSubject.length > 0 &&
                                    highestAccuracyChaptersPerSubject.map((q, index) => (
                                        <TableRow key={index}>
                                            <TableCell sx={{ width: '23%' }}>{q.chapterName}</TableCell>
                                            <TableCell align="center">
                                                <Chip label={q.subject} size="small" sx={{
                                                    backgroundColor: subjectColorMap[q.subject], color: "#fff",
                                                    borderRadius: '15px', fontWeight: '500'
                                                }} /> </TableCell>
                                            <TableCell align="center">{q.correct}</TableCell>
                                            <TableCell align="center">{q.totalAnswered}</TableCell>
                                            <TableCell align="center">{q.totalQuestions}</TableCell>
                                            <TableCell align="center">{q.accuracy}</TableCell>
                                            <TableCell align="center">{q.avgTime}sec</TableCell>
                                        </TableRow>
                                    ))}
                                {highestAccuracyChaptersPerSubject?.length === 0 && (
                                    <TableRow>
                                        <TableCell colSpan={7} align="center">Please attempt the assessment to know the analytics!</TableCell>
                                    </TableRow>
                                )}
                            </TableBody>
                        </Table>
                    </Box>

                    <Box id="commonShadow">
                        <Typography variant="h6" gutterBottom>Performace - Weak Chapter</Typography>
                        <Table sx={{ mb: 1 }}>
                            <TableHead>
                                <TableRow style={{ backgroundColor: '#ccdbff', borderRadius: '10px' }}>
                                    <TableCell sx={{ width: '23%' }}>Chapter Name</TableCell>
                                    <TableCell align="center">Subject</TableCell>
                                    <TableCell align="center">Correct</TableCell>
                                    <TableCell align="center">Total Answered</TableCell>
                                    <TableCell align="center">Total Questions</TableCell>
                                    <TableCell align="center">Accuracy</TableCell>
                                    <TableCell align="center">Avg Time</TableCell>
                                </TableRow>
                            </TableHead>
                            <TableBody>
                                {weakestAccuracyChaptersPerSubject.length > 0 &&
                                    weakestAccuracyChaptersPerSubject.map((q, index) => (
                                        <TableRow key={index}>
                                            <TableCell sx={{ width: '23%' }}>{q.chapterName}</TableCell>
                                            <TableCell align="center">
                                                <Chip label={q.subject} size="small" sx={{
                                                    backgroundColor: subjectColorMap[q.subject], color: "#fff",
                                                    borderRadius: '15px', fontWeight: '500'
                                                }} /> </TableCell>
                                            <TableCell align="center">{q.correct}</TableCell>
                                            <TableCell align="center">{q.totalAnswered}</TableCell>
                                            <TableCell align="center">{q.totalQuestions}</TableCell>
                                            <TableCell align="center">{q.accuracy}</TableCell>
                                            <TableCell align="center">{q.avgTime}sec</TableCell>
                                        </TableRow>
                                    ))}
                                {weakestAccuracyChaptersPerSubject?.length === 0 && (
                                    <TableRow>
                                        <TableCell colSpan={7} align="center">Please attempt the assessment to know the analytics!</TableCell>
                                    </TableRow>
                                )}
                            </TableBody>
                        </Table>
                    </Box>
                </Box>
            }

            {tabIndex === 2 &&
                <>
                    <Box >
                        <Grid container spacing={2}>
                            <Grid item xs={12} sm={12}>
                                <Grid container gap={1} spacing={2} sx={{ padding: "0px !important", justifyContent: 'space-between', margin: '0 !important', marginBottom: '15px !important', overflow: "hidden", width: "100% !important" }}>
                                    <Grid item xs={12} sm={4} md={3} lg={2} sx={{ boxShadow: 'rgba(99, 99, 99, 0.2) 0px 2px 8px 0px', background: '#fff', minHeight: '100px', marginBottom: '10px', borderRadius: '10px' }}>
                                        <Typography variant="subtitle1" fontWeight={700} textAlign={'center'}>
                                            Total Marks:
                                        </Typography>
                                        <Typography variant="h5" textAlign={'center'} sx={{ fontSize: { xs: '20px', sm: '22px', md: '24px', lg: '28px', } }} fontWeight={900}>
                                            {totalMarksPhysics && totalMarksPhysics}
                                        </Typography>
                                    </Grid>
                                    <Grid item xs={12} sm={4} md={3} lg={2} sx={{ boxShadow: 'rgba(99, 99, 99, 0.2) 0px 2px 8px 0px', background: '#fff', minHeight: '100px', marginBottom: '10px', borderRadius: '10px' }}>
                                        <Typography variant="subtitle1" fontWeight={700} textAlign={'center'}>
                                            Total Question Answered:
                                        </Typography>
                                        <Typography variant="h5" textAlign={'center'} sx={{ fontSize: { xs: '20px', sm: '22px', md: '24px', lg: '28px', } }} fontWeight={900}>
                                            {totalAnsweredPhysics > 0 ? (
                                                `${totalAnsweredPhysics} / ${totalPhysicsQuestions}`
                                            ) : (
                                                '0 / 45'
                                            )}
                                        </Typography>
                                    </Grid>
                                    <Grid item xs={12} sm={4} md={3} lg={2} sx={{ boxShadow: 'rgba(99, 99, 99, 0.2) 0px 2px 8px 0px', background: '#fff', minHeight: '100px', marginBottom: '10px', borderRadius: '10px' }}>
                                        <Box display="flex" alignItems="center" gap={1} justifyContent="center">
                                            <CheckCircleIcon color="success" fontSize="small" />
                                            <Typography variant="subtitle1" fontWeight={700}>
                                                Correct
                                            </Typography>
                                        </Box>
                                        <Typography variant="h5" textAlign={'center'} sx={{ fontSize: { xs: '20px', sm: '22px', md: '24px', lg: '28px', } }} fontWeight={900}>
                                            {totalCorrectPhysics && totalCorrectPhysics}


                                        </Typography>
                                    </Grid>
                                    <Grid item xs={12} sm={4} md={3} lg={2} sx={{ boxShadow: 'rgba(99, 99, 99, 0.2) 0px 2px 8px 0px', background: '#fff', minHeight: '100px', marginBottom: '10px', borderRadius: '10px' }}>
                                        <Box display="flex" alignItems="start" gap={1} justifyContent="center">
                                            <CancelIcon color="error" fontSize="small" />
                                            <Typography variant="subtitle1" fontWeight={700}>
                                                Incorrect
                                            </Typography>
                                        </Box>
                                        <Typography variant="h5" textAlign={'center'} sx={{ fontSize: { xs: '20px', sm: '22px', md: '24px', lg: '28px', } }} fontWeight={900}>
                                            {totalIncorrectPhysics && totalIncorrectPhysics}
                                        </Typography>
                                    </Grid>
                                    <Grid item xs={12} sm={4} md={3} lg={2} sx={{ boxShadow: 'rgba(99, 99, 99, 0.2) 0px 2px 8px 0px', background: '#fff', minHeight: '100px', marginBottom: '10px', borderRadius: '10px' }}>
                                        <Box display="flex" alignItems="start" gap={1} justifyContent="center">
                                            <TrackChangesIcon sx={{ color: "#a1c5ff" }} fontSize="small" />
                                            <Typography variant="subtitle1" fontWeight={700}>
                                                Accuracy
                                            </Typography>
                                        </Box>
                                        <Typography variant="h5" textAlign={'center'} sx={{ fontSize: { xs: '20px', sm: '22px', md: '24px', lg: '28px', } }} fontWeight={900}>
                                            {averageAccuracyPhysics ? `${averageAccuracyPhysics.toFixed(2)}%` : "0%"}
                                        </Typography>
                                    </Grid>
                                </Grid>
                            </Grid>
                        </Grid>
                    </Box>

                    <Paper elevation={3} sx={{ p: 2, mb: 4 }} >
                        <Typography variant="h6" sx={{ mb: 2, fontWeight: 'bold', color: '#000000ff', }}>Time Spent</Typography>
                        {physicsChartData.series[0]?.data?.length > 0 ? (
                            <>
                                <BarChart
                                    height={350}
                                    series={[{ ...physicsChartData.series[0], color: '#437bfc', data: paginatedDataPhy }]}
                                    xAxis={[{
                                        scaleType: 'band',
                                        data: paginatedLabelsPhy,
                                        barGapRatio: 0,
                                        tickLabelStyle: {
                                            textOverflow: 'ellipsis',
                                            whiteSpace: 'pre',
                                            fontSize: xAxisFontSize,
                                            dy: 50,
                                        },
                                        categoryGapRatio: 0.9,
                                    }]}
                                    yAxis={[{
                                        label: 'Average Time(in seconds)',
                                        tickLabelStyle: { fontSize: 12 }
                                    }]}
                                    margin={{ left: 50, right: 30, top: 30, bottom: 50 }}
                                    barSeries={[{ barWidth: '50%' }]}
                                    slotProps={{
                                        bar: {
                                            style: {
                                                width: 25,
                                                justifyItems: 'center',
                                            },
                                        },
                                    }}
                                    tooltip={{
                                        trigger: 'item',
                                        formatter: (value, context) => {
                                            return paginatedTooltipsPhy[context.dataIndex] || 'N/A';
                                        }
                                    }}
                                />
                                <Box display="flex" justifyContent="center" mt={2}>
                                    <Button sx={{ color: (currentPagePhy > 0) ? '#0b57d0' : "" }}
                                        disabled={currentPagePhy === 0}
                                        onClick={() => setCurrentPagePhy(prev => prev - 1)}
                                    >
                                        ← Prev
                                    </Button>
                                    <Typography mx={2} sx={{ alignItems: 'center', alignSelf: 'center' }}>{currentPagePhy + 1} of {Math.ceil(formattedXAxisLabelsPhy.length / itemsPerPage)}</Typography>
                                    <Button sx={{ color: (endIndexPhy <= formattedXAxisLabelsPhy.length) ? '#0b57d0' : "" }}
                                        disabled={endIndexPhy >= formattedXAxisLabelsPhy.length}
                                        onClick={() => setCurrentPagePhy(prev => prev + 1)}
                                    >
                                        Next →
                                    </Button>
                                </Box>
                            </>
                        ) : (
                            <Typography variant="body2" color="textSecondary" sx={{ mt: 2 }}>
                                Please attempt the assessment to know the analytics!
                            </Typography>
                        )}
                    </Paper>

                    <Box sx={{ mt: 3 }} id="commonShadow">
                        <Typography variant="h6" gutterBottom>Chapter Wise Performance</Typography>
                        <Table sx={{ mb: 1 }}>
                            <TableHead>
                                <TableRow style={{ backgroundColor: '#ccdbff', borderRadius: '10px' }}>
                                    <TableCell>Chapter Name</TableCell>
                                    <TableCell align="center">Correct</TableCell>
                                    <TableCell align="center">Total Answered</TableCell>
                                    <TableCell align="center">Accuracy</TableCell>
                                    <TableCell align="center">Total Questions</TableCell>
                                    <TableCell align="center">Avg Time</TableCell>
                                </TableRow>
                            </TableHead>
                            <TableBody>
                                {physicsStats?.slice().sort((a, b) => a.chapterName?.localeCompare(b.chapterName)).map((chapter, index) => (
                                    <TableRow key={index}>
                                        <TableCell>{chapter?.chapterName}</TableCell>
                                        <TableCell align="center">{chapter?.correct}</TableCell>
                                        <TableCell align="center">{chapter?.totalAnswered === 0 ? '-' : chapter?.totalAnswered}</TableCell>
                                        <TableCell align="center">{chapter?.accuracy === 0 ? '-' : chapter?.accuracy}</TableCell>
                                        <TableCell align="center">{chapter?.totalQuestions}</TableCell>
                                        <TableCell align="center">{chapter?.avgTime === 0 ? '-' : `${chapter?.avgTime}sec`}</TableCell>
                                    </TableRow>
                                ))}
                                {physicsStats?.length === 0 && (
                                    <TableRow>
                                        <TableCell colSpan={6} align="center">Please attempt the assessment to know the analytics!</TableCell>
                                    </TableRow>
                                )}
                            </TableBody>
                        </Table>
                    </Box>
                    <Box sx={{
                        padding: 2, boxShadow: '0px 3px 3px -2px rgba(145, 158, 171, 0.2), 0px 3px 4px 0px rgba(145, 158, 171, 0.14), 0px 1px 8px 0px rgba(145, 158, 171, 0.12)',
                        margin: '0', background: '#fff', paddingTop: '10px !important', borderRadius: '16px', marginBottom: '20px', paddingBottom: '15px !important'
                    }}>
                        <Typography variant="h6" sx={{ fontWeight: 'bold', marginBottom: '20px', marginTop: '20px', marginLeft: '30px' }}>{"Questions and Solutions"}</Typography>
                        {neetAnalysis?.physics && neetAnalysis?.physics?.length > 0 ? neetAnalysis?.physics?.slice(startIndexPhysicsQuestions, endIndexPhysicsQuestions).map((data, index) => {
                            let parsedOptions;

                            parsedOptions = typeof data.options === "string" ? JSON.parse(data.options) : data.options;
                          

                            return (
                                <Accordion
                                    key={index}
                                    ref={(el) => {
                                        inputRefs.current[index] = el;
                                    }}
                                    expanded={expanded === `panel${index}`}
                                    id="accordionCard"
                                    onChange={handleAccordionChange(`panel${index}`, index)}
                                    sx={{
                                        margin: '0 26px 16px !important',
                                        borderRadius: '15px !important',
                                        '&:before': {
                                            display: 'none',
                                        },
                                        boxShadow: 'rgba(0, 0, 0, 0.16) 0px 1px 4px'
                                    }}
                                >
                                    <AccordionSummary className='iconExplore'
                                        expandIcon={<ExpandMoreIcon style={{
                                            borderRadius: '50%',
                                            border: '1px solid',
                                            width: '30px',
                                            height: '30px',
                                            padding: '5px',
                                            transform: 'rotate(270deg)',
                                            margin: '0px 10px 0 0',
                                        }} />}
                                        style={{
                                            borderRadius: expanded === `panel${index}` ? '15px 15px 0 0' : '15px',

                                            flexDirection: 'row-reverse',
                                            '& .MuiAccordionSummary-expandIconWrapper': {
                                                marginRight: '16px',
                                            }
                                        }}
                                    >
                                        <Typography
                                            variant="body1" id="questionDropdown"
                                            sx={{ flexGrow: 1, fontWeight: "bold" }}
                                            dangerouslySetInnerHTML={{ __html: data.question_text }}

                                        />
                                        <Box>

                                            {data.response === 'incorrect' && (
                                                <CancelIcon sx={{ color: '#ea4b67' }} />
                                            )}
                                            {data.response === 'correct' && (
                                                <CheckCircleIcon sx={{ color: '#70bb9c' }} />
                                            )}
                                            {data.response === 'null' || data.response === null && (

                                                <ErrorIcon sx={{ color: '#bfbbbb' }} />
                                            )}

                                        </Box>


                                    </AccordionSummary>
                                    <AccordionDetails>
                                        <Divider />
                                        <Box sx={{ display: 'flex', spaceBetween: '1', margin: '10px 0px', marginLeft: '12px' }}>
                                            <Typography sx={{ alignItems: 'center', display: 'flex', marginRight: '20px' }}>
                                                <HourglassBottomIcon sx={{ marginRight: "5px" }} /> Time spent  : {data?.time_taken ? formatTimeTimerDisplay(data?.time_taken) : '0 min 0 sec'}
                                            </Typography>

                                        </Box>

                                        <Box sx={{ marginLeft: '17px' }}>
                                            {/* <Typography sx={{ marginTop: '10px', marginBottom: '5px' }}>
                                                            Instructions :
                                                        </Typography>
                                                        <Typography sx={{ marginBottom: '10px' }}>
                                                            {data?.instruction ? data?.instruction : 'To prepare your JSON for translation using your LLM function — only translating values like courseModuleName, courseSubmoduleName, and courseSubmoduleTopics — you can extract those values, translate them, and then reinsert them into the original structure Here’s a Python function to do that:'}
                                                        </Typography> */}
                                        </Box>
                                        <Divider />
                                        <Box sx={{ marginLeft: '17px' }}>
                                            <Typography variant="subtitle2" fontWeight="bold" sx={{ marginTop: '10px', marginBottom: '8px' }}>
                                                <span style={{ position: 'relative', top: '3px', fontSize: '15px' }}>Question</span>   <Button style={{ color: '#fff', background: '#437bfc', borderRadius: '5px', marginLeft: '25px', width: '124px', padding: '4px', fontSize: '15px' }} onClick={toggleSection}>{isShow ? 'Hide Answer' : 'Show Answer'}</Button>
                                            </Typography>
                                            <Typography variant="subtitle2" id="questionWithAnswer" fontWeight="bold" sx={{ marginTop: '10px', marginBottom: '8px' }}
                                                dangerouslySetInnerHTML={{ __html: data.question_text }} />
                                        </Box>

                                        <Box id="toggleBox">
                                            <Typography variant="subtitle2" fontWeight="bold" sx={{ marginTop: '10px', marginBottom: '8px' }}>
                                                Options:
                                            </Typography>


                                            {parsedOptions && parsedOptions.mcqOptions && parsedOptions.mcqOptions.map((option, idx) => {
                                                const isSelected = data.selectedanswer === option;
                                                const isCorrect = parsedOptions.correctAnswer?.[idx];

                                                let circleColor = '';
                                                let textColor = '';
                                                let fontWeight = 'normal';

                                                if (!isShow) {
                                                    if (isSelected) {
                                                        circleColor = '#888';
                                                    }
                                                } else {
                                                    if (isCorrect) {
                                                        circleColor = 'green';
                                                        textColor = 'green';
                                                        fontWeight = 'bold';
                                                    } else if (isSelected && !isCorrect) {
                                                        circleColor = 'red';
                                                        textColor = 'red';
                                                        fontWeight = 'bold';
                                                    }
                                                }

                                                return (
                                                    <Box key={idx} display="flex" alignItems="center" mb={1} sx={{ marginLeft: '1px' }}>
                                                        <Box style={{ width: '30px' }}>
                                                            <span style={{
                                                                border: `1px solid ${circleColor || '#aaa'}`,
                                                                borderRadius: '50%',
                                                                height: '18px',
                                                                width: '19px',
                                                                display: 'flex',
                                                                justifyContent: 'center',
                                                                alignItems: 'center',
                                                                marginRight: '12px',
                                                            }}>
                                                                <span style={{
                                                                    width: '7px',
                                                                    height: '7px',
                                                                    borderRadius: '50%',
                                                                    background: circleColor,
                                                                }} id="loremDot" />
                                                            </span>
                                                        </Box>
                                                        <Typography
                                                            variant="body2"
                                                            sx={{
                                                                fontWeight: fontWeight,
                                                                color: textColor || 'inherit',
                                                            }}
                                                            dangerouslySetInnerHTML={{ __html: option }}
                                                        />
                                                    </Box>
                                                );
                                            })}
                                        </Box>

                                        <Divider />
                                        {isShow && data?.justification && (
                                            <Box mt={2}>
                                                <Typography variant="subtitle2" fontWeight="bold">
                                                    Explanation:
                                                </Typography>
                                                <Typography id="ExplanationDiv" variant="body2" sx={{ paddingLeft: '45px', textAlignLast: 'start', fontWeight: '550', marginTop: '10px' }}
                                                    dangerouslySetInnerHTML={{ __html: data.justification }} />
                                            </Box>
                                        )}


                                    </AccordionDetails>

                                </Accordion>
                            )
                        })
                            :
                            (
                                <Typography variant="body2" color="textSecondary" sx={{ mt: 2, textAlign: 'center' }}>
                                    Please attempt the assessment to know the Solutions!
                                </Typography>
                            )
                        }

                        {neetAnalysis?.physics && neetAnalysis?.physics?.length > questionsPerPage && (
                            <Box display="flex" justifyContent="center" mt={2}>
                                <Button sx={{ color: (currentPagePhysicsQuestions > 0) ? '#0b57d0' : "" }}
                                    disabled={currentPagePhysicsQuestions === 0}
                                    onClick={() => setCurrentPagePhysicsQuestions(prev => prev - 1)}
                                >
                                    ← Prev
                                </Button>
                                <Typography mx={2} sx={{ alignItems: 'center', alignSelf: 'center' }}>
                                    {currentPagePhysicsQuestions + 1} of {Math.ceil(neetAnalysis?.physics?.length / questionsPerPage)}
                                </Typography>
                                <Button sx={{ color: (endIndexPhysicsQuestions <= neetAnalysis?.physics?.length) ? '#0b57d0' : "" }}
                                    disabled={endIndexPhysicsQuestions >= neetAnalysis?.physics?.length}
                                    onClick={() => setCurrentPagePhysicsQuestions(prev => prev + 1)}
                                >
                                    Next →
                                </Button>
                            </Box>
                        )}
                    </Box>

                </>
            }

            {tabIndex === 3 &&
                <>

                    <Box >
                        <Grid container spacing={2}>
                            <Grid item xs={12} sm={12}>
                                <Grid container gap={1} spacing={2} sx={{ padding: "0px !important", justifyContent: 'space-between', margin: '0 !important', marginBottom: '15px !important', overflow: "hidden", width: "100% !important" }}>
                                    <Grid item xs={12} sm={4} md={3} lg={2} sx={{ boxShadow: 'rgba(99, 99, 99, 0.2) 0px 2px 8px 0px', background: '#fff', minHeight: '100px', marginBottom: '10px', borderRadius: '10px' }}>
                                        <Typography variant="subtitle1" fontWeight={700} textAlign={'center'}>
                                            Total Marks:
                                        </Typography>
                                        <Typography variant="h5" textAlign={'center'} sx={{ fontSize: { xs: '20px', sm: '22px', md: '24px', lg: '28px', } }} fontWeight={900}>
                                            {totalMarksBiology && totalMarksBiology}
                                        </Typography>
                                    </Grid>
                                    <Grid item xs={12} sm={4} md={3} lg={2} sx={{ boxShadow: 'rgba(99, 99, 99, 0.2) 0px 2px 8px 0px', background: '#fff', minHeight: '100px', marginBottom: '10px', borderRadius: '10px' }}>
                                        <Typography variant="subtitle1" fontWeight={700} textAlign={'center'}>
                                            Total Question Answered:
                                        </Typography>
                                        <Typography variant="h5" textAlign={'center'} sx={{ fontSize: { xs: '20px', sm: '22px', md: '24px', lg: '28px', } }} fontWeight={900}>
                                            {totalAnsweredBiology > 0 ? (
                                                `${totalAnsweredBiology} / ${totalBiologyQuestions}`
                                            ) : (
                                                '0 / 90'
                                            )}
                                        </Typography>
                                    </Grid>
                                    <Grid item xs={12} sm={4} md={3} lg={2} sx={{ boxShadow: 'rgba(99, 99, 99, 0.2) 0px 2px 8px 0px', background: '#fff', minHeight: '100px', marginBottom: '10px', borderRadius: '10px' }}>
                                        <Box display="flex" alignItems="center" gap={1} justifyContent="center">
                                            <CheckCircleIcon color="success" fontSize="small" />
                                            <Typography variant="subtitle1" fontWeight={700}>
                                                Correct
                                            </Typography>
                                        </Box>
                                        <Typography variant="h5" textAlign={'center'} sx={{ fontSize: { xs: '20px', sm: '22px', md: '24px', lg: '28px', } }} fontWeight={900}>
                                            {totalCorrectBiology && totalCorrectBiology}
                                        </Typography>
                                    </Grid>
                                    <Grid item xs={12} sm={4} md={3} lg={2} sx={{ boxShadow: 'rgba(99, 99, 99, 0.2) 0px 2px 8px 0px', background: '#fff', minHeight: '100px', marginBottom: '10px', borderRadius: '10px' }}>
                                        <Box display="flex" alignItems="start" gap={1} justifyContent="center">
                                            <CancelIcon color="error" fontSize="small" />
                                            <Typography variant="subtitle1" fontWeight={700}>
                                                Incorrect
                                            </Typography>
                                        </Box>
                                        <Typography variant="h5" textAlign={'center'} sx={{ fontSize: { xs: '20px', sm: '22px', md: '24px', lg: '28px', } }} fontWeight={900}>
                                            {totalIncorrectBiology && totalIncorrectBiology}
                                        </Typography>
                                    </Grid>
                                    <Grid item xs={12} sm={4} md={3} lg={2} sx={{ boxShadow: 'rgba(99, 99, 99, 0.2) 0px 2px 8px 0px', background: '#fff', minHeight: '100px', marginBottom: '10px', borderRadius: '10px' }}>
                                        <Box display="flex" alignItems="start" gap={1} justifyContent="center">
                                            <TrackChangesIcon sx={{ color: "#a1c5ff" }} fontSize="small" />
                                            <Typography variant="subtitle1" fontWeight={700}>
                                                Accuracy
                                            </Typography>
                                        </Box>
                                        <Typography variant="h5" textAlign={'center'} sx={{ fontSize: { xs: '20px', sm: '22px', md: '24px', lg: '28px', } }} fontWeight={900}>
                                            {averageAccuracyBiology ? `${averageAccuracyBiology.toFixed(2)}%` : "0%"}

                                        </Typography>
                                    </Grid>
                                </Grid>

                            </Grid>

                        </Grid>
                    </Box>

                    <Paper elevation={3} sx={{ p: 2, mb: 4 }}>
                        <Typography variant="h6">Time Spent</Typography>

                        {biologyChartData.series[0]?.data.length > 0 ? (
                            <>
                                <BarChart
                                    height={350}
                                    series={[{ ...biologyChartData.series[0], color: '#52c28c', data: paginatedDataBio }]}

                                    xAxis={[{
                                        scaleType: 'band',
                                        data: paginatedLabelsBio,

                                        barGapRatio: 0,
                                        tickLabelStyle: {
                                            textOverflow: 'ellipsis',
                                            whiteSpace: 'pre',
                                            fontSize: xAxisFontSize,
                                            dy: 50,
                                        },
                                        categoryGapRatio: 0.9,
                                    }]}
                                    yAxis={[{
                                        label: 'Average Time(in seconds)',
                                        tickLabelStyle: { fontSize: 12 }
                                    }]}
                                    margin={{ left: 50, right: 30, top: 30, bottom: 50 }}
                                    barSeries={[{ barWidth: '50%' }]}
                                    slotProps={{
                                        bar: {
                                            style: {
                                                width: 25,
                                            },
                                        },
                                    }}
                                    tooltip={{
                                        trigger: 'item',
                                        formatter: (value, context) => {
                                            return paginatedTooltipsBio[context.dataIndex] || 'N/A';
                                        }
                                    }}
                                />
                                <Box display="flex" justifyContent="center" mt={2}>
                                    <Button sx={{ color: (currentPageBio > 0) ? '#0b57d0' : "" }}
                                        disabled={currentPageBio === 0}
                                        onClick={() => setCurrentPageBio(prev => prev - 1)}
                                    >
                                        ← Prev
                                    </Button>
                                    <Typography mx={2} sx={{ alignItems: 'center', alignSelf: 'center' }}>{currentPageBio + 1} of {Math.ceil(formattedXAxisLabelsBio.length / itemsPerPage)}</Typography>
                                    <Button sx={{ color: (endIndexBio <= formattedXAxisLabelsBio.length) ? '#0b57d0' : "" }}
                                        disabled={endIndexBio >= formattedXAxisLabelsBio.length}
                                        onClick={() => setCurrentPageBio(prev => prev + 1)}
                                    >
                                        Next →
                                    </Button>
                                </Box>
                            </>
                        ) : (
                            <Typography variant="body2" color="textSecondary" sx={{ mt: 2 }}>
                                Please attempt the assessment to know the analytics!
                            </Typography>
                        )}
                    </Paper>


                    <Box sx={{ mt: 3 }} id="commonShadow">
                        <Typography variant="h6" gutterBottom>Chapter Wise Performance</Typography>
                        <Table sx={{ mb: 1 }}>
                            <TableHead>
                                <TableRow style={{ backgroundColor: '#ccdbff', borderRadius: '10px' }}>
                                    <TableCell>Chapter Name</TableCell>
                                    <TableCell align="center">Correct</TableCell>
                                    <TableCell align="center">Total Answered</TableCell>
                                    <TableCell align="center">Accuracy</TableCell>
                                    <TableCell align="center">Total Questions</TableCell>
                                    <TableCell align="center">Avg Time</TableCell>
                                </TableRow>
                            </TableHead>
                            <TableBody>
                                {biologyStats.slice().sort((a, b) => a.chapterName.localeCompare(b.chapterName)).map((chapter, index) => (
                                    <TableRow key={index}>
                                        <TableCell>{chapter?.chapterName}</TableCell>
                                        <TableCell align="center">{chapter?.correct}</TableCell>
                                        <TableCell align="center">{chapter?.totalAnswered === 0 ? '-' : chapter?.totalAnswered}</TableCell>
                                        <TableCell align="center">{chapter?.accuracy === 0 ? '-' : chapter?.accuracy}</TableCell>
                                        <TableCell align="center">{chapter?.totalQuestions}</TableCell>
                                        <TableCell align="center">{chapter?.avgTime === 0 ? '-' : `${chapter?.avgTime}sec`}</TableCell>
                                    </TableRow>
                                ))}
                                {biologyStats.length === 0 && (
                                    <TableRow>
                                        <TableCell colSpan={6} align="center">Please attempt the assessment to know the analytics! .</TableCell>
                                    </TableRow>
                                )}
                            </TableBody>
                        </Table>
                    </Box>


                    <Box sx={{
                        padding: 2, boxShadow: '0px 3px 3px -2px rgba(145, 158, 171, 0.2), 0px 3px 4px 0px rgba(145, 158, 171, 0.14), 0px 1px 8px 0px rgba(145, 158, 171, 0.12)',
                        margin: '0', background: '#fff', paddingTop: '10px !important', borderRadius: '16px', marginBottom: '20px', paddingBottom: '15px !important'
                    }}>
                        <Typography variant="h6" sx={{ fontWeight: 'bold', marginBottom: '20px', marginTop: '20px', marginLeft: '30px' }}>{"Questions and Solutions"}</Typography>
                        {neetAnalysis?.biology && neetAnalysis?.biology?.length > 0 ? neetAnalysis?.biology?.slice(startIndexBiologyQuestions, endIndexBiologyQuestions).map((data, index) => {
                             let parsedOptions;

                             parsedOptions = typeof data.options === "string" ? JSON.parse(data.options) : data.options;
                           
                            return (
                                <Accordion
                                    key={index}
                                    ref={(el) => {
                                        inputRefs.current[index] = el;
                                    }}
                                    expanded={expanded === `panel${index}`}
                                    id="accordionCard"
                                    onChange={handleAccordionChange(`panel${index}`, index)}
                                    sx={{
                                        margin: '0 26px 16px !important',
                                        borderRadius: '15px !important',
                                        '&:before': {
                                            display: 'none',
                                        },
                                        boxShadow: 'rgba(0, 0, 0, 0.16) 0px 1px 4px'
                                    }}
                                >
                                    <AccordionSummary className='iconExplore'
                                        expandIcon={<ExpandMoreIcon style={{
                                            borderRadius: '50%',
                                            border: '1px solid',
                                            width: '30px',
                                            height: '30px',
                                            padding: '5px',
                                            transform: 'rotate(270deg)',
                                            margin: '0px 10px 0 0',
                                        }} />}
                                        style={{
                                            borderRadius: expanded === `panel${index}` ? '15px 15px 0 0' : '15px',

                                            flexDirection: 'row-reverse',
                                            '& .MuiAccordionSummary-expandIconWrapper': {
                                                marginRight: '16px',
                                            }
                                        }}
                                    >
                                        <Typography
                                            variant="body1" id="questionDropdown"
                                            sx={{ flexGrow: 1, fontWeight: "bold" }}
                                            dangerouslySetInnerHTML={{ __html: data.question_text }}

                                        />
                                        <Box>
                                            {/* {data.userResponse?.isCorrect === 'incorrect' || !data.userResponse?.isCorrect ? (
                                                                <CancelIcon sx={{ color: 'red' }} />
                                                            ) :
                                                                <CheckCircleIcon sx={{ color: 'green' }} />
                                                            } */}

                                            {data.response === 'incorrect' && (
                                                <CancelIcon sx={{ color: '#ea4b67' }} />
                                            )}
                                            {data.response === 'correct' && (
                                                <CheckCircleIcon sx={{ color: '#70bb9c' }} />
                                            )}
                                            {data.response === 'null' || data.response === null && (

                                                <ErrorIcon sx={{ color: '#bfbbbb' }} />
                                            )}

                                        </Box>


                                    </AccordionSummary>
                                    <AccordionDetails>
                                        <Divider />
                                        <Box sx={{ display: 'flex', spaceBetween: '1', margin: '10px 0px', marginLeft: '12px' }}>
                                            <Typography sx={{ alignItems: 'center', display: 'flex', marginRight: '20px' }}>
                                                <HourglassBottomIcon sx={{ marginRight: "5px" }} /> Time spent  : {data?.time_taken ? formatTimeTimerDisplay(data?.time_taken) : '0 min 0 sec'}
                                            </Typography>

                                        </Box>

                                        <Box sx={{ marginLeft: '17px' }}>
                                            {/* <Typography sx={{ marginTop: '10px', marginBottom: '5px' }}>
                                                            Instructions :
                                                        </Typography>
                                                        <Typography sx={{ marginBottom: '10px' }}>
                                                            {data?.instruction ? data?.instruction : 'To prepare your JSON for translation using your LLM function — only translating values like courseModuleName, courseSubmoduleName, and courseSubmoduleTopics — you can extract those values, translate them, and then reinsert them into the original structure Here’s a Python function to do that:'}
                                                        </Typography> */}
                                        </Box>
                                        <Divider />
                                        <Box sx={{ marginLeft: '17px' }}>
                                            <Typography variant="subtitle2" fontWeight="bold" sx={{ marginTop: '10px', marginBottom: '8px' }}>
                                                <span style={{ position: 'relative', top: '3px', fontSize: '15px' }}>Question</span>   <Button style={{ color: '#fff', background: '#437bfc', borderRadius: '5px', marginLeft: '25px', width: '124px', padding: '4px', fontSize: '15px' }} onClick={toggleSection}>{isShow ? 'Hide Answer' : 'Show Answer'}</Button>
                                            </Typography>
                                            <Typography variant="subtitle2" id="questionWithAnswer" fontWeight="bold" sx={{ marginTop: '10px', marginBottom: '8px' }}
                                                dangerouslySetInnerHTML={{ __html: data.question_text }} />

                                            {parsedOptions && parsedOptions.mcqOptions && parsedOptions.mcqOptions.map((option, idx) => {
                                                const isSelected = data.selectedanswer === option;
                                                const isCorrect = parsedOptions.correctAnswer?.[idx];


                                                let circleColor = '';
                                                let textColor = '';
                                                let fontWeight = 'normal';

                                                if (!isShow) {
                                                    if (isSelected) {
                                                        circleColor = '#888';
                                                    }
                                                } else {
                                                    if (isCorrect) {
                                                        circleColor = 'green';
                                                        textColor = 'green';
                                                        fontWeight = 'bold';
                                                    } else if (isSelected && !isCorrect) {
                                                        circleColor = 'red';
                                                        textColor = 'red';
                                                        fontWeight = 'bold';
                                                    }
                                                }

                                                return (
                                                    <Box key={idx} display="flex" alignItems="center" mb={1} sx={{ marginLeft: '1px' }}>
                                                        <Box style={{ width: '30px' }}>
                                                            <span style={{
                                                                border: `1px solid ${circleColor || '#aaa'}`, // Use color or default gray
                                                                borderRadius: '50%',
                                                                height: '18px',
                                                                width: '19px',
                                                                display: 'flex',
                                                                justifyContent: 'center',
                                                                alignItems: 'center',
                                                                marginRight: '12px',
                                                            }}>
                                                                <span style={{
                                                                    width: '7px',
                                                                    height: '7px',
                                                                    borderRadius: '50%',
                                                                    background: circleColor,
                                                                }} id="loremDot" />
                                                            </span>
                                                        </Box>
                                                        <Typography
                                                            variant="body2"
                                                            sx={{
                                                                fontWeight: fontWeight,
                                                                color: textColor || 'inherit',
                                                            }}
                                                            dangerouslySetInnerHTML={{ __html: option }}
                                                        />
                                                    </Box>
                                                );
                                            })}
                                        </Box>
                                        <Divider />
                                        {isShow && data?.justification && (
                                            <Box mt={2}>
                                                <Typography variant="subtitle2" fontWeight="bold">
                                                    Explanation:
                                                </Typography>
                                                <Typography id="ExplanationDiv" variant="body2" sx={{ paddingLeft: '45px', textAlignLast: 'start', fontWeight: '550', marginTop: '10px' }}
                                                    dangerouslySetInnerHTML={{ __html: data.justification }} />
                                            </Box>
                                        )}


                                    </AccordionDetails>

                                </Accordion>
                            )
                        })
                            :
                            (
                                <Typography variant="body2" color="textSecondary" sx={{ mt: 2, textAlign: 'center' }}>
                                    Please attempt the assessment to know the Solutions!
                                </Typography>
                            )
                        }
                        {neetAnalysis?.biology && neetAnalysis?.biology?.length > questionsPerPage && (
                            <Box display="flex" justifyContent="center" mt={2}>
                                <Button sx={{ color: (currentPageBiologyQuestions > 0) ? '#0b57d0' : "" }}
                                    disabled={currentPageBiologyQuestions === 0}
                                    onClick={() => setCurrentPageBiologyQuestions(prev => prev - 1)}
                                >
                                    ← Prev
                                </Button>
                                <Typography mx={2} sx={{ alignItems: 'center', alignSelf: 'center' }}>
                                    {currentPageBiologyQuestions + 1} of {Math.ceil(neetAnalysis?.biology?.length / questionsPerPage)}
                                </Typography>
                                <Button sx={{ color: (endIndexBiologyQuestions <= neetAnalysis?.biology?.length) ? '#0b57d0' : "" }}
                                    disabled={endIndexBiologyQuestions >= neetAnalysis?.biology?.length}
                                    onClick={() => setCurrentPageBiologyQuestions(prev => prev + 1)}
                                >
                                    Next →
                                </Button>
                            </Box>
                        )}
                    </Box>

                </>
            }
            {tabIndex === 4 &&
                <>


                    <Box >
                        <Grid container spacing={2}>
                            <Grid item xs={12} sm={12}>
                                <Grid container gap={1} spacing={2} sx={{ padding: "0px !important", justifyContent: 'space-between', margin: '0 !important', marginBottom: '15px !important', overflow: "hidden", width: "100% !important" }}>
                                    <Grid item xs={12} sm={4} md={3} lg={2} sx={{ boxShadow: 'rgba(99, 99, 99, 0.2) 0px 2px 8px 0px', background: '#fff', minHeight: '100px', marginBottom: '10px', borderRadius: '10px' }}>
                                        <Typography variant="subtitle1" fontWeight={700} textAlign={'center'}>
                                            Total Marks:
                                        </Typography>
                                        <Typography variant="h5" textAlign={'center'} sx={{ fontSize: { xs: '20px', sm: '22px', md: '24px', lg: '28px', } }} fontWeight={900}>
                                            {totalMarksChemistry && totalMarksChemistry}
                                        </Typography>
                                    </Grid>
                                    <Grid item xs={12} sm={4} md={3} lg={2} sx={{ boxShadow: 'rgba(99, 99, 99, 0.2) 0px 2px 8px 0px', background: '#fff', minHeight: '100px', marginBottom: '10px', borderRadius: '10px' }}>
                                        <Typography variant="subtitle1" fontWeight={700} textAlign={'center'}>
                                            Total Question Answered:
                                        </Typography>
                                        <Typography variant="h5" textAlign={'center'} sx={{ fontSize: { xs: '20px', sm: '22px', md: '24px', lg: '28px', } }} fontWeight={900}>
                                            {totalAnsweredChemistry > 0 ? (
                                                `${totalAnsweredChemistry} / ${totalChemistryQuestions}`
                                            ) : (
                                                '0 / 45'
                                            )}
                                        </Typography>
                                    </Grid>
                                    <Grid item xs={12} sm={4} md={3} lg={2} sx={{ boxShadow: 'rgba(99, 99, 99, 0.2) 0px 2px 8px 0px', background: '#fff', minHeight: '100px', marginBottom: '10px', borderRadius: '10px' }}>
                                        <Box display="flex" alignItems="center" gap={1} justifyContent="center">
                                            <CheckCircleIcon color="success" fontSize="small" />
                                            <Typography variant="subtitle1" fontWeight={700}>
                                                Correct
                                            </Typography>
                                        </Box>
                                        <Typography variant="h5" textAlign={'center'} sx={{ fontSize: { xs: '20px', sm: '22px', md: '24px', lg: '28px', } }} fontWeight={900}>
                                            {totalCorrectChemistry && totalCorrectChemistry}

                                        </Typography>
                                    </Grid>
                                    <Grid item xs={12} sm={4} md={3} lg={2} sx={{ boxShadow: 'rgba(99, 99, 99, 0.2) 0px 2px 8px 0px', background: '#fff', minHeight: '100px', marginBottom: '10px', borderRadius: '10px' }}>
                                        <Box display="flex" alignItems="start" gap={1} justifyContent="center">
                                            <CancelIcon color="error" fontSize="small" />
                                            <Typography variant="subtitle1" fontWeight={700}>
                                                Incorrect
                                            </Typography>
                                        </Box>
                                        <Typography variant="h5" textAlign={'center'} sx={{ fontSize: { xs: '20px', sm: '22px', md: '24px', lg: '28px', } }} fontWeight={900}>
                                            {totalIncorrectChemistry && totalIncorrectChemistry}
                                        </Typography>
                                    </Grid>
                                    <Grid item xs={12} sm={4} md={3} lg={2} sx={{ boxShadow: 'rgba(99, 99, 99, 0.2) 0px 2px 8px 0px', background: '#fff', minHeight: '100px', marginBottom: '10px', borderRadius: '10px' }}>
                                        <Box display="flex" alignItems="start" gap={1} justifyContent="center">
                                            <TrackChangesIcon sx={{ color: "#a1c5ff" }} fontSize="small" />
                                            <Typography variant="subtitle1" fontWeight={700}>
                                                Accuracy
                                            </Typography>
                                        </Box>
                                        <Typography variant="h5" textAlign={'center'} sx={{ fontSize: { xs: '20px', sm: '22px', md: '24px', lg: '28px', } }} fontWeight={900}>
                                            {averageAccuracyChemistry ? `${averageAccuracyChemistry.toFixed(2)}%` : "0%"}
                                        </Typography>
                                    </Grid>
                                </Grid>

                            </Grid>

                        </Grid>
                    </Box>

                    <Paper elevation={3} sx={{ p: 2, mb: 4 }}>
                        <Typography variant="h6">Time Spent</Typography>
                        {chemistryChartData.series[0]?.data.length > 0 ? (
                            <>
                                <BarChart
                                    height={350}
                                    series={[{ ...chemistryChartData.series[0], color: '#fc9f06', data: paginatedDataChem }]}
                                    xAxis={[{
                                        scaleType: 'band',
                                        data: paginatedLabelsChem,
                                        barGapRatio: 0,
                                        tickLabelStyle: {
                                            textOverflow: 'ellipsis',
                                            whiteSpace: 'pre',
                                            fontSize: xAxisFontSize,
                                            dy: 50,
                                        },
                                        categoryGapRatio: 0.9,
                                    }]}
                                    yAxis={[{
                                        label: 'Average Time(in seconds)',
                                        tickLabelStyle: { fontSize: 12 }
                                    }]}
                                    margin={{ left: 50, right: 30, top: 30, bottom: 50 }}
                                    barSeries={[{ barWidth: '50%' }]}
                                    slotProps={{
                                        bar: {
                                            style: {
                                                width: 25,
                                            },
                                        },
                                    }}
                                    tooltip={{
                                        trigger: 'item',
                                        formatter: (value, context) => {
                                            return paginatedTooltipsChem[context.dataIndex] || 'N/A';
                                        }
                                    }}
                                />
                                <Box display="flex" justifyContent="center" mt={2}>
                                    <Button sx={{ color: (currentPageChem > 0) ? '#0b57d0' : "" }}
                                        disabled={currentPageChem === 0}
                                        onClick={() => setCurrentPageChem(prev => prev - 1)}
                                    >
                                        ← Prev
                                    </Button>
                                    <Typography mx={2} sx={{ alignItems: 'center', alignSelf: 'center' }}>{currentPageChem + 1} of {Math.ceil(formattedXAxisLabelsChem.length / itemsPerPage)}</Typography>
                                    <Button sx={{ color: (endIndexChem <= formattedXAxisLabelsChem.length) ? '#0b57d0' : "" }}
                                        disabled={endIndexChem >= formattedXAxisLabelsChem.length}
                                        onClick={() => setCurrentPageChem(prev => prev + 1)}
                                    >
                                        Next →
                                    </Button>
                                </Box>
                            </>
                        ) : (
                            <Typography variant="body2" color="textSecondary" sx={{ mt: 2 }}>
                                Please attempt the assessment to know the analytics!
                            </Typography>
                        )}
                    </Paper>

                    <Box sx={{ mt: 3 }} id="commonShadow">
                        <Typography variant="h6" gutterBottom>Chapter Wise Performance</Typography>
                        <Table sx={{ mb: 1 }}>
                            <TableHead>
                                <TableRow style={{ backgroundColor: '#ccdbff', borderRadius: '10px' }}>
                                    <TableCell>Chapter Name</TableCell>
                                    <TableCell align="center">Correct</TableCell>
                                    <TableCell align="center">Total Answered</TableCell>
                                    <TableCell align="center">Accuracy</TableCell>
                                    <TableCell align="center">Total Questions</TableCell>
                                    <TableCell align="center">Avg Time</TableCell>
                                </TableRow>
                            </TableHead>
                            <TableBody>
                                {chemistryStats.slice().sort((a, b) => a.chapterName.localeCompare(b.chapterName)).map((chapter, index) => (
                                    <TableRow key={index}>
                                        <TableCell>{chapter?.chapterName}</TableCell>
                                        <TableCell align="center">{chapter?.correct}</TableCell>
                                        <TableCell align="center">{chapter?.totalAnswered === 0 ? '-' : chapter?.totalAnswered}</TableCell>
                                        <TableCell align="center">{chapter?.accuracy === 0 ? '-' : chapter?.accuracy}</TableCell>
                                        <TableCell align="center">{chapter?.totalQuestions}</TableCell>
                                        <TableCell align="center">{chapter?.avgTime === 0 ? '-' : `${chapter?.avgTime}sec`}</TableCell>

                                    </TableRow>
                                ))}
                                {chemistryStats.length === 0 && (
                                    <TableRow>
                                        <TableCell colSpan={6} align="center">Please attempt the assessment to know the analytics! .</TableCell>
                                    </TableRow>
                                )}
                            </TableBody>
                        </Table>
                    </Box>

                    <Box sx={{
                        padding: 2, boxShadow: '0px 3px 3px -2px rgba(145, 158, 171, 0.2), 0px 3px 4px 0px rgba(145, 158, 171, 0.14), 0px 1px 8px 0px rgba(145, 158, 171, 0.12)',
                        margin: '0', background: '#fff', paddingTop: '10px !important', borderRadius: '16px', marginBottom: '20px', paddingBottom: '15px !important'
                    }}>
                        <Typography variant="h6" sx={{ fontWeight: 'bold', marginBottom: '20px', marginTop: '20px', marginLeft: '30px' }}>{"Questions and Solutions"}</Typography>
                        {neetAnalysis?.chemistry && neetAnalysis?.chemistry?.length > 0 ? neetAnalysis?.chemistry?.slice(startIndexChemistryQuestions, endIndexChemistryQuestions).map((data, index) => {
                             let parsedOptions;

                             parsedOptions = typeof data.options === "string" ? JSON.parse(data.options) : data.options;
                            return (
                                <Accordion
                                    key={index}
                                    ref={(el) => {
                                        inputRefs.current[index] = el;
                                    }}
                                    expanded={expanded === `panel${index}`}
                                    id="accordionCard"
                                    onChange={handleAccordionChange(`panel${index}`, index)}
                                    sx={{
                                        margin: '0 26px 16px !important',
                                        borderRadius: '15px !important',
                                        '&:before': {
                                            display: 'none',
                                        },
                                        boxShadow: 'rgba(0, 0, 0, 0.16) 0px 1px 4px'
                                    }}
                                >
                                    <AccordionSummary className='iconExplore'
                                        expandIcon={<ExpandMoreIcon style={{
                                            borderRadius: '50%',
                                            border: '1px solid',
                                            width: '30px',
                                            height: '30px',
                                            padding: '5px',
                                            transform: 'rotate(270deg)',
                                            margin: '0px 10px 0 0',
                                        }} />}
                                        style={{
                                            borderRadius: expanded === `panel${index}` ? '15px 15px 0 0' : '15px',

                                            flexDirection: 'row-reverse',
                                            '& .MuiAccordionSummary-expandIconWrapper': {
                                                marginRight: '16px',
                                            }
                                        }}
                                    >
                                        <Typography
                                            variant="body1" id="questionDropdown"
                                            sx={{ flexGrow: 1, fontWeight: "bold" }}
                                            dangerouslySetInnerHTML={{ __html: data.question_text }}

                                        />
                                        <Box>


                                            {data.response === 'incorrect' && (
                                                <CancelIcon sx={{ color: '#ea4b67' }} />
                                            )}
                                            {data.response === 'correct' && (
                                                <CheckCircleIcon sx={{ color: '#70bb9c' }} />
                                            )}
                                            {data.response === 'null' || data.response === null && (

                                                <ErrorIcon sx={{ color: '#bfbbbb' }} />
                                            )}

                                        </Box>


                                    </AccordionSummary>
                                    <AccordionDetails>
                                        <Divider />
                                        <Box sx={{ display: 'flex', spaceBetween: '1', margin: '10px 0px', marginLeft: '12px' }}>
                                            <Typography sx={{ alignItems: 'center', display: 'flex', marginRight: '20px' }}>
                                                <HourglassBottomIcon sx={{ marginRight: "5px" }} /> Time spent  : {data?.time_taken ? formatTimeTimerDisplay(data?.time_taken) : '0 min 0 sec'}
                                            </Typography>

                                        </Box>

                                        <Box sx={{ marginLeft: '17px' }}>
                                            {/* <Typography sx={{ marginTop: '10px', marginBottom: '5px' }}>
                                                            Instructions :
                                                        </Typography>
                                                        <Typography sx={{ marginBottom: '10px' }}>
                                                            {data?.instruction ? data?.instruction : 'To prepare your JSON for translation using your LLM function — only translating values like courseModuleName, courseSubmoduleName, and courseSubmoduleTopics — you can extract those values, translate them, and then reinsert them into the original structure Here’s a Python function to do that:'}
                                                        </Typography> */}
                                        </Box>
                                        <Divider />
                                        <Box sx={{ marginLeft: '17px' }}>
                                            <Typography variant="subtitle2" fontWeight="bold" sx={{ marginTop: '10px', marginBottom: '8px' }}>
                                                <span style={{ position: 'relative', top: '3px', fontSize: '15px' }}>Question</span>   <Button style={{ color: '#fff', background: '#437bfc', borderRadius: '5px', marginLeft: '25px', width: '124px', padding: '4px', fontSize: '15px' }} onClick={toggleSection}>{isShow ? 'Hide Answer' : 'Show Answer'}</Button>
                                            </Typography>
                                            <Typography variant="subtitle2" id="questionWithAnswer" fontWeight="bold" sx={{ marginTop: '10px', marginBottom: '8px' }}
                                                dangerouslySetInnerHTML={{ __html: data.question_text }} />


                                            {parsedOptions && parsedOptions.mcqOptions && parsedOptions.mcqOptions.map((option, idx) => {
                                                const isSelected = data.selectedanswer === option;
                                                const isCorrect = parsedOptions.correctAnswer?.[idx];


                                                let circleColor = '';
                                                let textColor = '';
                                                let fontWeight = 'normal';

                                                if (!isShow) {
                                                    if (isSelected) {
                                                        circleColor = '#888';
                                                    }
                                                } else {
                                                    if (isCorrect) {
                                                        circleColor = 'green';
                                                        textColor = 'green';
                                                        fontWeight = 'bold';
                                                    } else if (isSelected && !isCorrect) {
                                                        circleColor = 'red';
                                                        textColor = 'red';
                                                        fontWeight = 'bold';
                                                    }
                                                }

                                                return (
                                                    <Box key={idx} display="flex" alignItems="center" mb={1} sx={{ marginLeft: '1px' }}>
                                                        <Box style={{ width: '30px' }}>
                                                            <span style={{
                                                                border: `1px solid ${circleColor || '#aaa'}`, // Use color or default gray
                                                                borderRadius: '50%',
                                                                height: '18px',
                                                                width: '19px',
                                                                display: 'flex',
                                                                justifyContent: 'center',
                                                                alignItems: 'center',
                                                                marginRight: '12px',
                                                            }}>
                                                                <span style={{
                                                                    width: '7px',
                                                                    height: '7px',
                                                                    borderRadius: '50%',
                                                                    background: circleColor,
                                                                }} id="loremDot" />
                                                            </span>
                                                        </Box>
                                                        <Typography
                                                            variant="body2"
                                                            sx={{
                                                                fontWeight: fontWeight,
                                                                color: textColor || 'inherit',
                                                            }}
                                                            dangerouslySetInnerHTML={{ __html: option }}
                                                        />
                                                    </Box>
                                                );
                                            })}

                                        </Box>
                                        <Divider />
                                        {isShow && data?.justification && (
                                            <Box mt={2}>
                                                <Typography variant="subtitle2" fontWeight="bold">
                                                    Explanation:
                                                </Typography>
                                                <Typography id="ExplanationDiv" variant="body2" sx={{ paddingLeft: '45px', textAlignLast: 'start', fontWeight: '550', marginTop: '10px' }}
                                                    dangerouslySetInnerHTML={{ __html: data.justification }} />
                                            </Box>
                                        )}


                                    </AccordionDetails>

                                </Accordion>
                            )
                        })
                            :
                            (
                                <Typography variant="body2" color="textSecondary" sx={{ mt: 2, textAlign: 'center' }}>
                                    Please attempt the assessment to know the Solutions!
                                </Typography>
                            )
                        }
                        {neetAnalysis?.chemistry && neetAnalysis?.chemistry?.length > questionsPerPage && (
                            <Box display="flex" justifyContent="center" mt={2}>
                                <Button sx={{ color: (currentPageChemistryQuestions > 0) ? '#0b57d0' : "" }}
                                    disabled={currentPageChemistryQuestions === 0}
                                    onClick={() => setCurrentPageChemistryQuestions(prev => prev - 1)}
                                >
                                    ← Prev
                                </Button>
                                <Typography mx={2} sx={{ alignItems: 'center', alignSelf: 'center' }}>
                                    {currentPageChemistryQuestions + 1} of {Math.ceil(neetAnalysis?.chemistry?.length / questionsPerPage)}
                                </Typography>
                                <Button sx={{ color: (endIndexChemistryQuestions <= neetAnalysis?.chemistry?.length) ? '#0b57d0' : "" }}
                                    disabled={endIndexChemistryQuestions >= neetAnalysis?.chemistry?.length}
                                    onClick={() => setCurrentPageChemistryQuestions(prev => prev + 1)}
                                >
                                    Next →
                                </Button>
                            </Box>
                        )}
                    </Box>
                </>
            }

        </Box>
    );

};

export default NeeAssessmentCourseDetails;

const useStyles = makeStyles((theme) => ({
    badgeShower: {
        background: '#ddd',
        padding: '4px 8px',
        borderRadius: '5px',
        borderCollapse: 'collapse',
        marginRight: '15px',
        display: 'inline-flex'
    },
    tableRow: {
        border: '1px solid #ddd',
        padding: '10px',
        textAlign: 'left',
        borderCollapse: 'collapse',
        textTransform: 'capitalize'
    },
    assessmentContainer: {
        width: '100%',
        maxWidth: '600px',
        background: 'white',
        padding: '20px',
        borderRadius: '10px',
        boxShadow: '0px 4px 10px rgba(0, 0, 0, 0.1)',
        display: 'flex',
    },
}));
