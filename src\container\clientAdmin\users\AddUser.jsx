/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable react/prop-types */
import React, { useState, useEffect } from 'react';
import { Grid, Typography, TextField, Checkbox, FormControlLabel, TextareaAutosize, MenuItem } from '@mui/material';
import { Form, Formik } from 'formik';
import * as Yup from 'yup';
import { LoadingButton } from '@mui/lab';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import SnackBar from '../../../components/snackbar/snackbar';
import userServices from '../../../services/clientAdmin/userServices';
import ErrorFocus from '../../../components/ErrorFocus/ErrorFocus';
import PhoneNumber from '../../../components/PhoneNumber/Index';
import '../../../components/PhoneNumber/styles.css';



export default function CreateAdminForm(props) {
  console.log(props?.assessmentList,"props");
  
  // const phoneNumber = `1${""}`
  const [loading, setLoading] = useState(false);
  const [openSnackbar, setOpenSnackbar] = useState(false);
  const [snackbarTitle, setSnackbarTitle] = React.useState('');


  const [formValues, setFormValues] = React.useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    dateOfBirth: null,
    classroom: '',
    assignCourse: [],
    assignAssessment: [],
    specialAttention: false,
    personalNotes: [{ date: new Date(), value: '' }],
    languageCode: ''
  });

  const handleAddClientFormValues = async (values) => {
    if (values.personalNotes && Array.isArray(values.personalNotes)) {
      values.personalNotes = values.personalNotes.map(note => {
        if (note.value === "") {
          return { date: "", value: "" };
        }
        return note;
      });
    }
    setLoading(true);
    const payload = {
      firstName: values.firstName,
      lastName: values.lastName,
      email: values.email,
      phone: values.phone,
      role: 'AUTH_USER',
      specialAttention: values.specialAttention,
      personalNotes: values.personalNotes ,
      birth_date: values.dateOfBirth,
      planId: values.assignCourse,
      assessmentId: values.assignAssessment,
      languageCode: values.languageCode
    };
    if (props.mode === 'create') {
      try {
        const response = await userServices.postUsersDetails(payload);

        
        if (response.ok) {
          props.getUsersDetails();
          setOpenSnackbar(true);
          setSnackbarTitle("User Profile Created Successfully")
          props.snackBarControl(response.data.message);
          props.handleModalClose();
          setLoading(false);
        } else if (response.status === 400) {
          if(response.data.message === 'Email already exists'){
            setOpenSnackbar(true);
            setSnackbarTitle("Email already exists")

          }
          else{
            setOpenSnackbar(true);
            props.snackBarControl(response.data.message);
          }
         
        }
      } catch (error) {
        console.log(error);
        setOpenSnackbar(false);
      }
    } else {
      try {
        const response = await userServices.editUserDetailsByID(props.id, payload);
        if (response.ok) {
          setOpenSnackbar(true);
          props.snackBarControl(response.data.message);
          props.getUsersDetails();
          props.handleModalClose();
          setLoading(false);
        } else if (response.status === 400) {
          setOpenSnackbar(true);
          props.snackBarControl(response.data.message);
          props.handleModalClose();
        }
      } catch (error) {
        console.log(error);
        setOpenSnackbar(false);
      }
    }
    setLoading(false);
  };
  const getUserDetalsById = async () => {
    const result = await userServices.getUserDetalsById(props.id);
    if (result.ok) {
      const data = { ...formValues };
      data.firstName = result.data.firstName;
      data.lastName = result.data.lastName;
      data.email = result.data.email;
      data.phone = result.data.phone;
      setFormValues(data);
    }
  };
  useEffect(() => {
    if (props.mode === 'edit') {
      getUserDetalsById();
    }
  }, []);

  
  return (
    <div>
      <Formik
        enableReinitialize
        initialValues={formValues}
        validationSchema={Yup.object().shape({
          firstName: Yup.string().trim().min(3).matches(/^[A-Za-z ]+$/, "Only alphabets allowed").required('Required'),
          lastName: Yup.string().trim().matches(/^[A-Za-z ]+$/, "Only alphabets allowed"),
          email: Yup.string().required('Required').email(),
          phone: Yup.string().trim().nullable().min(11).max(12)
            .matches('^([0|[0-9]{1,5})?([2-9][0-9]{9})$', 'Invalid Phone number')
        })}
        onSubmit={handleAddClientFormValues}
      >
        {({ values, handleChange, handleBlur, setFieldValue, touched, errors }) => (
          <Form>
            

            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle1">First Name*</Typography>
                <TextField
                  fullWidth
                  name="firstName"
                  value={values.firstName}
                  onChange={handleChange}
                  onBlur={handleBlur}
                  error={Boolean(touched.firstName && errors.firstName)}
                  helperText={touched.firstName && errors.firstName}
                />
                <ErrorFocus />
              </Grid>

              {/* Last Name */}
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle1">Last Name</Typography>
                <TextField
                  fullWidth
                  name="lastName"
                  value={values.lastName}
                  onChange={handleChange}
                  onBlur={handleBlur}
                  error={Boolean(touched.lastName && errors.lastName)}
                  helperText={touched.lastName && errors.lastName}
                />
              </Grid>

              {/* Email */}
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle1">Email*</Typography>
                <TextField
                  fullWidth
                  name="email"
                  disabled={props.mode === 'edit'}
                  value={values.email}
                  onChange={handleChange}
                  onBlur={handleBlur}
                  error={Boolean(touched.email && errors.email)}
                  helperText={touched.email && errors.email}
                />
              </Grid>

              {/* Phone */}
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle1">Phone</Typography>
                <PhoneNumber fullWidth country="us" specialLabel="" />
              </Grid>

              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle1">Date of Birth</Typography>
                <LocalizationProvider dateAdapter={AdapterDateFns}>
                  <DatePicker fullWidth
                    value={values.dateOfBirth ? values.dateOfBirth : null}
                    onChange={(date) => setFieldValue('dateOfBirth', date?.toISOString())}
                    slotProps={{ textField: { fullWidth: true } }}
                    maxDate={new Date()}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        placeholder="MM/DD/YYYY"
                        inputProps={{
                          ...params.inputProps,
                          readOnly: true,
                        }}
                        fullWidth
                        sx={{
                          'input::placeholder': {
                            textTransform: 'uppercase',
                          },
                          '& .MuiOutlinedInput-root': {
                            '& fieldset': {
                              borderColor: 'black',
                            },
                            '&:hover fieldset': {
                              borderColor: 'black',
                            },
                            '&.Mui-focused fieldset': {
                              borderColor: 'black',
                            },
                          },
                        }}
                      />
                    )}
                  />
                </LocalizationProvider>
              </Grid>

              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle1" gutterBottom>
                  Assign Course
                </Typography>
                <TextField
                  select
                  fullWidth
                  name="assignCourse"
                  value={values.assignCourse}
                  onChange={handleChange}
                  onBlur={handleBlur}
                  variant="outlined"
                  SelectProps={{
                    multiple: true
                  }}
                  error={Boolean(touched.assignCourse && errors.assignCourse)}
                  helperText={touched.assignCourse && errors.assignCourse}
                >
                  {props?.courseList?.length > 0 &&
                    props.courseList.map((option) => (
                      <MenuItem disabled={+option?.noOfLicenseAllocated === +option?.noOfLicenseAssigned} key={option.id} value={option.id}>
                        {option.courseName}
                      </MenuItem>
                    ))}

                </TextField>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle1" gutterBottom>
                  Assign Assessment
                </Typography>
                <TextField
                  select
                  fullWidth
                  name="assignAssessment"
                  value={values.assignAssessment}
                  onChange={handleChange}
                  onBlur={handleBlur}
                  variant="outlined"
                  SelectProps={{
                    multiple: true
                  }}
                  error={Boolean(touched.assignAssessment && errors.assignAssessment)}
                  helperText={touched.assignAssessment && errors.assignAssessment}
                >
                  {props?.assessmentList?.length > 0 &&
                    props.assessmentList.map((option1) => (
                      <MenuItem disabled={+option1?.noOfLicenseAllocated === +option1?.noOfLicenseAssigned} key={option1.id} value={option1.id}>
                        {option1?.assessmentTitle || option1.title || option1.planName}
                      </MenuItem>
                    ))}

                </TextField>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle1" gutterBottom>
                  Language
                </Typography>
                <TextField
                  select
                  fullWidth
                  name="languageCode" 
                  value={values.languageCode}
                  onChange={handleChange}
                  onBlur={handleBlur}
                  variant="outlined"
                  error={Boolean(touched.languageCode && errors.languageCode)}
                  helperText={touched.languageCode && errors.languageCode}
                >
                  {props.languageCode.map((option) => (
                    <MenuItem key={option.value} value={option.value}>
                      {option.code.charAt(0).toUpperCase() + option.code.slice(1)}
                    </MenuItem>
                  ))}
                </TextField>
              </Grid>


              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle1">Special Attention</Typography>
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={values.specialAttention}
                      onChange={(e) => setFieldValue('specialAttention', e.target.checked)}
                    />
                  }
                  label="User needs special attention"
                />
              </Grid>

              {values.specialAttention && (
                <Grid item xs={12}>
                  <Typography variant="subtitle1">Personal Notes</Typography>
                  <TextareaAutosize
                    minRows={4}
                    style={{ width: '100%', padding: 10 }}
                    name="personalNotes"
                    value={values.personalNotes?.[0]?.value || ''}
                    onChange={(e) => {
                      setFieldValue('personalNotes', [
                        {
                          ...values.personalNotes?.[0],
                          value: e.target.value,
                          date: values.personalNotes?.[0]?.date || new Date(),
                        },
                      ]);
                    }}
                  />
                </Grid>
              )}
              <Grid item xs={12}>
                <Grid container spacing={2}>
                  <Grid item xs={6}>
                    <LoadingButton
                      fullWidth
                      size="medium"
                      type="submit"
                      variant="contained"
                      loading={loading}
                    >
                      {props.mode === 'create' ? 'Add' : 'Update'}
                    </LoadingButton>
                  </Grid>
                  <Grid item xs={6}>
                    <LoadingButton
                      fullWidth
                      size="medium"
                      variant="outlined"
                      color="secondary"
                      onClick={props.handleModalClose}
                    >
                      Cancel
                    </LoadingButton>
                  </Grid>
                </Grid>
              </Grid>
            </Grid>
          </Form>
        )}
      </Formik>
      <SnackBar open={openSnackbar} snackbarTitle={snackbarTitle}  close={() => setOpenSnackbar(false)} />
    </div>
  );
}
