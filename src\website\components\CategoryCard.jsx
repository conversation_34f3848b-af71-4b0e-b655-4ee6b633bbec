import * as React from 'react';
import { styled } from '@mui/material/styles';
import Card from '@mui/material/Card';
import CardHeader from '@mui/material/CardHeader';
import CardContent from '@mui/material/CardContent';
import Avatar from '@mui/material/Avatar';
import IconButton, { IconButtonProps } from '@mui/material/IconButton';
import { Typography, Box } from '@mui/material';
import { makeStyles } from '@mui/styles';

const ExpandMore = styled((props) => {
  const { expand, ...other } = props;
  return <IconButton {...other} />;
})(({ theme, expand }) => ({
  transform: !expand ? 'rotate(0deg)' : 'rotate(180deg)',
  marginLeft: 'auto',
  transition: theme.transitions.create('transform', {
    duration: theme.transitions.duration.shortest,
  }),
}));

export default function CategoryCard(props) {
  const classes = useStyles();
  const [expanded, setExpanded] = React.useState(false);

  const handleExpandClick = () => {
    setExpanded(!expanded);
  };

  return (
    <Card
      sx={{
        // width: '150px',
        display: 'flex',
        flexDirection: 'column',
        // minHeight: '520px',
        background: '#FFFFFF',
        borderRadius: '10px',
      }}
    >
      <Box sx={{ margin: '1rem' }}>
        <img src={props.image} alt="pic" width="100%" height="180px" />
      </Box>

      <CardContent>
        <Typography variant="subtitle1" className={classes.cardTitle} gutterBottom>
          {props.title}
        </Typography>

        <Typography
          variant="body1"
          sx={{ color: props.color ? props?.color : '#101828' }}
          className={classes.cardSubTitle}
          gutterBottom
        >
          {props.subTitle}
        </Typography>
        <Typography variant="body1" className={classes.cardContent} gutterBottom>
          {props.content}
        </Typography>
      </CardContent>
    </Card>
  );
}

const useStyles = makeStyles((theme) => ({
  cardTitle: {
    fontWeight: '600',
    fontSize: '0.8rem',
    color: '#00B673;',
  },
  cardSubTitle: {
    fontWeight: '600',
    fontSize: '1.5rem',
  },
  cardContent: {
    fontWeight: 400,
    fontSize: '1rem',
    color: '#27292E;',
  },
}));
