import React from 'react';
import Dialog from '@mui/material/Dialog';
import { makeStyles } from '@mui/styles';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import DialogTitle from '@mui/material/DialogTitle';
import { IconButton, Typography, Box, Grid, Avatar, Button, Modal } from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import { createTheme, useTheme, ThemeProvider } from '@mui/material/styles';

import BackgroundImageModal from '../images/BackgroundImageModal.png';
import OurTeamImage from '../images/OurTeamImage.png';
import theme from '../../theme/palette';

const ImageModel = ({ children, openModel, closeModel, data }) => {
  const classes = useStyles();

  return (
    <Dialog
      fullWidth
      maxWidth={'md'}
      open={openModel}
      aria-labelledby="alert-dialog-title"
      aria-describedby="alert-dialog-description"
      sx={{
        // position: 'absolute',
        // '.css-hz1bth-MuiDialog-container': {
        //   height: 'unset',
        // },

        '.css-wrcnww-MuiPaper-root-MuiDialog-paper': {
          backgroundColor: '#ddf499 !important',
          // backgroundImage: `url(${BackgroundImageModal})`,
          // backgroundRepeat: 'no-repeat',
          // backgroundSize: '100%',
          // minHeight: '620px',

          //   boxShadow: 'none',
          //    maxWidth: '123px',
        },

        // zIndex: '999',
      }}
    >
      <div>
        <DialogTitle id="alert-dialog-title" sx={{ padding: '0px', textAlign: 'right' }}>
          <Box className={classes.combined}>
            <IconButton className={classes.icon} size="small" onClick={() => closeModel()}>
              <CloseIcon style={{ color: theme.primary.main }} fontSize="small" />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent className={classes.dialogContent}>
          <Grid container spacing={2} className={classes.selfStudyContainer}>
            <Grid
              item
              xs={12}
              md={3}
              sx={
                data?.content.length > 375
                  ? {
                      display: 'flex',
                      justifyContent: 'center',
                      flexDirection: 'center',
                      alignItems: 'center',
                    }
                  : { display: 'flex', justifyContent: 'center' }
              }
            >
              <Box>
                <Box>
                  <Avatar
                    alt="noImage"
                    src={data?.image}
                    sx={{
                      width: 180,
                      height: 180,
                      filter: 'grayscale(100%)',
                      //  boxShadow: 'rgba(0, 0, 0, 0.1) 0px 4px 12px'
                    }}
                  />
                </Box>
                <Box className={classes.alignItemsNames}>
                  <Typography variant="subtitle1" className={classes.profileName}>
                    {data?.name}
                  </Typography>

                  <Typography variant="subtitle1" className={classes.profileTitle} gutterBottom>
                    {data?.designation}
                  </Typography>
                </Box>
              </Box>
            </Grid>
            <Grid
              item
              xs={12}
              md={9}
              sx={
                data?.content.length < 390 && {
                  marginTop: '1.5rem',
                }
              }
            >
              <Box
              // sx={{
              //   paddingTop: '16px',
              // }}
              >
                {data?.content}
              </Box>
            </Grid>
          </Grid>
        </DialogContent>
      </div>
    </Dialog>
  );
};

const useStyles = makeStyles((theme) => ({
  dialog: {
    backgroundImage: `url(${BackgroundImageModal})`,
    backgroundRepeat: 'no-repeat',
    backgroundSize: 'contain',
  },
  combined: {
    paddingLeft: '22px',
    paddingRight: '22px',
    paddingTop: '20px',
    [theme.breakpoints.down('md')]: {
      marginBottom: '-22px',
    },
  },
  dialogContent: {
    marginTop: '1rem',
  },
  background: {
    fontWeight: 'bold',
    color: '#000000',
  },
  icon: {
    backgroundColor: '#FFE7D5',
    borderRadius: '50%',
    
    '&:hover': {
      backgroundColor: '#FFE7D5',
    },
  },
  title: {
    fontSize: '19px',
    fontWeight: 'bold',
    color: '#FE7000',
  },
  profileName: {
    fontSize: '1rem',
    fontWeight: 600,
    letterSpacing: ' -0.02em',
  },
  profileTitle: {
    fontSize: '0.9rem',
    fontWeight: 500,
    letterSpacing: ' -0.02em',
  },
  alignItemsNames: {
    marginTop: '1rem',
    textAlign: 'center',
  },
}));
const customeTheme = createTheme({
  components: {
    MuiDialogContent: {
      styleOverrides: {
        root: {
          paddingTop: '-1px !important',
        },
      },
    },
  },
});

export default ImageModel;
