/* eslint-disable consistent-return */
/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable react/prop-types */
/* eslint-disable no-unused-vars */
import React, { useState, useEffect } from 'react';
import { List, Box, Typography, useTheme } from '@mui/material';
import ListItemButton from '@mui/material/ListItemButton';
import ListItemIcon from '@mui/material/ListItemIcon';
import ListItemText from '@mui/material/ListItemText';
import { NavLink, useNavigate } from 'react-router-dom';
import { makeStyles } from '@mui/styles';
import { useSelector } from 'react-redux';
import './scrollStyle.css';
import LottieLoading from '../../components/LottieLoading';

import simulationApi from '../../services/simulation';

const LinkBtn = React.forwardRef((props, ref) => <NavLink to={props.to} {...props} innerRef={ref} />);

const useStyles = makeStyles((theme) => ({
  listItem: {
    borderBottom: '1px solid #BCBCBC',
    padding: 12,
    "&:hover": {
      color: "#fff"
    }
  },
  skillIqTest: {
    padding: '4px 14px',
    borderRadius: '6px',
    margin: 'auto',
    border: '1px solid #FE7000',
    width: 'max-content',
    marginTop: '1rem',
    cursor: 'pointer',
  },
}));

const VideoTopicList = ({ topics, onVideoClick, onClickCallBack, selectedIndex, submoduleId, location, onReferenceButtonClick, subModuleName, details, status, activeTopic, activeStep, screens }) => {



  const classes = useStyles();
  const theme = useTheme();
  const navigate = useNavigate();
  const userRole = useSelector((state) => state.userInfo?.role);
  // const [screens, setScreens] = useState([]);
  const [isLoading, setIsLoading] = useState(false);

  // console.log(screens,"screensscreens inside Topic");

  // React.useMemo(()=>{
  // setScreens(prevScreens =>
  //   prevScreens.map(screen =>
  //     screen.id === details?.id ? { ...screen, isCompleted: true } : screen
  //   )
  // );


  // //  setScreens(screens)
  // },[details])


  // useEffect(() => {

  //   if (location.state?.type === 'video'||location.state?.type === 'Video') {
  //     const handleKeyDown = (event) => {
  //       if (event.key === 'Enter') {
  //         //  CallBackNewTopic("fromVideoList",selectedIndex)
  //         }
  //     };
  //     window.addEventListener('keydown', handleKeyDown);
  //     return () => {
  //       window.removeEventListener('keydown', handleKeyDown);
  //     };
  //   }
  // }, [location.state?.type, screens]);

  // useEffect( async () => {
  //   setIsLoading(true)

  //   const selectedTopic = topics[0]
  //   if(selectedTopic!==undefined){
  //     await getScreens(topics[0].id, "HOTKEYS")
  //     setIsLoading(false)
  //   }


  // }, [topics]);


  //   useEffect(async () => {
  //     setIsLoading(true);

  //     if (topics && topics.length > 0) {
  //       const screenPromises = [];

  //       topics.forEach(topic => {
  //         screenPromises.push(getScreens(topic.id, "HOTKEYS"));
  //       });

  //       // Wait for all promises to resolve
  //       await Promise.all(screenPromises);
  //     }

  //     setIsLoading(false);
  // }, [topics]);

  const renderProgressLabel = (item) => {
    if (item.isCompleted && item.id !== 'reference') {
      return (
        <span style={{
          background: "#a8e0c5",
          padding: '2px 8px',
          color: "#408655",
          marginLeft: '8px',
          borderRadius: "12px",
          fontSize: '10px',
          fontWeight: '500',
          minWidth: '75px',
          border: '1px solid',
        }}>
          Completed
        </span>
      );
    }
    if (selectedIndex === item.id && item.id !== 'reference') {
      return (
        <span style={{
          background: "#FFE0B2",
          padding: '2px 8px',
          color: "#E65100",
          marginLeft: '8px',
          borderRadius: "12px",
          fontSize: '10px',
          minWidth: '75px',
          border: '1px solid',
          fontWeight: '500'
        }}>
          In Progress
        </span>
      );
    }
    return null;
  };


  const handleListItemClick = (event, index, item) => {
    if (item?.actionType?.code === 'REFERENCE') {
      onReferenceButtonClick(submoduleId, subModuleName);
    }
    else if (item?.actionType?.code === "SkillSet") {
      const path = ['AUTH_USER', 'SUPER_ADMIN', 'CONTENT_WRITER'].includes(userRole) ?
        `/app/skilliq-test?subModuleId=${submoduleId}` :
        `/auth/skilliq-test?subModuleId=${submoduleId}`;
      navigate(path, { state: { ...location.state, subModuleName } });
    }
    else if (event?._reactName === 'onClick') {
      onClickCallBack(item, index, screens, activeStep);
    }
    //   else{

    //     onClickCallBack(item, index, screens,activeStep);
    // }   
  };


  const getBase64FromUrl = async (url = 'https://ik.imagekit.io/k38yuwpb2/windows1654795623.jpeg') => {
    const data = await fetch(url);
    const blob = await data.blob();
    return new Promise((resolve) => {
      const reader = new FileReader();
      reader.readAsDataURL(blob);
      reader.onloadend = () => {
        const base64data = reader.result;
        resolve(base64data);
      };
    });
  };


  const NavigateSkill = () => {
    const path = ['AUTH_USER', 'SUPER_ADMIN', 'CONTENT_WRITER'].includes(userRole) ?
      `/app/skilliq-test?subModuleId=${submoduleId}` :
      `/auth/skilliq-test?subModuleId=${submoduleId}`;
    navigate(path, { state: { ...location.state, subModuleName } });
  }


  // const getScreens = async (submoduleTopicId, actionType) => {   
  //   simulationApi
  //     .getScreens(submoduleTopicId, actionType)
  //     .then(async (res) => {

  //       try {
  //         if (res.ok) {
  //           const sortedData = res.data;           
  //           await Promise.all(
  //             sortedData.map(async (item, index) => {
  //               if (item.type === 'SUCCESS' || item.type === 'INTRO') {
  //                 const base64Img = await getBase64FromUrl(item.backgroundImg);

  //                 sortedData[index] = {
  //                   ...item,
  //                   backgroundBase64: base64Img,
  //                 };
  //               }  

  //             })
  //           );

  //           if(topics[1]!== undefined){
  //             sortedData.push(topics[1])
  //           }
  //           setScreens(sortedData);                       
  //         }
  //       } catch (error) {
  //         console.log('error after data received from getScreens api', error);

  //       }
  //     })
  //     .catch((error) => {
  //       console.log('error getting screens from api ', error);

  //     });
  // };

  const renderIcon = (item) => {
    if (item.type === 'VIDEO') {
      return (
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            width: 24,
            height: 24,
            borderRadius: '50%',
            backgroundColor: theme.palette.common.white,
            border: `1px solid ${theme.palette.warning.main}`,
            color: theme.palette.warning.main,
            fontSize: '0.75rem',
          }}
        >
          <Typography variant="caption" component="span" sx={{ color: theme.palette.warning.main }}>
            {'V'}
          </Typography>
        </Box>
      )
    }
    if (item === "SkillSet IQ") {
      return (
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            width: 24,
            height: 24,
            borderRadius: '50%',
            backgroundColor: theme.palette.common.white,
            border: `1px solid ${theme.palette.warning.main}`,
            color: theme.palette.warning.main,
            fontSize: '0.75rem',
          }}
        >
          <Typography variant="caption" component="span" sx={{ color: theme.palette.warning.main }}>
            {'Q'}
          </Typography>
        </Box>

      );
    }
    if (item.actionType?.code === 'REFERENCE') {
      return (
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            width: 24,
            height: 24,
            borderRadius: '50%',
            backgroundColor: theme.palette.common.white,
            border: `1px solid ${theme.palette.warning.main}`,
            color: theme.palette.warning.main,
            fontSize: '0.75rem',
          }}
        >
          <Typography variant="caption" component="span" sx={{ color: theme.palette.warning.main }}>
            {'R'}
          </Typography>
        </Box>
      )
    }
    if (item.actionType?.code !== 'VIDEO' && item.actionType?.code !== 'REFERENCE' && item !== "SkillSet IQ") {

      return (
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            width: 24,
            height: 24,
            borderRadius: '50%',
            backgroundColor: theme.palette.common.white,
            border: `1px solid ${theme.palette.warning.main}`,
            color: theme.palette.warning.main,
            fontSize: '0.75rem',
          }}
        >
          <Typography variant="caption" component="span" sx={{ color: theme.palette.warning.main }}>
            {'S'}
          </Typography>
        </Box>
      )

    }

  };




  return (
    <>

      {isLoading ? (
        <div
          style={{
            position: 'absolute',
            width: '100%',
            height: `calc(100vh - 70px)`,
            backgroundColor: 'rgba(255, 255, 255, 0.8)',
            display: 'flex',
            flexFlow: 'column',
            justifyContent: 'center',
            alignItems: 'center',
            zIndex: 999,
          }}
        >

          <LottieLoading loading={isLoading} />
        </div>
      ) : (
        <div>
          <List
            sx={{
              width: '100%',
              marginTop: 0,
              bgcolor: 'background.paper',
              paddingTop: '0',
              maxHeight: 'calc(100vh - 322px)',
              overflow: 'auto',
              overflowX: 'hidden',
              scrollbarWidth: 'thin',
              scrollSnapType: 'y proximity',
              scrollBehavior: 'smooth',
            }}
            component="nav"
            aria-labelledby="nested-list-subheader"
          >
            {topics && topics?.length > 0 && topics.map((item, index) => {
              // const isSelected =  selectedIndex === index;
              const isSelected = selectedIndex === item.id;
              const { isCompleted } = item;
              return (
                <ListItemButton
                  className={classes.listItem}
                  selected={selectedIndex === 'REFERENCE' ? item.id === 'reference' : isSelected}
                  onClick={status ? (event) => handleListItemClick(event, index, item) : undefined}
                  key={`topic-${index}`}
                  sx={{
                    color: isCompleted ? theme.palette.secondary.main : theme.palette.grey[500],
                    scrollSnapAlign: isSelected ? 'start' : 'none',
                    scrollMargin: isSelected ? '100px' : '0px',
                    '&.Mui-selected': {
                      color: "#111",
                      fontWeight: '600',
                      backgroundColor: "#a8e0c5"
                    },
                    '&.Mui-selected:hover': {
                      color: "#FE7000",
                      backgroundColor: "#a8e0c5"
                    },
                    '&:hover': {
                      backgroundColor: "#00B673"
                    },
                  }}
                >
                  {/* <ListItemIcon>
                    {renderIcon(item)}
                  </ListItemIcon> */}
                  {/* { primary={item.id !=='reference' && (

                  )} */}
                  <ListItemText id="SkillSetIQForVideos"
                    //  selected={selectedIndex === 'REFERENCE' && item.id==='reference'}
                    primary={item.id === 'reference' ? 'Reference' : item.courseSubmoduleTopics}
                    sx={{
                      // color: isSelected ? theme.palette.primary.main : 'inherit',
                      color: 'rgb(17, 17, 17)',
                      lineHeight: '1.2',
                      fontSize: '14px !important',
                    }}
                  />
                  <ListItemText sx={{ flex: '0 0 auto' }}
                    primary={
                      <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'end' }}>

{item?.id !== "reference" && item?.id !== "skillset" &&  userRole !== 'SUPER_ADMIN' &&  userRole !== 'CONTENT_WRITER' && renderProgressLabel(item)}
</div>
                    }
                    primaryTypographyProps={{
                      style: {
                        color: '#111',
                        fontSize: '14px',
                        lineHeight: '1.2'
                      },
                    }}
                  />

                  {/* <ListItemText id="SkillSetIQForVideos"
                    //  selected={selectedIndex === 'REFERENCE' && item.id==='reference'}
                    primary={item.id==='reference'? 'Reference' : item.courseSubmoduleTopics}
                    sx={{
                      // color: isSelected ? theme.palette.primary.main : 'inherit',
                      color: 'rgb(17, 17, 17)',
                      lineHeight: '1.2',
                      fontSize: '14px !important',
                    }}
                  /> */}
                </ListItemButton>
              );
            })}

            {/* <ListItemButton
              className={classes.listItem}
              onClick ={status?() => NavigateSkill():undefined}
              sx={{
            
                scrollSnapAlign:'start' ,
                scrollMargin:'100px',
                fontSize: '14px',
                '&.Mui-selected': {
                 color: "#111",
              fontWeight: '600',
              backgroundColor: "#a8e0c5"  
                },
                '&.Mui-selected:hover': {
                       color: "#FE7000",        
              backgroundColor: "#a8e0c5"    
                },
                '&:hover': {
                  // backgroundColor: theme.palette.secondary.lighter,
                     backgroundColor: "#00B673" , 
                },
              }}
            >        
                <ListItemText id="SkillSetIQ" sx={{paddingLeft: '16px', color: '#111', fontSize: '14px !important'}} primary={'SkillSet IQ'} />
            </ListItemButton> */}
          </List>


        </div>
      )}
    </>
  );
};

export default VideoTopicList;