/* eslint-disable no-unused-vars */
/* eslint-disable consistent-return */
/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable react/prop-types */
import React, { useRef, useState, useEffect, useLayoutEffect } from 'react';
import PropTypes from 'prop-types';

const VideoPlayer = ({ videoUrl, onClickCallBack, videoLoadCallback, activeStep }) => {
  const playerRef = useRef(null);
  const [showError, setShowError] = useState(false);
  const [isMuted, setIsMuted] = useState(false);
  const [isVideoLoaded, setIsVideoLoaded] = useState(false);
  const [activeScreen, setActiveScreen] = useState('');
  const currentVideoRef = useRef(null);

  useEffect(() => {
    setActiveScreen(activeStep?.id || '');
  }, [videoUrl]);


  console.log(videoUrl,"videoUrl inside");
  


  
  useEffect(() => {
    const handleNoVideoUrl = () => {
      const videoElement = playerRef.current?.querySelector('video');
      if (videoElement) {
        videoElement.pause();
        videoElement.muted = true;
      }
    };

    if (!videoUrl) {
      handleNoVideoUrl();
      return;
    }

    const loadGriffithPlayer = () => {
      const { Griffith } = window;

      if (!Griffith) {
        console.error('Griffith library could not be loaded');
        setShowError(true);
        videoLoadCallback(false);
        localStorage.setItem("play", false);
        return;
      }

      const target = playerRef.current;
      if (!target) {
        console.error('Player target element not found');
        setShowError(true);
        videoLoadCallback(false);
        localStorage.setItem("play", false);
        return;
      }

      try {
        const sources = {
          hd: { play_url: videoUrl },
        };

        // Delay render 
        setTimeout(() => {
          const newPlayer = Griffith.createPlayer(target);
          newPlayer.render({
            sources,
            autoplay: true,
          });

          const videoElement = target.querySelector('video');
          if (videoElement) {
            videoElement.muted = isMuted;

            videoElement.addEventListener('canplay', () => {
              setIsVideoLoaded(true);
              videoLoadCallback(true);
              localStorage.setItem("play", true);
              currentVideoRef.current = videoUrl;
            });

            videoElement.addEventListener('play', () => {
              if (!isMuted) videoElement.muted = false;
            });

            videoElement.addEventListener('pause', () => {
              console.log("555555555")
              videoElement.muted = true;
            });

            videoElement.addEventListener('ended', () => {
              if (currentVideoRef.current === videoUrl) {
                onClickCallBack(videoUrl);
              }
            });

            videoElement.addEventListener('error', () => {
              console.error('Error while loading video');
              setShowError(true);
              videoLoadCallback(false);
              localStorage.setItem("play", false);
            });
          }
        }, 0);
      } catch (error) {
        console.error('Error initializing Griffith player:', error);
        setShowError(true);
        videoLoadCallback(false);
        localStorage.setItem("play", false);
      }
    };

    if (!window.Griffith) {
      const script = document.createElement('script');
      script.src = `https://unpkg.com/griffith-standalone/dist/index.umd.min.js?${Date.now()}`;
      script.async = true;
      script.onload = loadGriffithPlayer;
      script.onerror = (e) => {
        console.error('Script loading error:', e);
        setShowError(true);
        videoLoadCallback(false);
        localStorage.setItem("play", false);
      };
      document.body.appendChild(script);
    } else {
      loadGriffithPlayer();
    }

    return () => {
      const script = document.querySelector(`script[src*="griffith-standalone"]`);
      if (script && script.parentNode) {
        script.parentNode.removeChild(script);
      }
    };
  }, [videoUrl, isMuted]);

  useLayoutEffect(() => {
    const videoElement = playerRef.current?.querySelector('video');
    if (videoElement) {
      videoElement.currentTime = 0;
    }
  }, [videoUrl]);

  useEffect(() => {
    if (!isVideoLoaded) {
      videoLoadCallback(false);
      localStorage.setItem("play", false);
    }
  }, [isVideoLoaded]);

  useEffect(() => {
    const handleKeyDown = (event) => {
      if (!isVideoLoaded && event.key === 'Enter') {
        event.preventDefault();
        console.warn('Enter key pressed, but video is still loading.');
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [isVideoLoaded]);

  return (
    <div style={{ position: 'relative', width: '100%', height: '75%' }}>
      {showError ? (
        <div style={{ position: 'absolute', paddingTop: '10%', paddingLeft: '10%', textAlign: 'center' }}>
          <div>Oops! Something went wrong while trying to play the video. Please try again later or switch browser.</div>
        </div>
      ) : (
        <div ref={playerRef} style={{ position: 'relative', width: '100%', height: '500px' }} />
      )}
    </div>
  );
};

VideoPlayer.propTypes = {
  videoUrl: PropTypes.string.isRequired,
  onClickCallBack: PropTypes.func.isRequired,
  videoLoadCallback: PropTypes.func.isRequired,
  activeStep: PropTypes.object,
};

export default VideoPlayer;
