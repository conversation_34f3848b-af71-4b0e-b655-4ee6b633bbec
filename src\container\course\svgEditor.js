/* eslint-disable react/no-danger */
import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';

const SvgEditor = ({ presignedUrl, placeholder, replacement }) => {
  const [svgContent, setSvgContent] = useState(null);

  async function fetchSVG(url) {
    const response = await fetch(url);
    if (!response.ok) {
      throw new Error('Failed to fetch SVG');
    }
    const svgText = await response.text();
    console.log('svgText...',svgText);
    return svgText;
  }

  function replacePlaceholder(svgText, placeholder, replacement) {
    const parser = new DOMParser();
    const xmlDoc = parser.parseFromString(svgText, 'image/svg+xml');
    const placeholderElement = xmlDoc.querySelector(`[data-placeholder="${placeholder}"]`);
    
    if (placeholderElement) {
      placeholderElement.textContent = replacement;
    }
    
    const serializer = new XMLSerializer();
    return serializer.serializeToString(xmlDoc);
  }

  useEffect(() => {
    async function getAndEditSVG() {
      try {
        const svgText = await fetchSVG(presignedUrl);
        const updatedSvg = replacePlaceholder(svgText, placeholder, replacement);
        setSvgContent(updatedSvg);
      } catch (error) {
        console.error('Error fetching or editing SVG:', error);
      }
    }

    getAndEditSVG();
  }, [presignedUrl, placeholder, replacement]);

  return (
    <div dangerouslySetInnerHTML={{ __html: svgContent }} />
  );
};

SvgEditor.propTypes = {
  presignedUrl: PropTypes.string.isRequired,
  placeholder: PropTypes.string.isRequired,
  replacement: PropTypes.string.isRequired,
};

export default SvgEditor;
