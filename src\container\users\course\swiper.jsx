import React, { useEffect, useState } from 'react';
import { makeStyles } from '@mui/styles';
import LinearProgress from '@mui/material/LinearProgress';
import { Swiper, SwiperSlide } from 'swiper/react';
import 'swiper/swiper-bundle.min.css';
import { Box, Grid, Typography, Container, Button, IconButton } from '@mui/material';
import SwiperCore, { Navigation, Pagination, Mousewheel, Keyboard } from 'swiper';
import UserCard from '../../../components/cards/UserCard';

SwiperCore.use([Navigation, Pagination, Mousewheel, Keyboard]);

const useStyles = makeStyles((theme) => ({
  swiperPagination: {
    position: 'relative',
    display: 'flex',
    justifyContent: 'center',
    marginTop: '10px',
  },
  '@global': {
    '.custom-bullet': {
      width: '8px',
      height: '8px',
      background: '#000',
      margin: '0 5px',
      borderRadius: '50%',
      opacity: 0.5,
      cursor: 'pointer',
    },
    '.custom-bullet-active': {
      opacity: 1,
      background: '#789df5',
    },
    '.swiper-button-next, .swiper-button-prev': {
      width: '25px',
      height: '25px',
      backgroundColor: '#789df5',
      borderRadius: '100%',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      cursor: 'pointer',
      margin: '0',
      '&::after': {
        fontSize: '12px',
        color: '#fff',
      },
    },
    '.swiper-button-next': {
      right: '0px',
    },
    '.swiper-button-prev': {
      left: '0px',
    },
  },
}));

const SwiperComponent = ({ res, handleCardClickItem }) => {
  const classes = useStyles();
  const [view, setView] = useState([]);
  const [courseList, setCourseList] = useState([]);
  const [loading, setLoading] = useState(true);
  const [courseGroups, setCourseGroup] = useState([]);

  useEffect(() => {
    if (res?.length > 0) {
      const sortedRes = [...res].sort((a, b) => {
        if (a.isSubscribed && !b.isSubscribed) {
          return -1;
        }
        if (!a.isSubscribed && b.isSubscribed) {
          return 1;
        }
        return 0;
      });
      setView(sortedRes);
    }
  }, [res]);


  function LinearProgressWithLabel({ value }) {
    return (
      <Box>
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            marginTop: '16px !important',
          }}
          mb={0.5}
        >
          <Box sx={{ width: '100%', mr: 1 }}>
            <LinearProgress variant="determinate" value={value} />
          </Box>
          <Box sx={{ width: 'max-content' }}>
            <Typography
              variant="body2"
              color="gray"
              sx={{ fontSize: '0.7rem' }}
            >
              {value}%
            </Typography>
          </Box>
        </Box>
        <Typography
          variant="body2"
          color={'primary'}
          sx={{ fontSize: '0.7rem', marginTop: '-8px' }}
        >
          Completed
        </Typography>
      </Box>
    );
  }

  return (
    <Grid style={{ backgroundColor: '#f1f1f1', height: '500px', padding: '16px 0 0 0' }}>
    <Typography
      style={{ marginLeft: 20, marginBottom: -5 }}
      variant="h5"
      gutterBottom
    >
      {t("Start from where you left")}
    </Typography>
    <Swiper
      spaceBetween={0}
      slidesPerView={3}
      breakpoints={{
        1000: {
          slidesPerView: 3,
        },
        600: {
          slidesPerView: 2,
        },
        0: {
          slidesPerView: 1,
        },
      }}
      navigation={view?.length > 3}
      pagination={{
        el: view.length > 3 ? `.${classes.swiperPagination}` : "",
        clickable: view.length > 3,
        bulletClass: 'custom-bullet',
        bulletActiveClass: 'custom-bullet-active'
      }}
      mousewheel
      keyboard
      className="mySwiper"
    >
      {view.map((item, index) => (
        <SwiperSlide key={index} style={{ padding: '8px' }}> 
          <UserCard
            handleCardClick={() => handleCardClickItem(item)}
            image={item.thumbImage}
            planStatus={item.planStatus}
            title={item.title}
            trial={item.isTrial}
            subscribed={item.isSubscribed}
            category={item.category}
            logo={item.category.categoryImgUrl}
            enrollDate={item.validFrom}
            progress={<LinearProgressWithLabel value={item.completed} />}
            data={item}
          />
        </SwiperSlide>
      ))}
    </Swiper>
    <div className={classes.swiperPagination} />
  </Grid>
  );
};

export default SwiperComponent;