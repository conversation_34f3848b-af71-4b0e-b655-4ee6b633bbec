/* eslint-disable no-unused-vars */
/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable arrow-body-style */
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Grid, Typography } from '@mui/material';
import SatCard from '../../../components/cards/Satcard'
import CourseApi from '../../../services/users/courseApi'

const SATView = () => {
  const [satDetails, setSatDetails] = useState([]);
  const { t } = useTranslation('translation');

  React.useEffect(async () => {
    const result = await CourseApi.getSATList()
    setSatDetails(result.data)
  }, [])



  return (
    <>
     <Typography sx={{marginTop: '85px', marginBottom: "15px", marginLeft: {md: '35px', sm: '18px', xs: '10px'} }} variant="h4" gutterBottom>
          {/* Don't translate this title since we want it in English */}
          SAT Full Test
        </Typography>
      <Grid
        container
        spacing={2}
    //     style={{ backgroundColor: '#F8F9FA', padding: '0 20px !important', marginTop: '10px', maxWidth: '99%', width: '98%', margin: "auto",   display: 'flex',
    // flexWrap: 'wrap',
    // justifyContent: 'flex-start' }} 

      sx={{ 
    backgroundColor: '#F8F9FA', 
    padding: '20px !important', 
    marginTop: '10px', 
    width: '100%', 
    margin: "auto",
    display: 'flex',
    flexWrap: 'wrap',
    justifyContent: 'flex-start'
  }}
    
    
    >

        {satDetails && satDetails?.length > 0 && satDetails.map((item, index) => (

         <Grid item xs={12} sm={6} md={4} lg={3} xl={2} key={index}>
            <SatCard style={{marginRight: '0px', justifyContent: 'center'}}
              comingFrom={'B2B'}
              data={item}
            />          
          </Grid>
                
        ))}
      </Grid>

    </>
  )

}

export default SATView;