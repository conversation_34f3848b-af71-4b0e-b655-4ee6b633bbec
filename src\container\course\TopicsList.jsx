/* eslint-disable prefer-template */
/* eslint-disable no-unneeded-ternary */
/* eslint-disable no-unused-vars */
/* eslint-disable consistent-return */
/* eslint-disable react/prop-types */
import React, { useEffect } from 'react';
import { List, Typography, useTheme, ListItemButton, ListItemIcon, ListItemText, Box } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { NavLink, useNavigate } from 'react-router-dom';
import { makeStyles } from '@mui/styles';
import { useSelector } from 'react-redux';
import './scrollStyle.css';
import adminServices from '../../services/adminServices';

// eslint-disable-next-line no-unused-vars
const LinkBtn = React.forwardRef((props, ref) => <NavLink to={props.to} {...props} innerRef={ref} />);

const useStyles = makeStyles(() => ({
  listItem: {
    borderBottom: '1px solid #BCBCBC',
    padding: 12,
    "&:hover": {
      color: "#fff"
    }
  },
  skillIqTest: {
    padding: '4px 14px',
    borderRadius: '6px',
    margin: 'auto',
    border: '1px solid #FE7000',
    width: 'max-content',
    marginTop: '1rem',
    cursor: 'pointer',
  },

}));

const TopicList = ({ data, onClickCallBack, onReferenceButtonClick, onVideoClick, selectedIndex, submoduleId, location, subModuleName, status, screens, CallBackSkillSetIq, Modid }) => {
  const classes = useStyles();
  const theme = useTheme();
  const navigate = useNavigate();
  const userRole = useSelector((state) => state.userInfo?.role);
  const details = useSelector((state) => state.userInfo)

  const { t } = useTranslation('translation');
  const [selectedItemId, setSelectedItemId] = React.useState(data[0]?.id);
  const [authors, setAuthors] = React.useState([]);

  useEffect(() => {
    getAuthors()
  }, [])

 

  
  const getAuthors = async () => {
    try {
      const response = await adminServices.getAuthors();

      if (response?.ok && Array.isArray(response.data)) {
        const authArray = response.data.map((a) => a.name);
        setAuthors(authArray);
        return true;
      }
    } catch (error) {
      console.error('Error fetching authors:', error);
    }
  };

  React.useMemo(() => {
    setSelectedItemId(selectedIndex)
  }, [selectedIndex])



  const renderProgressLabel = (item, index) => {
    let label = '';
    let background = '';
    let color = '';
    if (item?.isCompleted === true) {
      label = 'Completed';
      background = '#a8e0c5';
      color = '#408655';
    } else if (item?.isCompleted === false && item?.screenProgress === "inprogress" && item?.id === selectedItemId) {
      label = 'In Progress';
      background = '#FFE0B2';
      color = '#E65100';
      
    } else if (item?.isCompleted === false && item?.screenProgress === "not_started" && item?.id === selectedItemId) {
      label = 'In Progress';
      background = '#FFE0B2';
      color = '#E65100';
    }

    if (!label) return null;

    return (
      <span style={{
        background,
        color,
        padding: '2px 8px',
        marginLeft: '8px',
        borderRadius: '12px',
        fontSize: '10px',
        fontWeight: '500',
        minWidth: '75px',
        border: '1px solid',
      }}>
        {label}
      </span>
    );
       
  };


  // const getSerialNumber = (item, index) => {
  //   if (item.actionType?.code === 'REFERENCE') {
  //     return 2; 
  //   } 
  //     return index + 2;
  // };


  // const getSkillIQSerialNumber = () => {
  //   return hasReference ? 3 : 2; 
  // };

  const handleListItemClick = (event, index, item) => {
    const menuCheckbox = document.getElementById("menuCheckbox");
    if (menuCheckbox) {
      menuCheckbox.checked = !menuCheckbox.checked;
    }
    if (item.actionType?.code === 'VIDEO') {
      setSelectedItemId(item?.id);
      onVideoClick(item);
    } else if (item?.id === 'reference') {
      setSelectedItemId('reference');
      onReferenceButtonClick(submoduleId, subModuleName);
    } else if (item?.id === "skillset") {
      setSelectedItemId('skillset')

      const keyType = { actionType: data[0]?.actionType?.code === 'CODE' ? 'CODE' : 'HOTKEYS' };
      if (userRole === 'SUPER_ADMIN' || (authors)?.includes(details?.firstName + ' '+ details?.lastName)) {
        const path = ['AUTH_USER', 'SUPER_ADMIN', 'CONTENT_WRITER'].includes(userRole) ?
          `/app/skilliq-test?subModuleId=${submoduleId}` :
          `/auth/skilliq-test?subModuleId=${submoduleId}`;
        navigate(path, { state: { ...location.state, ...keyType, subModuleName } });
      }
      else {
        const val = { ...location.state, ...keyType, subModuleName, Modid }
        CallBackSkillSetIq(val)
      }


    }
    else {
      if (event.key === 'Enter') {
        console.log('Inside Enter...')
        return
      }
      if (event.key === ' ') {
        console.log('Inside space...')
        return
      }
      setSelectedItemId(item?.id);
      onClickCallBack(index, event);
    }
  };


  const renderIcon = (item) => {
    // return <VideoLibraryIcon sx={{ color: theme.palette.primary.main }} />;
    if (item === "SkillSet IQ") {
      return (
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            width: 24,
            height: 24,
            borderRadius: '50%',
            backgroundColor: theme.palette.common.white,
            border: `1px solid ${theme.palette.warning.main}`,
            color: theme.palette.warning.main,
            fontSize: '0.75rem',
          }}
        >
          <Typography variant="caption" component="span" sx={{ color: theme.palette.warning.main }}>
            {'Q'}
          </Typography>
        </Box>

      );
    }
    if (item !== "SkillSet IQ") {

      return <Box
        sx={{
          display: 'none',
          alignItems: 'center',
          justifyContent: 'center',
          width: 24,
          height: 24,
          borderRadius: '50%',
          backgroundColor: theme.palette.common.white,
          border: `1px solid ${theme.palette.warning.main}`,
          color: theme.palette.warning.main,
          fontSize: '0.75rem',
        }}
      >
        <Typography variant="caption" component="span" sx={{ color: theme.palette.warning.main }}>

          {item?.type ? item.type.charAt(0).toUpperCase() : "S"}

        </Typography>
      </Box>

    }
  };



  // SkillIq

  return (
    <>
      <List
        sx={{
          width: '100%',
          marginTop: 0,
          bgcolor: 'background.paper',
          paddingTop: '0',
          maxHeight: 'calc(100vh - 322px)',
          overflow: 'auto',
          overflowX: 'hidden',
          scrollbarWidth: 'thin',
          scrollSnapType: 'y proximity',
          scrollBehavior: 'smooth',
        }}
        component="nav"
        aria-labelledby="nested-list-subheader"
      >
        {data && data?.length > 0 && data.map((item, index) => {
          // const isSelected = selectedIndex === item.id;
          const isSelected = selectedItemId === item?.id;


          const { isCompleted } = item;

          const isReference = item.actionType?.code === 'REFERENCE';
          const isVideo = item.actionType?.code === 'VIDEO';

          // const serialNumber = getSerialNumber(item, index);
          const status = true;
          return (
            <ListItemButton
              className={classes.listItem}
              // selected={isSelected}
              selected={isSelected}

              onClick={status ? (event) => handleListItemClick(event, index, item) : undefined}
              key={`topic-${index}`}
              sx={{
                color: isCompleted ? theme.palette.secondary.main : theme.palette.grey[500],
                scrollSnapAlign: isSelected ? 'start' : 'none',
                scrollMargin: isSelected ? '100px' : '0px',
                '&.Mui-selected': {
                  color: "#111",
                  fontWeight: '600',
                  backgroundColor: "#a8e0c5"
                },
                '&.Mui-selected:hover': {
                  color: "#FE7000",
                  // backgroundColor: theme.palette.secondary.lighter,     
                  backgroundColor: "#a8e0c5"
                },
                '&:hover': {
                  // backgroundColor: theme.palette.secondary.lighter,
                  backgroundColor: "#00B673"
                },
              }}
            >
              <ListItemIcon>

                {/* {(isReference || isVideo) ? (
                  <Box
                    sx={{
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      marginRight: '5px',
                      marginLeft: '8px',
                      color: '#111',

                    }}
                  >
                    <Typography variant="caption" component="span">
                      {serialNumber}.
                    </Typography>
                  </Box>
                ) : renderIcon(item)} */}
              </ListItemIcon>

              <ListItemText
                primary={
                  <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                    <span>{item.courseSubmoduleTopics}</span>
                    {item?.id !== "reference" && item?.id !== "skillset" && userRole !== 'SUPER_ADMIN' && userRole !== 'CONTENT_WRITER' && renderProgressLabel(item, index)}

                  </div>
                }
                primaryTypographyProps={{
                  style: {
                    color: '#111',
                    fontSize: '14px',
                    lineHeight: '1.2'
                  },
                }}
              />
            </ListItemButton>
          );
        })}



        {/* SkillSet IQ Item */}
        {/* <ListItemButton
          className={classes.listItem}
          onClick={status ? () => handleNavigateSkill() : undefined}
          sx={{
            marginTop: data && data.some(item => item.actionType?.code === 'REFERENCE') ? '0' : '8px',
            borderTop: data && data.some(item => item.actionType?.code === 'REFERENCE') ? 'none' : '1px solid #e0e0e0',
            color: theme.palette.grey[500],
            scrollSnapAlign: 'start',
            scrollMargin: '100px',
            '&.Mui-selected': {
              color: "#111",
              fontWeight: '600',
              backgroundColor: "#a8e0c5"   
            },
            '&.Mui-selected:hover': {       
              color: "#FE7000",        
              backgroundColor: "#a8e0c5"        
            },
            '&:hover': {
              backgroundColor: "#00B673"   
            },
          }}
        >
         
          <ListItemText id="SkillSetIQ" sx={{paddingLeft: '16px', color: '#111', fontSize: '14px !important'}} primary={t('SkillSet IQ')} />
        </ListItemButton> */}
      </List>



      {/* <Typography
        className={classes.skillIqTest}
        variant="subtitle1"
        onClick={() => {
          const keyType = { actionType: data[0]?.actionType?.code === 'CODE' ? 'CODE' : 'HOTKEYS' };
          const path = ['AUTH_USER', 'SUPER_ADMIN', 'CONTENT_WRITER'].includes(userRole) ?
            `/app/skilliq-test?subModuleId=${submoduleId}` :
            `/auth/skilliq-test?subModuleId=${submoduleId}`;

          navigate(path, { state: { ...location.state, ...keyType } });
        }}
        sx={{ textDecoration: 'none !important' }}
        color="primary"
      >
        SkillSet IQ
      </Typography> */}
    </>
  );
};

export default TopicList;
