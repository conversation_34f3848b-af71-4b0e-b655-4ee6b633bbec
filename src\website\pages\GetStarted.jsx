/* eslint-disable react/jsx-fragments */
/* eslint-disable no-unused-vars */
/* eslint-disable react-hooks/exhaustive-deps */

import React, { useState, useEffect, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Grid,
  Typography,
  Box, Button,
  FormControlLabel,
  TextField,
  Container,
  Checkbox,
  // Divider,
} from '@mui/material';
import { makeStyles } from '@mui/styles';
import VisibilityIcon from '@mui/icons-material/Visibility';
import VisibilityOffIcon from '@mui/icons-material/VisibilityOff';
import InputAdornment from '@material-ui/core/InputAdornment';
import { Form, Formik } from 'formik';
import * as Yup from 'yup';
import AppleIcon from '@mui/icons-material/Apple';
import { GoogleLogin } from "react-google-login";
import { Link as RouterLink, useNavigate, useLocation, useSearchParams } from 'react-router-dom';
import { LoadingButton } from '@mui/lab';
import { useDispatch } from 'react-redux';
import Cookies from 'js-cookie';
import jwtdecode from 'jwt-decode';
import ApartmentIcon from '@mui/icons-material/Apartment';
import { Carousel } from 'react-responsive-carousel';
import LanguageSwitcher from './LanguageSwitcher';
import "react-responsive-carousel/lib/styles/carousel.min.css";
import ErrorFocus from '../../components/ErrorFocus/ErrorFocus';
import SnackBar from '../../components/snackbar/snackbar';
import loginServices from '../../services/loginServices';
import apiClient from '../../services/apiClient';
import BackgroundImg from '../../assets/logo/images/bg.jpg';
import BackgroundImges1 from '../../assets/logo/images/backgroundImage1.png';
import BackgroundImges2 from '../../assets/logo/images/backgroundImage2.png';
import BackgroundImges3 from '../../assets/logo/images/backgroundImage3.png';
import BackgroundImges4 from '../../assets/logo/images/backgroundImage4.png';
import BackgroundImges5 from '../../assets/logo/images/backgroundImage5.png';
import LoginImage from '../../assets/logo/LoginImg.png'
import Register from "./Signup"
import './style.css'
import Logo from '../../assets/logo/logo.png';
import PhoneNumber from '../../components/PhoneNumber/Index';


export const GetStarted = () => {
  const classes = useStyles();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const location = useLocation();
  const { t } = useTranslation('translation');
  const [loading, setLoading] = useState(false);
  const [visible, setVisible] = React.useState(false);
  const [snackbar, setSnackbar] = useState(false);
  const [snackbarTitle, setSnackbarTitle] = useState('');
  const [searchParams] = useSearchParams();
  const [initialcomponent, setInitialComponent] = useState(true);
  const [userDetails, setUserDetails] = useState('');
  const [carouselHeight, setCarouselHeight] = useState('100%');
  const carouselRef = useRef(null);

  const [formValues] = React.useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    password: '',
    agreeToTerms: false
    // confirmPassword: '',
  });

  const [passwordError, setPasswordError] = useState('');
  console.log(passwordError, "passwordError");


  const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*()_+={}[\]:;"'<>,.?/-]).{8,}$/;
  const validatePassword = (password) => {
    if (!passwordRegex.test(password)) {
      return (
        <p>{"Password should contain a minimum of 8 characters."}</p>
        // <>
        //   <p>{t("Password should contain a minimum of 8 characters.")}</p>
        //   <p>{t("ONE uppercase letter.")}</p>
        //   <p>{t("ONE lowercase letter.")}</p>
        //   <p>{t("ONE number.")}</p>
        //   <p>{t("ONE special character from @#$%^_-.")}</p>

        // </>
      );

    }
    return '';
  };

  useEffect(() => {
    validatePassword(formValues.password)
    setPasswordError('')
  }, [t]);

  const websiteUserLogin = async (values) => {
    console.log(values);
    setLoading(true);

    const resetData = {
      firstName: values.firstName,
      lastName: values.lastName,
      email: values.email.toLowerCase(),
      phone: values.phone,
      password: values.password,
      // confirmPassword: values.password,
    };

    try {
      const response = await loginServices.postUserLogin(resetData);

      if (response.ok) {
        SendEmailVerification(values.firstName,values.email)
        navigate('/login');
        // CreateButton(values)
        setUserDetails(response.data.userId)
        apiClient.setHeader('Authorization', `BEARER ${response.data.token}`);
        Cookies.set('token', response.data.token);
        Cookies.set('refreshToken', response.data.refreshToken);
        Cookies.set('isLogIn', true);
        const decoded = await jwtdecode(response.data.token);

        Cookies.set('tokenExpireTime', decoded.exp);
        // await getLoginUserDetails();     
        setSnackbarTitle(t("Account Created!"));
        setSnackbar(true);
      }
      else {
        console.log('response...', response.data.message)
        setSnackbarTitle(response.data.message);
        setSnackbar(true);
      }

      setLoading(false);

    } catch (error) {
      console.log(error);
    }
  };

  const SendEmailVerification = async (firstname,email) => {
    const data = {
      // eslint-disable-next-line object-shorthand
      email:email,
      firstName:firstname,
  }
    try {
      const res = await loginServices.sendEmail(data);
      if (res.ok) {
        console.log("sent..")
        setSnackbarTitle("You have not verified your email ID. Please verify your email with the verification link sent to your email.");
        setSnackbar(true);
      }

    } catch (error) {
      console.log(error);
    }
  }


  const handleClickShowPassword = () => {
    setVisible(!visible);
  };

  // const images = [BackgroundImges1, BackgroundImges2, BackgroundImges3, BackgroundImges4, BackgroundImges5];
  const images = [LoginImage];

  // eslint-disable-next-line no-unused-vars
  const [index, setIndex] = useState(0);
  const [details, setDetails] = useState("");

  const [validationSnackbar, setValidationSnackbar] = useState(false);
  const [validationMessages, setValidationMessages] = useState([]);

  const handleValidationErrors = (errors) => {
    const errorMessages = [];

    // Collect all validation error messages
    Object.keys(errors).forEach(field => {
      if (errors[field]) {
        let fieldName = field;

        // Format field names for better readability
        switch (field) {
          case 'firstName':
            fieldName = 'First Name';
            break;
          case 'lastName':
            fieldName = 'Last Name';
            break;
          case 'email':
            fieldName = 'Email';
            break;
          case 'password':
            fieldName = 'Password';
            break;
          case 'phone':
            fieldName = 'Phone Number';
            break;
          case 'agreeToTerms':
            fieldName = 'Terms & Conditions';
            break;
          default:
            fieldName = field.charAt(0).toUpperCase() + field.slice(1);
        }

        errorMessages.push(`${fieldName}: ${t(errors[field])}`);
      }
    });

    if (errorMessages.length > 0) {
      setValidationMessages(errorMessages);
      setValidationSnackbar(true);
    }
  };

  const handleCloseValidationSnackbar = () => {
    setValidationSnackbar(false);
  };

  useEffect(() => {
    const interval = setInterval(() => {
      setIndex((prevIndex) => (prevIndex === images.length - 1 ? 0 : prevIndex + 1));
    }, 2000);
    return () => clearInterval(interval);
  }, []);


  const CreateButton = (data) => {
    console.log(data, "data");
    setInitialComponent(false)
    setDetails(data)
  }


  const handleSuccess = async () => {
    try {

      // const res = await axios.post("http://localhost:5000/auth/google", { token: tokenId });

      // console.log(res,'res');

    } catch (error) {
      console.error("Google Login Error:", error);
    }
  };

  const handleFailure = (error) => {
    console.error("Google Login Failed:", error);
  };

  const handleOutLookLogin = () => {
    console.log('google');

  }

  useEffect(() => {
    const updateCarouselHeight = () => {
      const signupElement = document.getElementById('SignupIs');
      if (signupElement) {
        const signupHeight = signupElement.offsetHeight;
        setCarouselHeight(`${signupHeight}px`);
      }
    };


    updateCarouselHeight();

    window.addEventListener('resize', updateCarouselHeight);

    const observer = new MutationObserver(updateCarouselHeight);
    const signupElement = document.getElementById('SignupIs');
    if (signupElement) {
      observer.observe(signupElement, {
        attributes: true,
        childList: true,
        subtree: true
      });
    }

    return () => {
      window.removeEventListener('resize', updateCarouselHeight);
      observer.disconnect();
    };
  }, []);

  return (

    <>
      {initialcomponent === true ?
        <Box className={classes.mainContainer}>
          <Container maxWidth="lg" style={{
            height: '100vh',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center' 
          }}>
            <Grid container spacing={2} id="loginPanelIs" alignItems="stretch">
              <Grid item xs={12} md={4.5} id="SignupIs">
                <Formik
                  enableReinitialize
                  initialValues={formValues}
                  validationSchema={Yup.object().shape({
                    firstName: Yup.string()
                      .trim()
                      .matches(/^[A-Za-z ]+$/, 'Only alphabets are allowed for this field')
                      .required('first_name_required'),
                    // lastName: Yup.string()
                    //   .trim()
                    //   .matches(/^[A-Za-z ]+$/, 'Only alphabets are allowed for this field')
                    //   .required('last_name_required'),
                    password: Yup.string()
                      .trim()
                      .min(4, 'Password must be at least 4 characters long')
                      .required('Password is required'),
                    email: Yup.string().required('email_required').email('Email must be valid email'),
                    agreeToTerms: Yup.boolean().oneOf([true], 'Please accept the Terms and Conditions to create your keySkillset account.'),
                    phone: Yup.string().trim().nullable(),
                  })}
                  onSubmit={(values) => {
                    websiteUserLogin(values);
                  }}
                >
                  {({ errors, handleBlur, handleChange, touched, values }) => (
                    <Form>


                      <Grid container spacing={1}>
                        <Grid item xs="12">
                          <img src={Logo} alt="logo" width="145" style={{ margin: 'auto', height: '48px', width: '102px' }} />
                          <Typography color="#000000" className={classes.login} variant="h6" style={{ marginTop: "10px", marginBottom: "0px", textAlign: 'center' }}>
                            {/* {t('signUp')} */}
                            Create Your Account
                          </Typography>
                        </Grid>
                        <Grid item xs="12" style={{ marginTop: "-10px", textAlign: 'center' }}>
                          <Typography style={{
                            fontSize: "12px",
                            color: 'rgb(171, 171, 171)', fontWeight: 500
                          }} >
                            {/* {t('signUpHeader')} */}
                            A great pathway to Learning begins here!
                          </Typography>
                        </Grid>
                        <Grid item xs={12} sm={6}>
                          <Typography variant="subtitle2" style={{ fontSize: "12px", color: '#333', fontWeight: 500, marginLeft: '15px', paddingTop: "10px"}} color="textSecondary">
                            First Name <span style={{ color: 'red' }}>*</span>
                          </Typography>
                          <TextField
                            fullWidth
                            name="firstName"
                            placeholder={t('firstName')}
                            variant="outlined"
                            color="success"
                            onBlur={handleBlur}
                            onChange={handleChange}
                            type="text"
                            value={values.firstName}
                            error={Boolean(touched.firstName && errors.firstName)}
                            helperText={touched.firstName && errors.firstName ? t(errors.firstName) : ''}
                            inputProps={{ maxLength: 180 }}
                            sx={{
                              marginTop: "5px",
                              '& input::placeholder': {
                                    fontSize: '0.85rem',
                                },
                              '& .MuiOutlinedInput-root': {
                                borderRadius: '30px',
                                '& fieldset': {
                                  borderColor: 'transparent',

                                  boxShadow: 'rgba(99, 99, 99, 0.2) 0px 2px 8px 0px'
                                },
                                '& input': {
                                  padding: '8px 14px', // Adjust this to reduce height
                                  height: 'auto', // Optional, for better control
                                },
                              },
                            }}

                          />
                          <ErrorFocus />
                        </Grid>

                        <Grid item xs={12} sm={6}>
                          <Typography variant="subtitle1" style={{ fontSize: "12px", color: '#333', fontWeight: 500, marginLeft: '15px' , paddingTop: '10px'}} color="textSecondary">
                            Last Name
                          </Typography>
                          <TextField
                            fullWidth
                            id="outlined-basic"
                            name="lastName"
                            placeholder={t('lastName')}
                            color="success"
                            onBlur={handleBlur}
                            onChange={handleChange}
                            type="text"
                            value={values.lastName}
                            variant="outlined"
                            // error={Boolean(touched.lastName && errors.lastName)}
                            // helperText={touched.lastName && errors.lastName ? t(errors.lastName) : ''}

                            inputProps={{ maxLength: 180 }}
                            sx={{
                              marginTop: "5px",
                              borderRadius: '30px',
                              '& input::placeholder': {
                                    fontSize: '0.85rem',
                                },
                              '& .MuiOutlinedInput-root': {
                                borderRadius: '30px',
                                '& fieldset': {
                                  borderColor: 'transparent',
                                  boxShadow: 'rgba(99, 99, 99, 0.2) 0px 2px 8px 0px'
                                },
                                '& input': {
                                  padding: '8px 14px', // Adjust this to reduce height
                                  height: 'auto', // Optional, for better control
                                },
                              },
                            }}
                          />
                          <ErrorFocus />
                        </Grid>
                        <Grid item xs={12} sm={12}>
                          <Typography variant="subtitle1" style={{ fontSize: "12px", color: '#333', fontWeight: 500, marginLeft: '15px', paddingTop: "5px" }} color="textSecondary">
                            Email Address <span style={{ color: 'red' }}>*</span>
                          </Typography>
                          <TextField
                            fullWidth
                            id="outlined-basic"
                            name="email"
                            color="success"
                            placeholder={t('email')}
                            onBlur={handleBlur}
                            onChange={handleChange}
                            // disabled={props.mode === 'edit'}
                            type="text"
                            value={values.email}
                            variant="outlined"
                            error={Boolean(touched.email && errors.email)}
                            helperText={touched.email && errors.email ? t(errors.email) : ''}
                            sx={{
                              marginTop: "5px",
                              borderRadius: '30px',
                              '& .MuiOutlinedInput-root': {
                                borderRadius: '30px',
                                '& fieldset': {
                                  borderColor: 'transparent',
                                  backgroundColor: 'none',
                                  boxShadow: 'rgba(99, 99, 99, 0.2) 0px 2px 8px 0px'
                                },
                                '& input::placeholder': {
                                    fontSize: '0.85rem',
                                },
                                '& input': {
                                  padding: '8px 14px', // Adjust this to reduce height
                                  height: 'auto', // Optional, for better control
                                },
                                '& input:-webkit-autofill': {
                                  boxShadow: '0 0 0 1000px #fff inset !important',
                                  WebkitTextFillColor: '#000 !important',
                                  transition: 'background-color 5000s ease-in-out 0s',
                                },
                              },
                            }}
                          />
                          <ErrorFocus />
                        </Grid>
                        {/* <Grid item xs={12} sm={12}>
                          <PhoneNumber value={values.phone} onChange={handleInputChangePhone}  fullWidth country="us" specialLabel="" />
                        </Grid> */}
                        <Grid item xs={12} sm={12}>
                          <Typography variant="subtitle1" style={{ fontSize: "12px", color: '#333', fontWeight: 500, marginLeft: '15px',  paddingTop: "5px" }} color="textSecondary">
                            {t('Phone Number')}
                          </Typography>
                          <PhoneNumber

                            value={values.phone}
                            onChange={(value) => {
                              handleChange({ target: { name: 'phone', value } });
                            }}
                            onBlur={handleBlur}
                            fullWidth
                            country={'us'}
                            error={Boolean(touched.phone && errors.phone)}
                            helperText={touched.phone && errors.phone ? t(errors.phone) : ''}
                            inputProps={{
                              name: 'phone',
                            }}
                            containerStyle={{
                              width: '100%',
                              marginTop: '5px',
                            }}
                            inputStyle={{
                              width: '100%',
                              borderRadius: '30px',
                              borderColor: 'transparent',
                              boxShadow: 'rgba(99, 99, 99, 0.2) 0px 2px 8px 0px',
                              padding: '10px 14px',
                            }}
                            sx={{'& input::placeholder': {
                                fontSize: '0.85rem',
                            },}}
                            buttonStyle={{
                              borderRadius: '30px 0 0 30px',
                            }}

                          />

                        </Grid>

                        <Grid item xs={12} sm={12}>
                          <Typography variant="subtitle1" style={{ fontSize: "12px", color: '#333', fontWeight: 500, marginLeft: '15px',  paddingTop: "5px" }} color="textSecondary">
                            Create Password <span style={{ color: 'red' }}>*</span>
                          </Typography>
                          <TextField
                            fullWidth
                            id="outlined-basic"
                            name="password"
                            color="success"
                            // placeholder={t('Create Password')}
                            placeholder={'Create a password'}
                            onBlur={handleBlur}
                            onChange={(e) => {
                              const { value } = e.target;
                              handleChange(e);
                              const error = validatePassword(value);

                              if (error) {
                                setPasswordError(error);
                              } else {
                                setPasswordError('');
                              }
                            }}
                            type={visible ? 'text' : 'password'}
                            value={values.password}
                            variant="outlined"
                            error={Boolean(touched.password && errors.password)}
                            helperText={touched.password && errors.password ? t(errors.password) : ''}
                            sx={{
                              marginTop: "5px",
                              borderRadius: '30px',
                              '& .MuiOutlinedInput-root': {
                                borderRadius: '30px',
                                '& fieldset': {
                                  borderColor: 'transparent',
                                  backgroundColor: 'none',
                                  boxShadow: 'rgba(99, 99, 99, 0.2) 0px 2px 8px 0px'
                                },
                                '& input::placeholder': {
                                  fontSize: '0.85rem',
                                },
                                '& input': {
                                  padding: '8px 14px', // Adjust this to reduce height
                                  height: 'auto', // Optional, for better control
                                },
                                '& input:-webkit-autofill': {
                                  boxShadow: '0 0 0 1000px #fff inset !important',

                                  WebkitTextFillColor: '#000 !important',
                                  transition: 'background-color 5000s ease-in-out 0s',
                                },
                              },
                            }}
                            InputProps={{
                              endAdornment: (
                                <InputAdornment position="end">
                                  {visible ? (
                                    <VisibilityIcon
                                      className={classes.visibleIcon}
                                      onClick={handleClickShowPassword}
                                    />
                                  ) : (
                                    <VisibilityOffIcon
                                      className={classes.visibleIcon}
                                      onClick={handleClickShowPassword}
                                    />
                                  )}
                                </InputAdornment>
                              ),
                            }}
                          />
                          <ErrorFocus />
                        </Grid>

                        <Grid xs={12}>
                          <div
                            style={{
                              display: 'grid',
                              placeItems: 'center',
                              // marginTop: '20px',
                              marginLeft: '15px',
                               paddingTop: "10px",
                            }}
                          >
                            <FormControlLabel style={{ marginRight: '0px' }}
                              control={
                                <Checkbox
                                  id="agreeToTermsCheckbox"
                                  name="agreeToTerms"
                                  checked={values.agreeToTerms}
                                  onChange={handleChange}
                                  onBlur={handleBlur}
                                  color="primary"
                                  sx={{
                                    position: "relative",
                                    top: '3px',
                                    paddingLeft: '0px',
                                    color: 'rgb(90, 90, 90)',

                                    '&.Mui-checked': {
                                      color: 'rgb(81, 156, 254)',
                                    },
                                    '& input': {
                                      padding: '8px 14px',
                                      height: 'auto',
                                    },
                                  }}
                                />
                              }
                              label={
                                <Typography id="fontSmall" style={{
                                  fontSize: '13px', lineHeight: '1.3',
                                  position: 'relative', paddingTop: '8px'
                                }}>
                                  {t("I agree to the")}{' '}
                                  <a
                                    href="https://www.keyskillset.com/terms"
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    style={{
                                      color: '#519cfe',
                                      textDecoration: 'none',
                                    }}
                                  >
                                    {t('Terms & Conditions')}
                                  </a> and
                                  <a
                                    href="https://www.keyskillset.com/terms"
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    style={{
                                      color: '#519cfe',
                                      textDecoration: 'none',
                                    }}
                                  >
                                    {t(' Privacy Policy')}
                                  </a>
                                </Typography>
                              }
                            />

                          </div>
                        </Grid>
                        {touched.agreeToTerms && errors.agreeToTerms && (
                          <Typography style={{ marginLeft: 12, lineHeight: '1.2', marginTop: '5px' }} color="error" variant="body2">
                            {t(errors.agreeToTerms)}
                          </Typography>
                        )}
                        <Grid item xs={12}>
                          <LoadingButton
                            fullWidth
                            size="medium"
                            type="submit"
                            variant="contained"
                            loading={loading}
                            className={classes.createButton}
                            sx={{
                              borderRadius: '30px !important',
                              background: "#00b673",
                              '&:hover': {
                                backgroundColor: "#00b673 !important",
                              }
                            }}
                          >
                            {t('Create Account')}
                          </LoadingButton>
                          {/* <Button onClick={() => CreateButton(values)}>Create an Account</Button> */}
                        </Grid>
                        <Grid item xs={12} >
                          <Box
                            sx={{
                              display: 'flex',
                              alignItems: 'center',
                              textAlign: 'center',
                              color: '#999',
                              '&::before, &::after': {
                                content: '""',
                                flex: 1,
                                borderBottom: '1px solid #ccc',
                              },
                              '&::before': {
                                mr: 2,
                              },
                              '&::after': {
                                ml: 2,
                              },
                            }}
                          >
                            or continue with
                          </Box>
                        </Grid>




                        {/* <Box align="center" item xs="12" style={{flex: '2', marginTop: "5px"}}>
                          <Typography className="textLabel" marginBottom={0}><span>Or Register with</span></Typography>
                          
                          <Box className="logoSection">
                          <GoogleLogin style={{flexDirection: 'row-reverse'}} className="Googlelogo"
                            onSuccess={handleSuccess}
                            clientId="554509231860-4vafv579krptm0mful37kso93laub55j.apps.googleusercontent.com"
                            buttonText="Login with"
                            onFailure={handleFailure}
                            cookiePolicy={"single_host_origin"}
                          />

                          <Button style={{
                            backgroundColor: "#fff",
                            alignItems: 'center',
                            color: '#838383',
                            padding: '6px',
                            height: '43px',
                            borderRadius: '6px',
                            border: '1px solid transparent',
                           
                          }} onClick={handleOutLookLogin}><Typography style={{ color: '#838383', fontWeight: "500", fontSize: '14px', textTransform: "none"}} >Login with</Typography><AppleIcon /></Button>
                        
                          <Button style={{
                            backgroundColor: "#fff",
                            alignItems: 'center',
                            color: '#838383',
                            padding: '6px',
                            height: '43px',
                            borderRadius: '6px',
                            border: '1px solid transparent',
                           
                          }} onClick={handleOutLookLogin}><Typography style={{ color: '#838383', fontWeight: "500", fontSize: '14px', textTransform: "none"}} >Login with</Typography><AppleIcon /></Button>
                        </Box>
                        </Box> */}
                        <Grid align="center" item xs="12">
                          <Button className={classes.ssoButton}
                            component={RouterLink}
                            to={'/login'} >
                            <ApartmentIcon className={classes.ApartmentIcon} sx={{ color: '#222' }} />  SSO
                          </Button>
                          <Typography variant="body2" sx={{ display: 'flex', justifyContent: "center", marginTop: "5px", fontSize: "13px" }}>
                            {t('alreadyHaveAccount')} &nbsp;
                            {searchParams.get('isSubscribe') ? (
                              <Typography
                                component={RouterLink}
                                className={classes.create}
                                // to={'/login'}
                                to={{
                                  pathname: '/login',
                                  search: '?isSubscribe=true',
                                }}
                                sx={{ color: "#437BFC", fontSize: "13px", textDecoration: "none" }}
                              >
                                {t('login')}
                              </Typography>
                            ) : (
                              <Typography
                                component={RouterLink}
                                className={classes.create}
                                to={'/login'}
                                sx={{ color: "#437BFC", fontSize: "13px", textDecoration: "none" }}
                              >
                                {t('login')}
                              </Typography>
                            )}
                          </Typography>
                        </Grid>
                        {/* <Grid item xs={12}>
                    <Typography className={classes.email}>
                        For login related issue, &nbsp;
                        <RouterLink
                          to={{
                            // pathname: '/sign-up',
                            // search: '?isSubscribe=true',
                          }}
                          // state={location.state}
                          className={classes.signup}
                        >
                          Get Help
                        </RouterLink>
                      </Typography>
                    </Grid> */}
                      </Grid>
                    </Form>
                  )}
                </Formik>
              </Grid>

              <Grid item xs={12} md={7.5} id="hideSlider" ref={carouselRef}>
                <Carousel autoPlay
                  interval={4500}
                  showArrows={false}
                  showStatus={false}
                  axis='horizontal'
                  infiniteLoop
                  showThumbs={false}
                  showIndicators={false}
                  className={classes.carouselContainer}
                  style={{ height: carouselHeight }}
                >
                  {images.map((image, idx) => (
                    <div key={idx} style={{ height: '100%', display: 'flex', alignItems: 'center' }}>
                      <img
                        className={classes.slideImg}
                        alt={` ${idx + 1}`}
                        src={image}
                        style={{
                          width: '100%',
                          height: '100%',
                          objectFit: 'cover',
                          borderRadius: '15px'
                        }}
                      />
                    </div>
                  ))}
                </Carousel>
              </Grid>
            </Grid>
          </Container>
          {/* <SnackBar open={false} snackbarTitle={''} close={() => {}} />  */}
          <SnackBar open={snackbar} snackbarTitle={snackbarTitle} close={() => setSnackbar(false)} />
        </Box>

        :
        <Register details={details} userDetails={userDetails} />
      }


    </>

  );
};

const useStyles = makeStyles((theme) => ({
  image: {
    height: 'auto',
    width: '100%',
    marginTop: '155px',
    '@media (max-width: 600px)': {
      marginTop: '20px'
    }
  },
  login: {
    marginBottom: theme.spacing(2),
    marginTop: "0px",
    '@media (max-width: 500px)': {
      marginBottom: '0px',
      marginTop: '20px'
    }
  },
  container: {
    maxWidth: '1440px',
    margin: 'auto',
  },
  lineHeight1: {
    marginTop: '70px',
    marginBottom: '20px',
    fontSize: '22px',
    color: '#101828',
  },
  about: {
    color: '#000000',
    fontSize: '0.95rem',
    fontWeight: '450',
    textAlign: 'center',
  },


  contentalignment: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    margin: '2.5rem 0rem 4rem 0rem',
    flexWrap: 'wrap',
  },
  create: {
    color: '#437BFC',
    fontSize: '13px',
    cursor: 'pointer',
    fontWeight: '450',
    margin: '0px 10px 0px 0px',
    textDecoration: 'none',
  },

  cardscontainer: {
    height: 'auto',
    backgroundColor: '#E7F7EF',
    padding: '4rem 6.5rem',
    '@media (max-width: 640px)': {
      padding: '2rem 1.5rem',
    },
  },
  headingHands: {
    color: '#FFFFFF',
    fontSize: '1.2rem',
    marginLeft: '14px',
  },
  hands: {
    maxWidth: '84%',
    color: '#FFFFFF',
    fontSize: '1rem',
    fontWeight: 200,
    [theme.breakpoints.down('md')]: {
      maxWidth: '100%',
    },
  },
  form: {
    paddingRight: '0px',
    paddingLeft: '120px',
    paddingTop: '64px',
    paddingBottom: '64px',


    '@media (max-width: 1199px)': {
      padding: '2rem 4rem 4rem 5rem',
    },
    [theme.breakpoints.down('md')]: {
      padding: '2rem 5rem 4rem 5rem',
    },
    [theme.breakpoints.down('sm')]: {
      padding: '2rem 2rem 0rem 2rem',
    },
    '@media (max-width: 400px)': {
      padding: '2rem 1rem 3rem 1rem',
    },
  },
  content: {
    fontSize: '1.1rem',
    color: '#667085',
    marginBottom: '2rem',
  },
  started: {
    backgroundColor: '#EC7930',
    borderRadius: '100px',
    color: '#fff',
    padding: '8px',
    fontWeight: '400',
    textTransform: 'capitalize ! important',
    marginTop: '1rem',
    '&:hover': {
      backgroundColor: '#EC7930',
      boxShadow: '0 0 1px 6px rgba(236, 121, 48, 40%),0 0 1px 0px rgba(236,121,48, 20%)',
    },
  },
  icon: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'flex-start',
  },

  email: {
    padding: '2px',
    fontSize: "13px",
    fontStyle: "normal",
    fontWeight: "400"
  },
  visibleIcon: {
    color: 'grey',
    '&:hover': {
      cursor: 'pointer',
    },
  },
  signup: {
    color: "#D52F37"
  },

  mainContainer: {
    background: "#F0F0F0",
    height: '100vh',
    display: 'flex',
    '@media (max-width: 767px)': {
      padding: "20px 0",
    },
  },
  boxContainer: {
    backgroundImage: `url(${BackgroundImg})`,
    backgroundSize: 'cover',
    backgroundRepeat: 'no-repeat',
    // height: '100vh',
    width: "500px",
    minHeight: "532px",
    marginTop: "12px",
    borderRadius: "20px",
    // animationName: "changeBackground",
    // animationDuration: "12s",
    // animationIterationCount: "infinite",
    // display: 'flex',
    // flexDirection: 'column',
    // justifyContent: 'center',
    '@media (max-width: 767px)': {
      height: '240px',
      marginTop: '70px'
    },

  },
  formcontainer: {
    background: "#FFFFFF",
    padding: '26px',
    paddingTop: '18px',
    borderRadius: "20px",
    paddingBottom: '18px',
    marginTop: "0px",
    minHeight: "500px",
    [theme.breakpoints.down('sm')]: {
      padding: '20px',
    },
  },
  SignupPanel: {
    display: 'flex',
    alignItems: 'center',
    minHeight: "100%",
  },

  createButton: {
    borderRadius: '30px',
    marginTop: '5px',
    background: "#00b673",
    fontSize: "12px",
  },
  text_one: {
    fontSize: "12px",
    alignItems: "center",
    justifyContent: "center",
    display: "flex",
    // marginTop: "10px",
  },
  carouselContainer: {
    backgroundColor: 'transparent',
    maxWidth: '100%',
    margin: 'auto',
    transition: 'height 0.3s ease',
    '& .carousel-slider, & .slider-wrapper, & .slider': {
      height: '100% !important',
    },
  },
  ssoButton: {
    width: '250px',
    borderRadius: '30px',
    border: '1px solid #222',
    backgroundColor: '#fff',
    cursor: 'pointer',
    color: '#222',
    '&:hover': {
      backgroundColor: '#222',
      color: '#fff',
      border: '1px solid #222',
      '& .MuiSvgIcon-root': {
        color: '#fff',
      },
    },
  },
  ApartmentIcon: {
    marginRight: '12px',
  },
  whiteBorder: {
    background: "whitesmoke",
    marginTop: '10px',
    height: "1px",
    padding: '1px',
    borderRadius: '5px',
    marginLeft: '15px'
  },
  slideImg: {
    height: '100%',
    objectFit: 'cover',
    '@media (max-width: 540px)': {
      maxHeight: '250px',
    }
  },

}));
