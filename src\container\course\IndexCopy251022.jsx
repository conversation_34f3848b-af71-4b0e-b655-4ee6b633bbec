import React, { useState, useEffect, useRef } from 'react';
import { Box, Container, IconButton, Grid, Typography } from '@mui/material';
import { makeStyles } from '@mui/styles';
import { styled, useTheme } from '@mui/material/styles';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import MobileStepper from '@mui/material/MobileStepper';

import Button from '@mui/material/Button';
import KeyboardArrowLeft from '@mui/icons-material/KeyboardArrowLeft';
import KeyboardArrowRight from '@mui/icons-material/KeyboardArrowRight';
import { useSearchParams, useLocation, useNavigate } from 'react-router-dom';
import swal from 'sweetalert';
import { useSelector } from 'react-redux';
import LottieLoading from '../../components/LottieLoading';
import Page from '../../components/Page';
import SubModuleProgress from '../../components/progress/SubModuleProgress';
import Snackbar from '../../components/snackbar/snackbar';
import TopicList from './TopicsList';
import InfoIcon from '../../assets/customIcons/infoScreenIcon.svg';
import palette from '../../theme/palette';
import simulationApi from '../../services/simulation';
import helper from '../../utils/helper';
import customeKeys from './customeKeys';
import client from '../../services/apiClient';

const BeginCourse = () => {
  const classes = useStyles();
  const theme = useTheme();
  const location = useLocation();
  const navigate = useNavigate();
  const userRole = useSelector((state) => state.userInfo && state.userInfo.role);
  const reduxDetails = useSelector((state) => state);
  const [openSnackbar, setOpenSnackbar] = useState(false);
  const [snackbarTitle, setSnackbarTitle] = useState('');
  const [activeTopic, setActiveTopic] = useState(null);
  const [activeStep, setActiveStep] = useState(0);
  const [topics, setTopics] = useState([]);
  const [screens, setScreens] = useState([]);
  const [activeKeyIndex, setActiveKeyIndex] = useState(0);
  const [searchParams] = useSearchParams();
  const [subModuleData, setSubModuleData] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isWrongKeyPressed, setIsWrongKeyPressed] = useState(false);
  const [mic, setMic] = useState(true);
  const [keyboard, setKeyboard] = useState('windows');

  const [resumeChecked, setResumeChecked] = useState(false);
  const audio = new Audio(
    'https://keySkillsetbucket.s3.us-east-2.amazonaws.com/courseInfo/excellent_snd1654843362.mp3'
  );

  // get topics
  useEffect(() => {
    getOS();
    getTopicsFromApi();
  }, []);

  const getBase64FromUrl = async (url = 'https://ik.imagekit.io/k38yuwpb2/windows1654795623.jpeg') => {
    const data = await fetch(url);
    const blob = await data.blob();
    return new Promise((resolve) => {
      const reader = new FileReader();
      reader.readAsDataURL(blob);
      reader.onloadend = () => {
        const base64data = reader.result;
        resolve(base64data);
      };
    });
  };

  function getOS() {
    const userAgent = window.navigator.userAgent;
    const platform = window.navigator?.userAgentData?.platform || window.navigator.platform;
    const macosPlatforms = ['Macintosh', 'MacIntel', 'MacPPC', 'Mac68K', 'macOS'];
    const windowsPlatforms = ['Win32', 'Win64', 'Windows', 'WinCE'];
    const iosPlatforms = ['iPhone', 'iPad', 'iPod'];
    let os = 'mac';

    if (macosPlatforms.indexOf(platform) !== -1) {
      os = 'mac';
    } else if (iosPlatforms.indexOf(platform) !== -1) {
      os = 'mac';
    } else if (windowsPlatforms.indexOf(platform) !== -1) {
      os = 'windows';
    } else if (/Android/.test(userAgent)) {
      os = 'windows';
    } else if (/Linux/.test(platform)) {
      os = 'windows';
    }
    setKeyboard(os);
    return os;
  }
  const getTopicsFromApi = async (subModuleId = searchParams.get('id')) => {
    setIsLoading(true);
    simulationApi
      .getTopics(subModuleId)
      .then((res) => {
        console.log(res, 'res');
        if (res.ok) {
          const dataSort = res.data.subModuleTopics.sort(helper.sortByPositionIndex);
          setSubModuleData({
            name: res.data.courseSubmoduleName,
            progress: res.data.completed,
            skillsetIq: res.data.skillsetIq,
          });
          setTopics(dataSort);

          if (!resumeChecked && searchParams.get('topic')) {
            const qpTopic = searchParams.get('topic');
            const resumeTopicIndex = dataSort.findIndex((item) => item.id.toString() === qpTopic);
            setActiveTopic(dataSort[resumeTopicIndex]);
            getScreens(dataSort[resumeTopicIndex].id);
          } else {
            setActiveTopic(dataSort[0]);
            getScreens(dataSort[0].id);
          }
        } else if (res.status === 400) {
          if (res.data.message === 'Subscription_Required') {
            if (reduxDetails.isLogin) {
              alert('Access denied. You need to subscribe to get access to this submodule.');

              if (userRole === 'USER_DTC') {
                navigate('/auth/my-courses');
              } else {
                navigate('/app/course');
              }
            } else {
              alert('Access denied. You need to subscribe to get access to this submodule.');
              navigate('/login');
            }
          }
        }
      })
      .catch((error) => {
        console.log('error data getting from api', error);
        setIsLoading(false);
      });
  };

  const updateTopics = (data) => {
    const dataSort = data.subModuleTopics.sort(helper.sortByPositionIndex);
    setSubModuleData({ name: data.courseSubmoduleName, progress: data.completed, skillsetIq: data.skillsetIq });
    setTopics(dataSort);

    if (data.completed === '50' && data.skillsetIq === false) {
      swal({
        // title: 'Good job!',
        text: 'You have successfully completed all the topics. You can now take the skillset IQ.',
        icon: 'success',
        buttons: {
          confirm: {
            text: 'Take SkillSet IQ',
            value: true,
            visible: true,
            className: '',
            closeModal: true,
          },
          cancel: {
            text: 'Continue Learning',
            value: null,
            visible: true,
            className: '',
            closeModal: true,
          },
        },
        closeOnClickOutside: false,
      }).then((res) => {
        if (res) {
          if (userRole === 'USER_DTC') {
            navigate(`/auth/skilliq-test?subModuleId=${searchParams.get('id')}`, { state: location.state });
          } else {
            navigate(`/app/skilliq-test?subModuleId=${searchParams.get('id')}`, { state: location.state });
          }
        }
      });
    }
  };

  const topicClickHandle = (index) => {
    setIsLoading(true);
    setActiveStep(0);
    setActiveKeyIndex(0);
    setActiveTopic(topics[index]);
    getScreens(topics[index].id);
  };

  const getScreens = async (topicId) => {
    setIsLoading(true);
    simulationApi
      .getScreens(topicId)
      .then(async (res) => {
        console.log(res);
        try {
          if (res.ok) {
            const sortedData = res.data.sort(helper.sortByPositionIndex);
            console.log('before promise');
            await Promise.all(
              sortedData.map(async (item, index) => {
                if (item.type === 'SUCCESS' || item.type === 'INTRO') {
                  const base64Img = await getBase64FromUrl(item.backgroundImg);
                  console.log('Intro and success');
                  sortedData[index] = {
                    ...item,
                    backgroundBase64: base64Img,
                  };
                }

                // if the screen type is action and key is combined
                if (item.type === 'ACTION' && item.keyType === 'COMBINED_HOT_KEYS') {
                  const url = item.keyObj[0][keyboard].backgroundImg;
                  if (url) {
                    const base64Img = await getBase64FromUrl(url);
                    console.log(`Action Combined url ${url}`);
                    sortedData[index].keyObj[0][keyboard] = {
                      ...item.keyObj[0][keyboard],
                      backgroundBase64: base64Img,
                    };
                  }
                }

                if (item.type === 'ACTION' && item.keyType === 'INDIVIDUAL_HOT_KEYS') {
                  await Promise.all(
                    item.keyObj.map(async (keys, _index) => {
                      const url = keys[keyboard].backgroundImg;

                      if (url) {
                        const base64Img = await getBase64FromUrl(url);
                        console.log('Action and individual', sortedData, 'index', index, sortedData[index].keyObj);
                        sortedData[index].keyObj[_index][keyboard] = { ...keys[keyboard], backgroundBase64: base64Img };
                      }
                    })
                  );
                }
              })
            );

            console.log('after promise', sortedData);
            setScreens(sortedData);
            setIsLoading(false);

            if (!resumeChecked && searchParams.get('screen')) {
              setResumeChecked(true);
              const qpScreenId = searchParams.get('screen');
              const screenIndex = sortedData.findIndex((item) => item.id.toString() === qpScreenId);
              setActiveStep(screenIndex);
            }
          }
        } catch (error) {
          console.log('error after data received from getScreens api', error);
          setIsLoading(false);
        }
      })
      .catch((error) => {
        console.log('error getting screens from api ', error);
        setIsLoading(false);
      });
  };

  const handleNext = () => {
    setActiveKeyIndex(0);
    setIsLoading(true);
    if (activeStep === screens.length - 1) {
      // get next topic data and moving to next step
      const nextTopicIndex = topics.findIndex((item) => item.id === activeTopic.id) + 1;

      // if user in last topic completes the navigating to first topic
      if (topics.length > nextTopicIndex) {
        console.log('can go to next topic from skip');
        topicClickHandle(nextTopicIndex);
      } else {
        console.log('can not go to next topic from skip');
        topicClickHandle(0);
      }
    } else {
      setActiveStep((prevActiveStep) => prevActiveStep + 1);
      setIsLoading(false);
    }
  };

  const handleBack = () => {
    setActiveStep((prevActiveStep) => prevActiveStep - 1);
  };

  const introBtnClickHandle = () => {
    if (screens[activeStep].type === 'INTRO' || screens[activeStep].type === 'SUCCESS') {
      if (activeStep === screens.length - 1) {
        // get next topic data and moving to next step
        const nextTopicIndex = topics.findIndex((item) => item.id === activeTopic.id) + 1;

        // if user in last topic completes the navigating to first topic
        if (topics.length > nextTopicIndex) {
          console.log('can go to next topic from skip');
          topicClickHandle(nextTopicIndex);
        } else {
          console.log('can not go to next topic from skip');
          swal({
            // title: 'Good job!',
            text: 'You have reached the end of the this submodule. You can take the skillset IQ or go to next submodule.',
            buttons: {
              confirm: {
                text: 'Take SkillSet IQ',
                value: true,
                visible: true,
                className: '',
                closeModal: true,
              },
              cancel: {
                text: 'Continue Learning',
                value: null,
                visible: true,
                className: '',
                closeModal: true,
              },
            },
            closeOnClickOutside: false,
          }).then((res) => {
            if (res) {
              if (userRole === 'USER_DTC') {
                navigate(`/auth/skilliq-test?subModuleId=${searchParams.get('id')}`, { state: location.state });
              } else {
                navigate(`/app/skilliq-test?subModuleId=${searchParams.get('id')}`, { state: location.state });
              }
            }
          });
          topicClickHandle(0);
        }
      } else {
        setActiveStep((prevActiveStep) => prevActiveStep + 1);
      }
    }
  };

  const handleMicToggle = () => {
    setMic(!mic);
  };

  const IntroScreen = () => {
    const horizontal = screens[activeStep].horizontalAlign.code === 'MIDDLE' ? 'center' : 'flex-start';
    const vertical = screens[activeStep].verticalAlign.code === 'CENTER' ? 'center' : 'start';

    const nextBtnElement = useRef();

    useEffect(() => {
      console.log('keyIntro component rendered');
      // if screen is success then audio will paly
      if (!mic) return;
      if (screens[activeStep].type === 'SUCCESS') audio.play();
    }, []);

    // adding and removing keyboard event listener
    useEffect(() => {
      window.addEventListener('keydown', handleKeyDownIntro);

      // removing EventListener when comp unmount
      return () => {
        window.removeEventListener('keydown', handleKeyDownIntro);
      };
    }, []);

    const handleKeyDownIntro = (event) => {
      console.log('keyDown event fires intro');
      event.preventDefault();

      if (event.repeat) {
        console.log('refeated');
        return false;
      }
      nextBtnElement.current.click();
    };

    return (
      <Box
        className={classes.gridContainer}
        sx={{
          justifyContent: screens[activeStep].horizontalAlign.code === 'RIGHT' ? 'flex-end' : horizontal,
          alignItems: screens[activeStep].verticalAlign.code === 'BOTTOM' ? 'end' : vertical,
          margin: 0,
          backgroundImage: screens.length > 0 ? `url(${screens[activeStep].backgroundBase64})` : '',
        }}
      >
        <Box className={classes.introContainer}>
          <div className={classes.introItem}>
            {screens[activeStep].type === 'INTRO' ? (
              <div className={classes.introImg}>
                <img src={InfoIcon} alt="infoIcon" style={{ margin: 'auto' }} />
              </div>
            ) : null}
            <div style={{ minHeight: 60 }}>
              <Typography
                sx={{ fontSize: '1.125rem' }}
                dangerouslySetInnerHTML={{
                  __html:
                    screens.length > 0 ? screens[activeStep < screens.length ? activeStep : 0].description : '&nbsp;',
                }}
              />
            </div>
            <div>
              <Button ref={nextBtnElement} sx={{marginBottom: '10px'}} variant="outlined" color="primary" onClick={introBtnClickHandle}>
                {screens[activeStep].buttonLabel || 'okay'}
              </Button>
              <Typography style={{ fontSize: '0.675rem', padding: 5 }}>Press any key to continue</Typography>
            </div>
             <Typography variant="body2" fontSize="12px" sx={{background: '#ebbb6ca8', padding: '4px', fontWeight: "600",  position: 'relative', top: '16px'}}>Please Note: You can drag this action dialogue box across the screen!</Typography>
          </div>
        </Box>
      </Box>
    );
  };

  const ActionScreen = () => {
    const [backgroundImg, setBackgroundImg] = useState();
    // extracting screen of active step or first step if derectly comes for clickin topics or start sub module in previous page
    const screenInfo = screens.length > 0 ? screens[activeStep < screens.length ? activeStep : 0] : null;

    let keys = null;

    // extracting key types INDIVIDUAL_HOT_KEYS || COMBAINED_HOT_KEYS
    const keyType = screenInfo.keyType;

    // setting up keyBoard here WINDOWS || mac
    const keyBoard = keyboard.toLowerCase();

    // if screens exist extracting keyObj into keys
    if (screenInfo) {
      keys = screenInfo.keyObj;
    }

    console.log(keys);
    // adding and removing keyboard event listener
    useEffect(() => {
      window.addEventListener('keydown', handleKeyDown);
      window.addEventListener('keyup', handleKeyUp);

      // removing EventListener when comp unmount
      return () => {
        window.removeEventListener('keydown', handleKeyDown);
        window.removeEventListener('keyup', handleKeyUp);
      };
    }, []);

    // resetting to focus on first key is keyup not completing
    const handleKeyUp = (event) => {
      event.preventDefault();
      if (keyType === 'COMBINED_HOT_KEYS') {
        const keyArray = keys[0][keyBoard].keyCode;
        if (event.keyCode.toString() !== keyArray[activeKeyIndex]) {
          if (isWrongKeyPressed) {
            setActiveKeyIndex(activeKeyIndex);
          } else {
            setActiveKeyIndex(0);
          }
        }
      }
    };

    const multipleKeyCode = customeKeys.multipleKeyCode;

    const handleKeyDown = (event) => {
      console.log('keyDown event fires');
      event.preventDefault();
      // alert(`A key was pressed ${event.keyCode} activeIndex ${activeKeyIndex}`);
      setIsWrongKeyPressed(false);
      if (event.repeat) {
        console.log('refeated');
        return false;
      }
      const selectedKey = multipleKeyCode.filter((item) => item.key === event.key.toString());
      const isMultipleKey = selectedKey.length > 0 && selectedKey[0].keyCode.includes(event.keyCode);

      if (keyType === 'INDIVIDUAL_HOT_KEYS') {
        // statement for checking valid/right key is pressed
        if (
          ((event.keyCode.toString() === keys[activeKeyIndex][keyBoard].keyCode[0] || isMultipleKey) &&
            (event.key.toString().toLowerCase() === keys[activeKeyIndex][keyBoard].keyName[0].toLowerCase() ||
              (keys[activeKeyIndex][keyBoard].keyName[0].toLowerCase() === 'option' &&
                event.keyCode.toString() === '18') ||
              (event.key.toString().toLowerCase() === 'altgraph' && event.keyCode.toString() === '18') ||
              (keys[activeKeyIndex][keyBoard].keyName[0].toLowerCase() === 'space' &&
                event.keyCode.toString() === '32') ||
              (keys[activeKeyIndex][keyBoard].keyName[0].toLowerCase() === 'Command' &&
                event.keyCode.toString() === '91') ||
              (keys[activeKeyIndex][keyBoard].keyName[0].toLowerCase() === 'Command' &&
                event.keyCode.toString() === '93') ||
              (keys[activeKeyIndex][keyBoard].keyName[0].toLowerCase() === 'delete' &&
                event.keyCode.toString() === '8')) &&
            event.shiftKey === false) ||
          ((event.keyCode.toString() === keys[activeKeyIndex][keyBoard].keyCode[0] || isMultipleKey) &&
            (event.key.toString().toLowerCase() === keys[activeKeyIndex][keyBoard].keyName[0].toLowerCase() ||
              (keys[activeKeyIndex][keyBoard].keyName[0].toLowerCase() === 'space' &&
                event.keyCode.toString() === '32') ||
              (keys[activeKeyIndex][keyBoard].keyName[0].toLowerCase() === 'option' &&
                event.keyCode.toString() === '18') ||
              (event.key.toString().toLowerCase() === 'altgraph' && event.keyCode.toString() === '18') ||
              (keys[activeKeyIndex][keyBoard].keyName[0].toLowerCase() === 'delete' &&
                event.keyCode.toString() === '8') ||
              (keys[activeKeyIndex][keyBoard].keyName[0].toLowerCase() === 'Command' &&
                event.keyCode.toString() === '91') ||
              (keys[activeKeyIndex][keyBoard].keyName[0].toLowerCase() === 'Command' &&
                event.keyCode.toString() === '93')) &&
            event.shiftKey === true)
        ) {
          // checking for last key if it has multiple keys
          if (activeKeyIndex === keys.length - 1) {
            // this statement is to check for last screen and moving to next topic
            if (activeStep === screens.length - 1) {
              console.log('screens end');

              // post result
              postResult(screenInfo);

              // clearing screens
              setScreens([]);

              // resetting active screen to zero
              // setActiveStep(0);

              // get next topic data and moving to next step
              const nextTopicIndex = topics.findIndex((item) => item.id === activeTopic.id) + 1;
              // const copyTopics = topics.map((topic, index) => ({ ...topic, selected: index === nextTopicIndex }));
              // console.log('selected : ', copyTopics);
              // setTopics(copyTopics);

              // if user in last topic completes the navigating to first topic
              if (topics.length > nextTopicIndex) {
                console.log('can go to next topic');
                topicClickHandle(nextTopicIndex);
              } else {
                console.log('can not go to next topic');
                // topicClickHandle(0);

                swal({
                  // title: 'Good job!',
                  text: 'You have reached the end of the this submodule. You can take the skillset IQ or go to next submodule.',
                  buttons: {
                    confirm: {
                      text: 'Take SkillSet IQ',
                      value: true,
                      visible: true,
                      className: '',
                      closeModal: true,
                    },
                    cancel: {
                      text: 'Cancel',
                      value: null,
                      visible: true,
                      className: '',
                      closeModal: true,
                    },
                  },
                  closeOnClickOutside: false,
                }).then((res) => {
                  if (res) {
                    navigate(`/app/skilliq-test?subModuleId=${searchParams.get('id')}`, { state: location.state });
                  }
                });
              }

              // resetting active key index if has multiple keys
              setActiveKeyIndex(0);
            } else {
              setActiveKeyIndex(0);
              postResult(screenInfo);
              handleNext();
            }
          } else {
            // if is not last key the key index increase
            setActiveKeyIndex(activeKeyIndex + 1);
          }
        } else if (event.shiftKey && event.keyCode === 16) {
          console.log('shift key');
        } else {
          // wrong key is pressed
          setIsWrongKeyPressed(true);
          toggleClassName();
          console.log('wrong key entered : ', event.keyCode);
        }
      }

      if (keyType === 'COMBINED_HOT_KEYS') {
        console.log('Combined keys', keys[0][keyBoard]);

        const keyArray = keys[0][keyBoard].keyCode;
        const keyArrayName = keys[0][keyBoard].keyName;
        // custome condition to zoom in and out for XL sheet
        let isPlusOrMinus = false;
        if (event.ctrlKey && event.altKey && event.keyCode === 187) {
          isPlusOrMinus = true;
        } else if (event.altKey && event.metaKey && event.keyCode === 189) {
          isPlusOrMinus = true;
        } else if (event.altKey && event.metaKey && event.keyCode === 187) {
          isPlusOrMinus = true;
        } else {
          isPlusOrMinus = false;
        }

        if (
          ((event.keyCode.toString() === keyArray[activeKeyIndex] || isMultipleKey) &&
            (event.key.toString().toLowerCase() === keyArrayName[activeKeyIndex].toLowerCase() ||
              isPlusOrMinus ||
              ((event.altKey || event.ctrlKey) && event.keyCode.toString() === keyArray[activeKeyIndex]) ||
              (event.metaKey && event.shiftKey && event.keyCode.toString() === keyArray[activeKeyIndex]) ||
              (keyArrayName[activeKeyIndex].toLowerCase() === 'option' && event.keyCode.toString() === '18') ||
              (keyArrayName[activeKeyIndex].toLowerCase() === 'space' && event.keyCode.toString() === '32') ||
              (keyArrayName[activeKeyIndex].toLowerCase() === 'delete' && event.keyCode.toString() === '8') ||
              (keyArrayName[activeKeyIndex].toLowerCase() === 'command' && event.keyCode.toString() === '91') ||
              (keyArrayName[activeKeyIndex].toLowerCase() === 'command' && event.keyCode.toString() === '93') ||
              (event.key.toString().toLowerCase() === 'altgraph' && event.keyCode.toString() === '18') ||
              (event.key.toString().toLowerCase() === 'dead' && event.altKey === true)) &&
            event.shiftKey === false) ||
          ((event.keyCode.toString() === keyArray[activeKeyIndex] || isMultipleKey) &&
            (event.key.toString().toLowerCase() === keyArrayName[activeKeyIndex].toLowerCase() ||
              ((event.altKey || event.ctrlKey) && event.keyCode.toString() === keyArray[activeKeyIndex]) ||
              (event.metaKey && event.shiftKey && event.keyCode.toString() === keyArray[activeKeyIndex]) ||
              (keyArrayName[activeKeyIndex].toLowerCase() === 'option' && event.keyCode.toString() === '18') ||
              (keyArrayName[activeKeyIndex].toLowerCase() === 'space' && event.keyCode.toString() === '32') ||
              (keyArrayName[activeKeyIndex].toLowerCase() === 'delete' && event.keyCode.toString() === '8') ||
              (keyArrayName[activeKeyIndex].toLowerCase() === 'command' && event.keyCode.toString() === '91') ||
              (keyArrayName[activeKeyIndex].toLowerCase() === 'command' && event.keyCode.toString() === '93') ||
              (event.key.toString().toLowerCase() === 'altgraph' && event.keyCode.toString() === '18') ||
              (event.key.toString().toLowerCase() === 'dead' && event.altKey === true)) &&
            event.shiftKey === true)
        ) {
          // console.log('inside true');
          if (activeKeyIndex === keyArray.length - 1) {
            // console.log('last key press from Combined Screen');
            postResult(screenInfo);

            // this statement is to check for last screen and moving to next topic
            if (activeStep === screens.length - 1) {
              // clearing screens
              setScreens([]);

              // get next topic data and moving to next step
              const nextTopicIndex = topics.findIndex((item) => item.id === activeTopic.id) + 1;

              // if user in last topic completes the navigating to first topic
              if (topics.length > nextTopicIndex) {
                console.log('can go to next topic from Combined Screen');
                topicClickHandle(nextTopicIndex);
              } else {
                console.log('can not go to next topic from Combined Screen');
                // topicClickHandle(0);
                swal({
                  // title: 'Good job!',
                  text: 'You have reached the end of the this submodule. You can take the skillset IQ or go to next submodule.',
                  buttons: {
                    confirm: {
                      text: 'Take SkillSet IQ',
                      value: true,
                      visible: true,
                      className: '',
                      closeModal: true,
                    },
                    cancel: {
                      text: 'Cancel',
                      value: null,
                      visible: true,
                      className: '',
                      closeModal: true,
                    },
                  },
                  closeOnClickOutside: false,
                }).then((res) => {
                  if (res) {
                    navigate(`/app/skilliq-test?subModuleId=${searchParams.get('id')}`, { state: location.state });
                  }
                });
              }
            } else {
              console.log('can got to next screen from Combined Screen');
              handleNext();
              setActiveKeyIndex(0);
            }
          } else {
            console.log(`Match Key Index ${keyArray[activeKeyIndex]} and key`);
            setActiveKeyIndex(activeKeyIndex + 1);
          }
        } else if (event.shiftKey && event.keyCode === 16) {
          console.log('shift key');
        } else {
          setIsWrongKeyPressed(true);
          toggleClassName();
        }

        // keys[0][keyBoard].keyCode.map((itemKey, index, oriArray) => {
        //   console.log('current key', itemKey);
        //   if (event.keyCode.toString() === itemKey) {
        //     if (activeKeyIndex === oriArray.length - 1) {
        //       console.log('match last key', itemKey);
        //       setActiveKeyIndex(0);
        //       handleNext();
        //     } else {
        //       console.log(`Match Key Index ${index} and key`, itemKey);
        //       setActiveKeyIndex(activeKeyIndex + 1);
        //     }
        //   } else {
        //     console.log(`key not match active index ${activeKeyIndex} and index ${index}`);
        //     setIsWrongKeyPressed(true);
        //     toggleClassName();
        //   }
        //   return false;
        // });
      }
    };

    const toggleClassName = () => {
      setTimeout(() => {
        if (keyType === 'COMBINED_HOT_KEYS') {
          setActiveKeyIndex(0);
        }
        setIsWrongKeyPressed(false);
      }, 500);
    };

    const postResult = (screen) => {
      console.log('post result', screen);
      if (screen.isCompleted) return;

      const payload = {
        screenId: screen.id,
        keyboard: keyBoard.toUpperCase(),
        keyObj: screen.keyObj.map((keyItems) => {
          return {
            id: keyItems.id,
            keyCode: keyItems[keyBoard].keyCode,
          };
        }),
      };

      simulationApi.postResult(payload).then((res) => {
        if (res.ok) {
          console.log('post result res : ', res.data);
          // reload topics this is to set is completed check
          updateTopics(res.data);
        }
      });
      console.log('payload', payload);
    };

    return (
      <Box
        className={classes.gridContainer}
        sx={{
          justifyContent: 'flex-end',
          alignItems: 'center',
          backgroundImage:
            keys &&
            `url(${
              keyType === 'INDIVIDUAL_HOT_KEYS'
                ? keys[activeKeyIndex][keyBoard].backgroundBase64
                : keyType === 'COMBINED_HOT_KEYS' && keys[0][keyBoard].backgroundBase64
            })`,
        }}
      >
        <Box className={classes.introContainer} style={{ backgroundColor: '#e7e7e7cf' }}>
          <div style={{ textAlign: 'center' }}>
            <div style={{ minHeight: 50 }}>
              <Typography
                variant="body1"
                className={classes.actionDescription}
                dangerouslySetInnerHTML={{
                  __html: screenInfo ? screenInfo.description : '&nbsp;',
                }}
              />
            </div>
            <div className={classes.keyName}>
              {keyType === 'INDIVIDUAL_HOT_KEYS' &&
                keys &&
                keys.map((item, index) => (
                  <Typography
                    className={[
                      classes.activeKey,
                      index === activeKeyIndex && classes.buttoneight,
                      isWrongKeyPressed === true && index === activeKeyIndex && classes.keyWrong,
                    ]}
                    style={{
                      marginRight: 10,
                      // backgroundColor: activeKeyIndex === index ? '#ddd' : 'none',
                    }}
                  >
                    {customeKeys.arrowIcons[item[keyBoard].keyCode.toString()]
                      ? customeKeys.arrowIcons[item[keyBoard].keyCode.toString()]
                      : item[keyBoard].keyName}
                  </Typography>
                ))}

              {keyType === 'COMBINED_HOT_KEYS' &&
                keys &&
                keys[0][keyBoard].keyName.map((item, index, oriArray) => (
                  <>
                    <Typography
                      className={[
                        classes.activeKey,
                        index === activeKeyIndex && classes.buttoneight,
                        isWrongKeyPressed === true && index === activeKeyIndex && classes.keyWrong,
                      ]}
                      style={{
                        marginRight: 10,

                        // backgroundColor: activeKeyIndex === index ? '#ddd' : 'none',
                      }}
                    >
                      {customeKeys.arrowIcons[keys[0][keyboard].keyCode[index].toString()]
                        ? customeKeys.arrowIcons[keys[0][keyboard].keyCode[index].toString()]
                        : item}
                    </Typography>
                    {index < oriArray.length - 1 && (
                      <Typography style={{ fontSize: '1.5rem', color: '#5d5d5d' }}>+</Typography>
                    )}
                  </>
                ))}
              {/* {keyType === 'INDIVIDUAL_HOT_KEYS'
                ? keys &&
                  keys.map((item, index, orgArray) => (
                    <>
                      <Typography style={{ padding: 10, border: '1px solid #ddd' }}>
                        {item[keyBoard].keyName[0]}
                      </Typography>
                      <Typography style={{ padding: 10 }}>
                      {index >= 0 && index < orgArray.length - 1 ? '+' : ''}
                    </Typography>
                    </>
                  ))
                : null} */}
            </div>
          </div>
        </Box>
      </Box>
    );
  };

  const handleSwitchKeyboard = (value) => {
    setKeyboard(value);
  };

  return (
    <Page title={'Begin Course'} style={{ padding: '0px', paddingTop: '45px !important' }}>
      <Grid container>
        <Grid item sm={9}>
          <div style={{ position: 'relative' }}>
            {isLoading ? (
              <div
                style={{
                  position: 'absolute',
                  width: '100%',
                  height: `calc(100vh - 70px)`,
                  backgroundColor: 'rgba(255, 255, 255, 0.8)',
                  display: 'flex',
                  flexFlow: 'column',
                  justifyContent: 'center',
                  alignItems: 'center',
                  zIndex: 999,
                }}
              >
                {/* <Typography>Loading...</Typography> */}
                <LottieLoading loading={isLoading} />
              </div>
            ) : (
              screens.length === 0 && (
                <Typography align="center" variant="h6" sx={{ marginTop: '1rem' }}>
                  This topic contains no screen
                </Typography>
              )
            )}

            {screens.length > 0 && screens[activeStep].type === 'INTRO' ? <IntroScreen /> : null}
            {screens.length > 0 && screens[activeStep].type === 'SUCCESS' ? <IntroScreen /> : null}
            {screens.length > 0 && screens[activeStep].type === 'ACTION' ? <ActionScreen /> : null}
          </div>
        </Grid>
        <Grid item sm={3}>
          <Box className={classes.courseItem} sx={{ height: 1, display: 'flex', flexDirection: 'column' }}>
            <SubModuleProgress
              value={subModuleData && subModuleData.progress}
              subModuleName={subModuleData && subModuleData.name}
              moduleName={
                <div style={{ display: 'flex', alignItems: 'center' }}>
                  <IconButton
                    size="small"
                    sx={{ backgroundColor: '#edededc9' }}
                    onClick={() => {
                      if (userRole === 'USER_DTC') {
                        navigate('/auth/course-details', { state: location.state });
                      } else {
                        navigate('/app/course-details', { state: location.state });
                      }
                    }}
                  >
                    <ArrowBackIcon fontSize="small" sx={{ fontSize: '1rem' }} />
                  </IconButton>
                  <Typography sx={{ fontSize: '0.75rem' }}>&nbsp;{searchParams.get('module')}</Typography>
                </div>
              }
              mic={mic}
              micToggle={handleMicToggle}
              handleKeyboard={handleSwitchKeyboard}
              keyboardType={keyboard}
            />

            <TopicList
              onClickCallBack={topicClickHandle}
              data={topics}
              selectedIndex={activeTopic && activeTopic.id}
              submoduleId={searchParams.get('id')}
              location={location}
            />
            <Box sx={{ flexGrow: 1 }} />

            <Box>
              <MobileStepper
                variant="progress"
                steps={screens.length}
                position="static"
                activeStep={activeStep}
                LinearProgressProps={{
                  className: classes.hideLinearProgress, // and set this class
                }}
                nextButton={
                  <Button size="small" disabled={isLoading} onClick={handleNext}>
                    Skip
                    {theme.direction === 'rtl' ? <KeyboardArrowLeft /> : <KeyboardArrowRight />}
                  </Button>
                }
                backButton={
                  <Button size="small" onClick={handleBack} disabled={activeStep === 0}>
                    {theme.direction === 'rtl' ? <KeyboardArrowRight /> : <KeyboardArrowLeft />}
                    Previous
                  </Button>
                }
              />
            </Box>
          </Box>
        </Grid>
      </Grid>
      <Snackbar open={openSnackbar} snackbarTitle={snackbarTitle} close={() => setOpenSnackbar(false)} />
    </Page>
  );
};

const useStyles = makeStyles((theme) => ({
  gridContainer: {
    // backgroundImage: `url(${NewXlFile})`,
    minHeight: `calc(100vh - ${70}px)`,
    backgroundSize: '100%',
    backgroundRepeat: 'no-repeat',

    display: 'flex',
    padding: 10,
  },
  courseItem: {
    background: '#fff',
    minHeight: `calc(100vh - ${70}px)`,
    borderLeft: '1px solid #BCBCBC',
  },
  progressBox: {
    background: '#FAFAFA',
    borderLeft: 'none',
    border: '1px solid #BCBCBC',
    padding: 10,
  },

  activeKey: {
    boxSizing: 'border-box;',
    // lineHeight: '60px;',
    fontSize: '1.2rem',
    textAlign: 'center;',
    width: '60px',
    minWidth: 'max-content',
    padding: '12px 16px',
    cursor: 'pointer',
    background: '#FEFEFF 0% 0% no-repeat padding-box',
    margin: '0 18px;',
    // height: '40px',
    color: '#090909',
    borderColor: '#f2f2f2;',
    borderStyle: 'solid;',
    textShadow: '0 0.5px 1px #777, 0 2px 6px #f2f2f2;',
    borderWidth: '1px;',
    borderRadius: '6px;',
    // background: '-webkit-linear-gradient(top, #f9f9f9 0%, #D2D2D2 80%, #c0c0c0 100%);',
    fontFamily: 'sans-serif;',
    display: 'inline-block;',
    transition: 'box-shadow 0.3s ease, transform 0.15s ease;',
    // boxShadow: '0 0 15px #888, 0 1px 0 #fff, 0 5px 0 #c0c0c063',
    boxShadow: '1px 1px 3px #a4a4a4',
  },

  buttoneight: {
    animation: 'zoominout 1s infinite;',
  },

  keyWrong: {
    boxShadow: '0 0 12px #e30e0e, 0 1px 0 #fff, 0 5px 0 #c0c0c063',
    animation: 'shake .5s linear',
  },
  keyCorrect: {
    boxShadow: `0 0 12px ${palette.secondary.main}, 0 1px 0 #fff, 0 5px 0 #c0c0c063`,
  },
  introContainer: {
    minWidth: '250px',
    maxWidth: '575px',
    padding: '16px 0 16px',
    borderRadius: 8,
    backgroundColor: '#e7e7e7cf',
    margin: '3rem',
    overflow: 'hidden',
    // boxShadow: `4px 4px 8px 2px #a7a7a7`,
    boxShadow: '0px 2px 6px #00000029',
    // border: '2px solid #ddd',
  },
  introItem: {
    textAlign: 'center',
  },
  introImg: {
    height: 50,
    textAlign: 'center',
  },
  keyName: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
  },
  hideLinearProgress: {
    display: 'none',
  },
  actionDescription: {
    color: palette.common.black,
    fontSize: '1.2rem',
    fontWeight: '500',
  },

  '@global': {
    '@keyframes zoominout': {
      '0%': {
        transform: 'scale(1)',
      },
      '70%': {
        transform: 'scale(0.9)',
      },
      '100%': {
        transform: 'scale(1)',
      },
    },
    '@keyframes shake': {
      '8%, 41%': {
        transform: 'translateX(-10px)',
      },
      '25%, 58%': {
        transform: 'translateX(10px)',
      },
      '75%': {
        transform: 'translateX(-5px)',
      },
      '92%': {
        transform: 'translateX(5px)',
      },
      '0%, 100%': {
        transform: 'translateX(0)',
      },
    },
  },
}));

export default BeginCourse;
