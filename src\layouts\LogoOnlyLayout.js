import { useEffect } from 'react';
import { Outlet, useNavigate, useLocation } from 'react-router-dom';
import { styled } from '@mui/material/styles';
import { useDispatch,useSelector } from 'react-redux';
import ThemeProvider from '../theme';
import {languagecodevalue} from '../store/reducer'


const HeaderStyle = styled('header')(({ theme }) => ({
  top: 0,
  left: 0,
  lineHeight: 0,
  width: '100%',
  position: 'absolute',
  padding: theme.spacing(3, 3, 0),
  [theme.breakpoints.up('sm')]: {
    padding: theme.spacing(5, 5, 0),
  },
}));

// ----------------------------------------------------------------------

export default function LogoOnlyLayout() {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { pathname } = useLocation();
  const isLogin = useSelector((state) => state.isLogin);
  const userRole = useSelector((state) => state.userInfo && state.userInfo.role);

  useEffect(() => {
    if (isLogin && (pathname === '/' || pathname === '/login')) {
      if (isLogin && (userRole === 'SUPER_ADMIN' || userRole === 'CLIENT_ADMIN')) {
        navigate('/app/dashboard');
      } else if (isLogin && (userRole === 'CONTENT_WRITER' || userRole === 'AUTH_USER')) {
         dispatch(languagecodevalue('en'))
        navigate('/app/course');
      } else {
        // navigate('/auth/my-courses');
        navigate('/auth/subscribe');

      }
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <ThemeProvider>
      <HeaderStyle>{/* <Logo /> */}</HeaderStyle>
      <Outlet />
    </ThemeProvider>
  );
}
