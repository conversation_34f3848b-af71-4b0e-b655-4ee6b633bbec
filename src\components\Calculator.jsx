/* eslint-disable no-lonely-if */
/* eslint-disable no-useless-escape */
/* eslint-disable no-eval */
/* eslint-disable no-unused-vars */
import React, { useState } from 'react';
import {
  Box,
  Button,
  Card,
  Grid,
  Paper,
  Typography,
  styled
} from '@mui/material';
import { makeStyles } from '@mui/styles';


 
const Calculator = () => {

  const classes = useStyles();

  const [input, setInput] = useState('');
  const [result, setResult] = useState('0');
  const [history, setHistory] = useState([]);

// working
  // const handleButtonClick = (value) => {
  //   if (['+', '-', '*', '/'].includes(value)) {
  //     const lastChar = input.slice(-1);
      
  //     if (input === '' && value === '-') {
  //       setInput(prev => prev + value);  
  //     } else if (['+', '-', '*', '/'].includes(lastChar)) {
  //       setInput(prev => prev.slice(0, -1) + value);  
  //     } else if (input !== '' && input !== '0') {
  //       setInput(prev => prev + value); 
  //     }
  //   } else if (value === '=') {
  //     try {
       
  //       if (!input.includes("=")) {
  //       const calcResult = eval(input).toString();
  //       setResult(calcResult);
  //       setHistory(prev => [...prev, `${input} = ${calcResult}`]);
  //       setInput(prev => prev + value);  
  //       }
  //       // setInput(calcResult);
  //     } catch (error) {
  //       setResult('Error');
  //     }
  //   } else if (value === 'C') {
  //     setInput('');
  //     setResult('0');
  //   } else if (value === '⌫') {
  //     setInput(prev => prev.slice(0, -1));  
  //   } else if (value === '√') {
  //     try {
  //       const calcResult = Math.sqrt(parseFloat(input)).toString();
  //       setResult(calcResult);
  //       setInput(calcResult);
  //     } catch (error) {
  //       setResult('Error');
  //     }
  //   } else if (value === '%') {
  //     try {
  //       const calcResult = (parseFloat(input) / 100).toString();
  //       setResult(calcResult);
  //       setInput(calcResult);
  //     } catch (error) {
  //       setResult('Error');
  //     }
  //   } else if (value === '.') {
  //     const lastNumber = input.split(/[\+\-\*\/]/).pop();
  //     if (!lastNumber.includes('.')) {
  //       setInput(prev => prev + value); 
  //     }
  //   } else if (value === '0' && input === '0') {
  //     // Prevent adding multiple leading zeros for '0'
  //   } else if (input === result) {
  //     setInput(value); 
  //   } else {
  //     if (input.includes('+') || input.includes('-') || input.includes('*') || input.includes('/')) {
  //       const [lastPart] = input.split(/[\+\-\*\/]/).reverse();
  //       if (lastPart.startsWith('0') && lastPart.length > 1 && !lastPart.includes('.')) {
  //         const cleanedPart = lastPart.replace(/^0+/, ''); 
  //         setInput(prev => {
  //           const beforeLastPart = prev.slice(0, -(lastPart.length));
  //           return beforeLastPart + cleanedPart; 
  //         });
  //       } else {
  //         setInput(prev => prev + value); 
  //       }
  //     } else {
  //       setInput(prev => prev + value);
  //     }
  //   }
  // };
  

  const handleButtonClick = (value) => {
    if (['+', '-', '*', '/'].includes(value)) {
      const lastChar = input.slice(-1);
  
      if (input === '' && value === '-') {
        setInput(prev => prev + value);  
      } else if (['+', '-', '*', '/'].includes(lastChar)) {
        setInput(prev => prev.slice(0, -1) + value);  
      } else if (input !== '' && input !== '0') {
        setInput(prev => prev + value); 
      } else if (input === result) {
        setInput(result + value); // Start new calculation with the result
      }
    } else if (value === '=') {
      try {
        if (!input.includes("=")) {
          const calcResult = eval(input).toString();
          setResult(calcResult);
          setHistory(prev => [...prev, `${input} = ${calcResult}`]);
          setInput(calcResult);  // Set result as input for further calculations
        }
      } catch (error) {
        setResult('Error');
      }
    } else if (value === 'C') {
      setInput('');
      setResult('0');
    } else if (value === '⌫') {
      setInput(prev => prev.slice(0, -1));  
    } else if (value === '√') {
      try {
        const calcResult = Math.sqrt(parseFloat(input)).toString();
        setResult(calcResult);
        setInput(calcResult);
      } catch (error) {
        setResult('Error');
      }
    } else if (value === '%') {
      try {
        const calcResult = (parseFloat(input) / 100).toString();
        setResult(calcResult);
        setInput(calcResult);
      } catch (error) {
        setResult('Error');
      }
    } else if (value === '.') {
      const lastNumber = input.split(/[\+\-\*\/]/).pop();
      if (!lastNumber.includes('.')) {
        setInput(prev => prev + value); 
      }
    } else if (value === '0' && input === '0') {
      // Prevent adding multiple leading zeros for '0'
    } else if (input === result) {
      setInput(value); 
    } else {
      if (input.includes('+') || input.includes('-') || input.includes('*') || input.includes('/')) {
        const [lastPart] = input.split(/[\+\-\*\/]/).reverse();
        if (lastPart.startsWith('0') && lastPart.length > 1 && !lastPart.includes('.')) {
          const cleanedPart = lastPart.replace(/^0+/, ''); 
          setInput(prev => {
            const beforeLastPart = prev.slice(0, -(lastPart.length));
            return beforeLastPart + cleanedPart; 
          });
        } else {
          setInput(prev => prev + value); 
        }
      } else {
        setInput(prev => prev + value);
      }
    }
  };
  

  // recent
//   const handleButtonClick = (value) => {
//   if (['+', '-', '*', '/'].includes(value)) {
//     const lastChar = input.slice(-1);

//     if (result !== '0') {
//       if (input === '' || ['+', '-', '*', '/'].includes(lastChar)) {
//         setInput(prev => prev.slice(0, -1) + value);
//       } else {
//         setInput(prev => prev + value);
//       }
//     } else {
//       // If there's no result yet (or it's cleared), allow adding operators
//       setInput(prev => prev + value);
//     }
//   } else if (value === '=') {
//     try {
//       if (input !== '' && !input.includes("=")) {
//         const calcResult = eval(input).toString();
//         setResult(calcResult);
//         setHistory(prev => [...prev, `${input} = ${calcResult}`]);
//         setInput(calcResult); // Replace input with the result
//       }
//     } catch (error) {
//       setResult('Error');
//     }
//   } else if (value === 'C') {
//     setInput('');
//     setResult('0');
//   } else if (value === '⌫') {
//     setInput(prev => prev.slice(0, -1));
//   } else if (value === '√') {
//     try {
//       const calcResult = Math.sqrt(parseFloat(input)).toString();
//       setResult(calcResult);
//       setInput(calcResult);
//     } catch (error) {
//       setResult('Error');
//     }
//   } else if (value === '%') {
//     try {
//       const calcResult = (parseFloat(input) / 100).toString();
//       setResult(calcResult);
//       setInput(calcResult);
//     } catch (error) {
//       setResult('Error');
//     }
//   } else if (value === '.') {
//     const lastNumber = input.split(/[\+\-\*\/]/).pop();
//     if (!lastNumber.includes('.')) {
//       setInput(prev => prev + value);
//     }
//   } else if (value === '0' && input === '0') {
//     // Prevent adding multiple leading zeros for '0'
//   } else if (input === result) {
//     // If the input is equal to the result, replace it with the new value
//     setInput(value);
//   } else {
//     // Default case: Append the value to the input
//     setInput(prev => prev + value);
//   }
// };

  
  const buttons = [
    'C', '⌫','√', '%',
    '7', '8', '9', '/',
    '4', '5', '6', '*',
    '1', '2', '3', '-',
    '0', '.', '=', '+',
  ];
 
  const CalculatorCard = styled(Card)(({ theme }) => ({
    maxWidth: 360,
    // borderRadius: theme.shape.borderRadius * 2,
    overflow: 'hidden',
   //  boxShadow: theme.shadows[4],
  }));
 
  const DisplayPaper = styled(Paper)(({ theme }) => ({
    padding: theme.spacing(2),
    textAlign: 'right',
    backgroundColor: theme.palette.grey[900],
    color: theme.palette.common.white,
    borderRadius: 0,
  }));
 
  const CalcButton = styled(Button)(({ theme, value }) => ({
    minWidth: 'unset',
    height: 64,
    fontSize: '1.25rem',
    borderRadius: 0,
    // eslint-disable-next-line no-nested-ternary
    backgroundColor: value === '=' ? '#adc6ff' :
                    ['C', '⌫', '√', '%','+', '-', '/', '*'].includes(value) ? '#e5edff' : '#f1f1f1',
    color: value === '=' ? theme.palette.common.white : theme.palette.text.primary,
    marginRight: value ===  ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '.'].includes(value) ? '10px' : '0px',
    '&:hover': {
      // eslint-disable-next-line no-nested-ternary
      backgroundColor: value === '=' ? '#96b4f7' :
                       ['C', '⌫', '√', '%','+', '-', '/', '*'].includes(value) ? '#d2daff' :
                       '#e5e3e3',
    }
  }));
 
  return (
    <Box sx={{
      display: 'flex',
      flexDirection: { xs: 'column', md: 'row' },
      justifyContent: 'center',
      alignItems: 'flex-start',
      p: 2,
      maxWidth: 800,
      mx: 'auto'
    }}>
      <CalculatorCard sx={{boxShadow: 'none', borderRadius: '0px'}}>
        <DisplayPaper className={classes.displayPaper} elevation={0}>
          <Typography variant="body1" sx={{ opacity: 0.8, minHeight: 24 }}>
            {input || '0'}
          </Typography>
          <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
            {result}
          </Typography>
        </DisplayPaper>
        {/* <Divider /> */}
        <Grid container spacing={0} style={{justifyContent: 'center'}}>
          {buttons.map((btn, index) => (
            <Grid item xs={3} key={btn}  className={`${classes.commonBtn}  ${classes.CalcBtns}`}>
              <CalcButton
                fullWidth
                className={classes.btnDesign}
                onClick={() => handleButtonClick(btn)}
                value={btn}
                variant="contained"
                disableElevation
              >
                {btn}
              </CalcButton>
            </Grid>
          ))}
        </Grid>
      </CalculatorCard>
    </Box>
  );
};
 
export default Calculator;

const useStyles = makeStyles((theme) => ({
    commonBtn: {
         height: '45px !important',
        fontSize: '17px',
          width: '70px',
          borderRadius: '24px',
         padding: '0px',
         margin: '0px',
          overflow: 'hidden',
          marginBottom: '10px',
          
          display: 'flex',
          justifyContent: 'center',
          verticalAlign: 'center',
       
 
        '&:hover':{
        boxShadow: 'rgba(228, 60, 60, 0.16) 0px 1px 4px'
        },
  
        '&:nth-child(4n)': {
         marginRight: '0px',   
        },
       
    },
    btnDesign:{
           padding: '0px !important',
            height: '45px !important',
            width: '100%',
            overflow: 'hidden',
    },
    displayPaper:{
        background: '#fff',
        color: "#000",
        boxShadow: 'none',
        width: '96%',
        margin: 'auto',
        borderRadius: '10px',
        marginBottom: '15px',
        border: '1.5px solid #cfcdcd',
    },
    CalcBtns:{
        // color: '#f00 !important',
        // background: 'green',
        marginRight: '15px',
        maxWidth: '75px',
    },
    btnVertical: {
        height: '50px !important',
    }
 
}));