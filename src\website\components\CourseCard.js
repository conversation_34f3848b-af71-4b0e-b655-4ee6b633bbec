import * as React from 'react';
import { styled } from '@mui/material/styles';
import Card from '@mui/material/Card';
import CardHeader from '@mui/material/CardHeader';
import { makeStyles } from '@mui/styles';
import CardContent from '@mui/material/CardContent';
import Avatar from '@mui/material/Avatar';
import IconButton, { IconButtonProps } from '@mui/material/IconButton';
import { Typography, Box, Link } from '@mui/material';
import { useNavigate, Link as RouterLink } from 'react-router-dom';
import ArrowForwardIcon from '@mui/icons-material/ArrowForward';

export default function CourseCard(props) {
  const [expanded, setExpanded] = React.useState(false);
  const classes = useStyles();
  const navigate = useNavigate();

  const handleExpandClick = () => {
    setExpanded(!expanded);
  };
  let theme;
  return (
    <Card
      sx={{
        // width: '150px',
        display: 'flex',
        flexDirection: 'column',
        // display: 'flex',
        // height: 'auto',

        minHeight: '450px',
        // [theme.breakpoints.down('sm')]: {
        //   height: '620px !important',
        // },
        margin: '6px 15px',
        background: 'rgba(81, 179, 121, 0.11)',
        borderRadius: '10px',
      }}
    >
      <Box sx={{ marginBottom: '1rem' }}>
        <CardHeader
          avatar={
            <img
              src={props.image}
              alt="icon"
              width={props.title === 'Microsoft Word' ? '100%' : '50px'}
              height="50px"
            />
          }
        />
      </Box>

      <CardContent>
        <Typography variant="subtitle1" gutterBottom>
          {props.title}
        </Typography>
        <Box component="div" className={classes.contented}>
          <Typography variant="body1" gutterBottom>
            {props.content}
          </Typography>
        </Box>

        <div style={{ display: 'flex', alignItems: 'end', color: '#EC7930', marginTop: '1rem' }}>
          <Link
            // href={props.link}
            onClick={(e) => navigate(props.link)}
            sx={{ display: 'contents', cursor: 'pointer' }}
            underline="none"
          >
            <Typography variant="body1" className={classes.arrowMove}>
              {props.subTitle}
            </Typography>
            &nbsp;&nbsp; <ArrowForwardIcon sx={{ width: '20px', height: '22px' }} />
          </Link>
        </div>
      </CardContent>
    </Card>
  );
}

const useStyles = makeStyles((theme) => ({
  arrowMove: {
    // maxWidth: '63%',
    // animation: 'a2 5.5s alternate ease-in-out backwards',
    // animationDirection: 'reverse',
    '&:hover': {
      animation: 'a1 0.5s alternate ease-in-out forwards ',
      // alternate ease-in-out forwards
    },
    '&:after': {
      // animation: 'a1 0.5s alternate ease-in-out',
      transition: 'all 5.1s ease 0s; ',
      webkitTransition: 'all 0.3s ease 0s',
      zIndex: -1,
      // alternate ease-in-out forwards
    },
  },

  '@global': {
    '@keyframes a1': {
      '90%,100%': { flexGrow: 0.18 },
    },
  },

  contented: {
    display: '-webkit-box',
    boxOrient: 'vertical',
    lineClamp: 10,
    overflow: 'hidden',
    minHeight: '246px',
  },
}));
