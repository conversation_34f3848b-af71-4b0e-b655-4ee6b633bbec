<svg id="Group_1538" data-name="Group 1538" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="139.491" height="114" viewBox="0 0 139.491 114">
  <defs>
    <clipPath id="clip-path">
      <rect id="Rectangle_1589" data-name="Rectangle 1589" width="139.491" height="114" fill="none"/>
    </clipPath>
    <clipPath id="clip-path-3">
      <rect id="Rectangle_1582" data-name="Rectangle 1582" width="106.4" height="106.4" fill="none"/>
    </clipPath>
    <clipPath id="clip-path-4">
      <path id="Path_1095" data-name="Path 1095" d="M106.4,66.019a53.2,53.2,0,1,1-53.2-53.2,53.2,53.2,0,0,1,53.2,53.2" transform="translate(0 -12.819)" fill="none"/>
    </clipPath>
    <radialGradient id="radial-gradient" cx="0.5" cy="0.5" r="0.5" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#f13900"/>
      <stop offset="0.488" stop-color="#f13900"/>
      <stop offset="0.567" stop-color="#f4683c"/>
      <stop offset="0.653" stop-color="#f79577"/>
      <stop offset="0.737" stop-color="#fabba7"/>
      <stop offset="0.815" stop-color="#fcd8cd"/>
      <stop offset="0.888" stop-color="#fdede8"/>
      <stop offset="0.951" stop-color="#fefaf9"/>
      <stop offset="1" stop-color="#fff"/>
    </radialGradient>
  </defs>
  <g id="Group_1537" data-name="Group 1537" clip-path="url(#clip-path)">
    <g id="Group_1534" data-name="Group 1534">
      <g id="Group_1533" data-name="Group 1533" clip-path="url(#clip-path)">
        <g id="Group_1532" data-name="Group 1532" transform="translate(0 7.6)" style="mix-blend-mode: multiply;isolation: isolate">
          <g id="Group_1531" data-name="Group 1531">
            <g id="Group_1530" data-name="Group 1530" clip-path="url(#clip-path-3)">
              <g id="Group_1529" data-name="Group 1529">
                <g id="Group_1528" data-name="Group 1528" clip-path="url(#clip-path-4)">
                  <rect id="Rectangle_1581" data-name="Rectangle 1581" width="106.4" height="106.4" fill="url(#radial-gradient)"/>
                </g>
              </g>
            </g>
          </g>
        </g>
      </g>
    </g>
    <rect id="Rectangle_1584" data-name="Rectangle 1584" width="71.606" height="45.125" transform="translate(67.885 29.213)" fill="#00323e"/>
    <g id="Group_1536" data-name="Group 1536">
      <g id="Group_1535" data-name="Group 1535" clip-path="url(#clip-path)">
        <path id="Path_1096" data-name="Path 1096" d="M102.171,64.038a38,38,0,1,1-38-38,38,38,0,0,1,38,38" transform="translate(-10.654 -10.6)" fill="#fff"/>
        <path id="Path_1097" data-name="Path 1097" d="M61.869,103.088a41.351,41.351,0,1,1,41.352-41.351,41.4,41.4,0,0,1-41.352,41.351m0-76A34.649,34.649,0,1,0,96.518,61.736,34.688,34.688,0,0,0,61.869,27.088" transform="translate(-8.353 -8.299)" fill="#f13900"/>
        <rect id="Rectangle_1585" data-name="Rectangle 1585" width="8.154" height="10.133" transform="translate(49.44 5.146)" fill="#f13900"/>
        <path id="Path_1098" data-name="Path 1098" d="M90.754,7.6H80.5a1.623,1.623,0,0,1-1.623-1.623V1.624A1.623,1.623,0,0,1,80.5,0H90.754a1.622,1.622,0,0,1,1.623,1.623V5.978A1.622,1.622,0,0,1,90.754,7.6" transform="translate(-32.112 0)" fill="#f13900"/>
        <rect id="Rectangle_1586" data-name="Rectangle 1586" width="6.523" height="8.107" transform="matrix(0.707, -0.707, 0.707, 0.707, 16.665, 24.445)" fill="#f13900"/>
        <path id="Path_1099" data-name="Path 1099" d="M32.035,31.577l-5.8,5.8a1.3,1.3,0,0,1-1.836,0l-2.463-2.463a1.3,1.3,0,0,1,0-1.836l5.8-5.8a1.3,1.3,0,0,1,1.836,0l2.463,2.463a1.3,1.3,0,0,1,0,1.836" transform="translate(-8.775 -10.95)" fill="#f13900"/>
        <rect id="Rectangle_1587" data-name="Rectangle 1587" width="8.107" height="6.523" transform="translate(79.874 25.564) rotate(-45)" fill="#f13900"/>
        <path id="Path_1100" data-name="Path 1100" d="M146.582,37.377l-5.8-5.8a1.3,1.3,0,0,1,0-1.836l2.463-2.464a1.3,1.3,0,0,1,1.836,0l5.8,5.8a1.3,1.3,0,0,1,0,1.836l-2.463,2.463a1.3,1.3,0,0,1-1.836,0" transform="translate(-57.158 -10.95)" fill="#f13900"/>
        <path id="Path_1101" data-name="Path 1101" d="M85.333,62.494H74.666V56.772H92.655v4.9L84.363,96.046H77.091Z" transform="translate(-30.397 -23.112)" fill="#f13900"/>
        <path id="Path_1102" data-name="Path 1102" d="M68.587,100.036h-.03q-.47,0-.938-.014l.2-6.7q.369.011.742.011Zm14.955-3.771-3.189-5.9q.341-.184.674-.377l3.356,5.8q-.417.241-.841.47m-30.756-.438q-.425-.245-.841-.5l3.533-5.7c.216.134.434.264.655.391ZM95.442,85.06l-5.7-3.533q.2-.326.4-.66l5.8,3.358q-.244.422-.5.835M41.2,84.28q-.245-.42-.476-.848l5.9-3.185q.181.337.374.666Zm58.953-14.889-6.7-.2c.007-.256.011-.512.011-.77V68.3h6.7v.088c0,.358-.005.681-.015,1m-62.16-.918-1.052,0c0-.35,0-.652.012-.953l6.7.194q-.01.355-.01.712ZM90.464,56.553q-.183-.338-.376-.669l5.788-3.378q.244.417.476.844ZM46.946,56.02l-5.812-3.34q.245-.426.5-.843l5.7,3.529c-.133.215-.263.434-.39.654m34.649-8.829q-.329-.2-.663-.394l3.34-5.812c.28.162.559.327.835.5Zm-25.553-.314-3.374-5.791q.421-.245.85-.477l3.189,5.9c-.224.121-.445.246-.665.374m13.2-3.356c-.229-.006-.458-.009-.688-.009h-.091v-6.7h.091q.436,0,.869.012Z" transform="translate(-15.039 -14.985)" fill="#00323e"/>
        <path id="Path_1103" data-name="Path 1103" d="M166.268,62.918h3.147a6.2,6.2,0,0,1,2.535.421,2.531,2.531,0,0,1,1.3,1.3,5.788,5.788,0,0,1,.39,2.321v4.689a6.178,6.178,0,0,1-.381,2.376,2.554,2.554,0,0,1-1.288,1.335,5.845,5.845,0,0,1-2.495.437h-3.211Zm3.179,11.046a1.87,1.87,0,0,0,1.009-.215.953.953,0,0,0,.413-.628,6.144,6.144,0,0,0,.088-1.192V66.605a4.432,4.432,0,0,0-.1-1.1.885.885,0,0,0-.421-.556,2.094,2.094,0,0,0-1-.183h-.557v9.2Z" transform="translate(-67.688 -25.614)" fill="#fff"/>
        <path id="Path_1104" data-name="Path 1104" d="M184.687,62.918H187.6l2.48,12.874h-2.416l-.477-2.829h-2.035l-.493,2.829h-2.463Zm2.241,8.424-.778-5.181-.764,5.181Z" transform="translate(-74.17 -25.614)" fill="#fff"/>
        <path id="Path_1105" data-name="Path 1105" d="M199.715,70.69,197.2,62.918h2.464l1.383,4.53,1.271-4.53h2.4l-2.479,7.772v5.1h-2.528Z" transform="translate(-80.282 -25.614)" fill="#fff"/>
        <path id="Path_1106" data-name="Path 1106" d="M213.908,74.88a4.672,4.672,0,0,1-.97-3.027l2.258-.382a4.22,4.22,0,0,0,.342,1.8,1,1,0,0,0,.93.58.759.759,0,0,0,.668-.294,1.283,1.283,0,0,0,.206-.755,2.345,2.345,0,0,0-.342-1.3,5.757,5.757,0,0,0-1.073-1.168l-1.335-1.16a5.121,5.121,0,0,1-1.215-1.431,3.725,3.725,0,0,1-.421-1.828,3.118,3.118,0,0,1,.93-2.424,3.66,3.66,0,0,1,2.567-.85,2.726,2.726,0,0,1,2.432,1.009,5.054,5.054,0,0,1,.778,2.567l-2.273.334a4.617,4.617,0,0,0-.231-1.5.768.768,0,0,0-.787-.484.78.78,0,0,0-.668.326,1.271,1.271,0,0,0-.238.771,1.954,1.954,0,0,0,.279,1.073,4.619,4.619,0,0,0,.882.961l1.3,1.145a7,7,0,0,1,1.447,1.709,4.1,4.1,0,0,1,.525,2.106,3.254,3.254,0,0,1-.414,1.645,2.943,2.943,0,0,1-1.167,1.137,3.569,3.569,0,0,1-1.741.413,3.434,3.434,0,0,1-2.67-.978" transform="translate(-86.687 -25.505)" fill="#fff"/>
        <path id="Path_1107" data-name="Path 1107" d="M166.268,90.207h2.606V101.3h3.036v1.78h-5.643Z" transform="translate(-67.688 -36.723)" fill="#fff"/>
        <path id="Path_1108" data-name="Path 1108" d="M179.752,90.207h5.611v1.812h-3v3.417h2.321v1.828h-2.321V101.3h3.037v1.78h-5.643Z" transform="translate(-73.177 -36.723)" fill="#fff"/>
        <path id="Path_1109" data-name="Path 1109" d="M193.4,90.207h5.436V92H196v3.466h2.4V97.3H196v5.785H193.4Z" transform="translate(-78.732 -36.723)" fill="#fff"/>
        <path id="Path_1110" data-name="Path 1110" d="M207.339,92.146h-1.987V90.207h6.581v1.939h-1.955v10.935h-2.638Z" transform="translate(-83.598 -36.723)" fill="#fff"/>
        <path id="Path_1111" data-name="Path 1111" d="M126.176,16.489a1.267,1.267,0,1,1-1.266-1.266,1.266,1.266,0,0,1,1.266,1.266" transform="translate(-50.335 -6.197)" fill="#f13900"/>
        <path id="Path_1112" data-name="Path 1112" d="M11.387,111.311a1.821,1.821,0,1,1-1.821-1.821,1.821,1.821,0,0,1,1.821,1.821" transform="translate(-3.153 -44.573)" fill="#f13900"/>
        <path id="Path_1113" data-name="Path 1113" d="M33.168,149.708a4.433,4.433,0,1,1-4.433-4.433,4.433,4.433,0,0,1,4.433,4.433" transform="translate(-9.893 -59.141)" fill="#f13900"/>
        <path id="Path_1114" data-name="Path 1114" d="M149.064,139.826a2.3,2.3,0,1,1-2.3-2.3,2.3,2.3,0,0,1,2.3,2.3" transform="translate(-58.815 -55.988)" fill="#f13900"/>
        <path id="Path_1115" data-name="Path 1115" d="M46.627,8.359A2.217,2.217,0,1,1,44.41,6.142a2.217,2.217,0,0,1,2.217,2.217" transform="translate(-17.177 -2.5)" fill="#f13900"/>
        <path id="Path_1116" data-name="Path 1116" d="M11.358,60.424a3.008,3.008,0,1,1-3.008-3.008,3.009,3.009,0,0,1,3.008,3.008" transform="translate(-2.174 -23.374)" fill="#00323e"/>
        <path id="Path_1117" data-name="Path 1117" d="M120.344,165.256a3.958,3.958,0,1,1-3.958-3.958,3.958,3.958,0,0,1,3.958,3.958" transform="translate(-45.769 -65.664)" fill="#00323e"/>
        <path id="Path_1118" data-name="Path 1118" d="M165.927,131.834a.712.712,0,1,1-.713-.713.713.713,0,0,1,.713.713" transform="translate(-66.969 -53.379)" fill="#00323e"/>
        <path id="Path_1119" data-name="Path 1119" d="M161.656,17.805a2.85,2.85,0,1,1-2.85-2.85,2.85,2.85,0,0,1,2.85,2.85" transform="translate(-63.49 -6.088)" fill="#00323e"/>
        <path id="Path_1120" data-name="Path 1120" d="M62.61,164.562a1.662,1.662,0,1,1-1.662-1.662,1.662,1.662,0,0,1,1.662,1.662" transform="translate(-24.135 -66.316)" fill="#00323e"/>
      </g>
    </g>
  </g>
</svg>
