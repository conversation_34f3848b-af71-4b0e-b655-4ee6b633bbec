import React from 'react';
import { Container, <PERSON>rid, Typo<PERSON>, But<PERSON>, Divider, Box, TextField } from '@mui/material';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import Switch from '@mui/material/Switch';
import IconButton from '@mui/material/IconButton';

import { makeStyles } from '@mui/styles';
import MUIDataTable from 'mui-datatables';

const columns = [
  {
    name: 'name',
    label: 'Client ID',
    options: {
      filter: true,
      sort: true,
    },
  },
  {
    name: 'company',
    label: 'Name',
    options: {
      filter: true,
      sort: false,
    },
  },
  {
    name: 'city',
    label: 'Email',
    options: {
      filter: true,
      sort: false,
    },
  },
  {
    name: 'state',
    label: 'Admin',
    options: {
      filter: true,
      sort: false,
    },
  },
  {
    name: 'state',
    label: 'Status',
    options: {
      filter: true,
      sort: false,
      customBodyRender: (value, tableMeta, updateValue) => {
        return (
          <>
            <Switch color="secondary" {...label} />
          </>
        );
      },
    },
  },
  {
    name: 'state',
    label: 'Action',
    options: {
      filter: true,
      sort: false,
      customBodyRender: (value, tableMeta, updateValue) => {
        return (
          <>
            <div
              style={{
                display: 'flex',
                justifyContent: 'flex-start',
                alignItems: 'center',
              }}
            >
              <div style={{ backgroundColor: '#747474', borderRadius: '4px', padding: '3px 6px ', marginRight: '7px' }}>
                <Typography style={{ color: 'white' }}>2 subscription</Typography>
              </div>

              <IconButton>
                <EditIcon fontSize="small" style={{ color: '#BCBCBC', marginLeft: '2px' }} />
              </IconButton>

              <IconButton>
                <DeleteIcon fontSize="small" style={{ color: '#BCBCBC' }} />
              </IconButton>
            </div>
          </>
        );
      },
    },
  },
];

const data = [
  { name: 'Joe James', company: 'Test Corp', city: 'Yonkers', state: 'NY' },
  { name: 'John Walsh', company: 'Test Corp', city: 'Hartford', state: 'CT' },
  { name: 'Bob Herm', company: 'Test Corp', city: 'Tampa', state: 'FL' },
  { name: 'James Houston', company: 'Test Corp', city: 'Dallas', state: 'TX' },
];

const options = {
  filterType: 'checkbox',
};

const label = { inputProps: { 'aria-label': 'Switch demo' } };

export default function Client() {
  const classes = useStyles();
  return (
    <div style={{ marginTop: '1rem' }}>
      <MUIDataTable
        data={data}
        columns={columns}
        options={{
          responsive: "scroll",
          rowsPerPage: 25,
          rowsPerPageOptions: [25, 50, 75, 100],
          selectableRows: false,
          filter: false,
          download: false,
          print: false,
        }}
      />
    </div>
  );
}
const useStyles = makeStyles((theme) => ({
  subscription: {},
}));
