/* eslint-disable prefer-destructuring */
/* eslint-disable consistent-return */
/* eslint-disable arrow-body-style */
/* eslint-disable react/button-has-type */
/* eslint-disable react/no-danger */
/* eslint-disable no-unused-vars */
/* eslint-disable react/prop-types */
import React, { useState, useEffect } from "react";
import { Grid, Button, TextField, Paper, ToggleButtonGroup, ToggleButton, MenuItem, Select, Box, Typography, ButtonGroup } from "@mui/material";
import { Rnd } from "react-rnd";
import { ArrowBack, ArrowForward, ArrowUpward, ArrowDownward } from "@mui/icons-material";
import CloseIcon from '@mui/icons-material/Close';
import Latex from "react-latex-next";
import "katex/dist/katex.min.css";
import TextareaAutosize from '@mui/material/TextareaAutosize';
import TitleIcon from '@mui/icons-material/Title';
import ImageIcon from '@mui/icons-material/Image';
import FormatUnderlinedIcon from '@mui/icons-material/FormatUnderlined';
import SubscriptIcon from '@mui/icons-material/Subscript';
import SuperscriptIcon from '@mui/icons-material/Superscript';
import InterestsIcon from '@mui/icons-material/Interests';
import FunctionsTwoToneIcon from '@mui/icons-material/FunctionsTwoTone';
import NoneIcon from '@mui/icons-material/Block';
import BounceIcon from '@mui/icons-material/Waves';
import SlideIcon from '@mui/icons-material/Swipe';
import DelayIcon from '@mui/icons-material/AccessTime';
import HexagonIcon from '@mui/icons-material/Hexagon';
import ChangeHistoryIcon from '@mui/icons-material/ChangeHistory';
import Crop32Icon from '@mui/icons-material/Crop32';
import Brightness1OutlinedIcon from '@mui/icons-material/Brightness1Outlined';
import ArrowForwardOutlinedIcon from '@mui/icons-material/ArrowForwardOutlined';
import DeleteOutlineIcon from '@mui/icons-material/DeleteOutline';
import CloudUploadTwoToneIcon from '@mui/icons-material/CloudUploadTwoTone';
import adminServices from '../services/adminServices';

const CustomTextEditor = ({ onSubmit, onButtonClick, apiResponse }) => {

  const [elements, setElements] = useState([]);
  console.log(elements,"elements");
  console.log(apiResponse,"apiResponse");

  const [selectedElement, setSelectedElement] = useState(null);
  const [selectedText, setSelectedText] = useState('');
  const [shapeSelected, setShapeSelected] = useState(false);
  const [animationType, setAnimationType] = useState('');
  const [toggleSection, setToggle] = useState('');
  const [selectedType, setSelectedType] = useState(null);
  const [images, setImages] = useState([]);
  const [imageUrls, setImageUrls] = useState([]);
  const [imageUrlsLoading, setImageUrlsLoading] = useState(false);
  const [previewMode, setPreviewMode] = useState(false);
  const [visibleDeleteId, setVisibleDeleteId] = useState(null);
  const [elementError, setElementError] = useState('');
  const [backgroundImage, setBackgroundImage] = useState(null);
  const [animationTypeName, setAnimationTypeName] = useState('');
  const [selectedRange, setSelectedRange] = useState({ start: 0, end: 0 });
  const [backgroundImageSettings, setBackgroundImageSettings] = useState({
    url: '',
    size: 'cover',
    position: 'center',
    repeat: 'no-repeat'
  });

  const [containerDimensions, setContainerDimensions] = useState({
    width: 785,
    height: 450
  });

  useEffect(() => {
    setElements(apiResponse?.elements || []);
    setBackgroundImageSettings(apiResponse?.backgroundImageSettings);
    setAnimationTypeName(apiResponse?.animationTypeName)
  }, [apiResponse]);

  useEffect(() => {
    const updateDimensions = () => {
      const container = document.querySelector('.editor-container');
      if (container) {
        // setContainerDimensions({
        //   width: container.clientWidth,
        //   height: container.clientHeight
        // });
      }
    };

    updateDimensions();
    window.addEventListener('resize', updateDimensions);

    return () => window.removeEventListener('resize', updateDimensions);
  }, []);

  const handleAnimationSelect = (type) => {
    setAnimationType(type);

    document.body.classList.remove('rightToLeft', 'leftToRight', 'popUp', 'delayEffect');

    document.body.classList.add(type);
  };

  const toggleTextFormatting = (type) => {
    const element = elements.find(el => el.id === selectedElement);
    if (!element || selectedRange.start === selectedRange.end) return;
    setSelectedRange({ start: 0, end: 0 });
    const { content } = element;
    const { start, end } = selectedRange;
    let selectedText = content.slice(start, end);
    let newText = content;
    const tagMap = {
      underline: { open: "<u>", close: "</u>", regex: /^<u>(.*?)<\/u>$/ },
      superscript: { open: "<sup>", close: "</sup>", regex: /^<sup>(.*?)<\/sup>$/ },
      subscript: { open: "<sub>", close: "</sub>", regex: /^<sub>(.*?)<\/sub>$/ },
    };
    const { open, close, regex } = tagMap[type];
    if (regex.test(selectedText)) {
      selectedText = selectedText.replace(regex, "$1");
    } else {
      selectedText = selectedText.replace(/<\/?u>|<\/?sup>|<\/?sub>/g, "");
      selectedText = `${open}${selectedText}${close}`;
    }
    newText = content.slice(0, start) + selectedText + content.slice(end);
    updateElement(selectedElement, { content: newText });
  };


  const handleAnimationClick = (type) => {
    document.body.classList.remove('rightToLeft', 'leftToRight', 'popUp', 'delayEffect');

    let animationClass;

    switch (type) {

      case 'Delay':
        animationClass = 'delay';
        break;
      case 'Bounce':
        animationClass = 'popup';
        break;
      case 'SlideIn':
        animationClass = 'slideIn';
        break;
      default:
        animationClass = 'none';
    }

    setAnimationTypeName(animationClass);
    document.body.classList.add(animationClass);
  };

  useEffect(() => {
    const selectedItem = elements.find((item) => item.id === selectedElement);
    setSelectedType(selectedItem || null);
  }, [selectedElement, elements]);

  const addTextElement = () => {
    setElementError('');
    setElements([
      ...elements,
      {
        id: Date.now(),
        type: "text",
        content: "New Text",
        x: 100,
        y: 100,
        width: 200,
        height: 50,
        fontWeight: "normal",
        fontStyle: "normal",
        textAlign: "left",
        padding: "5px",
        fontSize: 16,
        color: "#000000",
        backgroundColor: "transparent",
        backgroundImage: "",
        backgroundSize: "cover",
        backgroundPosition: "center",
        animation: "none",
        animationDelay: "0.5s",
        underline: false,
        superscript: false,
        subscript: false,
      },
    ]);
  };
  const addFormulaElement = () => {
    setElementError('');
    setElements([
      ...elements,
      {
        id: Date.now(),
        type: "formula",
        content: "a + b = c",
        x: 100,
        y: 100,
        width: 200,
        height: 50,
        fontWeight: "normal",
        fontStyle: "normal",
        textAlign: "center",
        padding: "5px",
        fontSize: 16,
        color: "#000000",
        backgroundColor: "transparent",
        backgroundImage: "",
        backgroundSize: "cover",
        backgroundPosition: "center",
        animation: "none",
        animationDelay: "0.5s",
      },
    ]);
  };

   const handleBackgroundImageDelete = () =>{
     setBackgroundImageSettings(prev => ({
            ...prev,
            url: ''
          }));
   }
  const handleBackgroundImageUpload = async (event) => {
    const file = event.target.files[0];
    if (file) {
      const formData = new FormData();
      formData.append('thumbImage', file);

      try {
        setImageUrlsLoading(true);
        const imageUrl = await adminServices.postImag(formData);
        if (imageUrl.ok) {
          setBackgroundImageSettings(prev => ({
            ...prev,
            url: imageUrl.data
          }));
        }
        setImageUrlsLoading(false);
      } catch (error) {
        console.error('Error uploading background image:', error);
      }
    }
  };

  const addImageElement = () => {
    setElementError('');
    setElements([
      ...elements,
      {
        id: Date.now(),
        type: "image",
        src: "https://via.placeholder.com/150",
        x: 150,
        y: 150,
        width: 150,
        height: 150,
        backgroundColor: 'transparent',
        borderRadius: "5px",
        animation: "none",
        animationDelay: "0.5s",
      },
    ]);
  };


  const addShapeElement = (shapeType) => {
    setElementError('');
    setElements([
      ...elements,
      {
        id: Date.now(),
        type: "shape",
        shapeType,
        x: 100,
        y: 100,
        width: 100,
        height: 100,
        stroke: "#000000",
        strokeWidth: 2,
        fill: '#000000',
        lineStyle: "solid",
        arrowHead: shapeType === "arrow" ? "end" : "none",
        direction: "right",
        boxShadow: "2px 2px 5px rgba(231, 9, 9, 0.3)",
        text: "",
        textColor: "#FFF",
        fontSize: "18px",
        formula: "",
        formulaColor: "#FFF",
        animation: "none",
        animationDelay: "0.5s",
      },
    ]);
  };

  const updateElement = (id, newProps) => {
    setElements((prevElements) =>
      prevElements.map((el) => (el.id === id ? { ...el, ...newProps } : el))
    );
  };

  const handleFileChange = (event) => {
    setImageUrlsLoading(true)
    const files = event.target.files;
    const fileArray = Array.from(files).map((file) => URL.createObjectURL(file));
    setImages((prevImages) => [...prevImages, ...fileArray]);
    handleUploadImage(files[0]);
  };


  const handleUploadImage = async (file) => {
    const formData = new FormData();
    formData.append('thumbImage', file);

    try {
      const imageUrl = await adminServices.postImag(formData);
      if (imageUrl.ok) {
        setImageUrls((prevUrls) => [...prevUrls, imageUrl.data]);
        if (selectedElement) {
          updateElement(selectedElement, { src: imageUrl.data });
        }
      }
      setImageUrlsLoading(false);
    } catch (error) {
      console.error('Error uploading image:', error);
    }
  };

  const deleteElement = (id) => {
    setElements(elements.filter(el => el.id !== id));
    setSelectedElement(null);
  }

  const validateElement = () => {
    if (elements.length === 0) {
      setElementError('Please select at least one  Element  to Export  ')
      return false
    }
    if (elements.length !== 0) {
      onButtonClick()
      setElementError('')
      return true
    }

  }



  const exportToHTML = () => {
    const validate = validateElement()
    if (validate) {
      const transformedElements = elements.map((el) => {
        const containerWidth = 756;
        const containerHeight = 480;
        const currentWidth = document.querySelector('.editor-container')?.clientWidth || window.innerWidth;
        const scaleX = containerWidth / currentWidth;
        const scaleY = containerHeight / 450;
        const newX = el.x * scaleX;
        const newY = el.y * scaleY;
        const newWidth = el.width * scaleX;
        const newHeight = el.height * scaleY;
        return {
          ...el,
          x: newX,
          y: newY,
          width: newWidth,
          height: newHeight,
          content: el.type === "formula" ? `$$${el.content}$$` : el.content
        };
      });
           // min-width: 800px; for bodyStyle
      const bodyStyle = `
      margin: 0;
      padding: 0;
      width: 100%;
 
      height: 80vh;     
      display: block;
      overflow: hidden;
      position: relative;
      background-image: ${backgroundImageSettings?.url ? `url(${backgroundImageSettings?.url})` : 'none'};
      background-size: 100% 80vh;
      background-position: ${backgroundImageSettings?.position || 'center'};
      background-repeat: ${backgroundImageSettings?.repeat || 'no-repeat'};
    `;

      let htmlContent = `
      <html lang="en">
      <head>
  
        <style>
          .editor-element {
            opacity: 0;
            transform: translateY(10px);
            animation-fill-mode: forwards;
            animation-duration: 1s;
            position: absolute;
            z-index: 999;
            transform-origin: center;
            transition: transform 0.3s ease;
    
          }

          svg.editor-element {
  opacity: 0;
  animation: showArrow 0.5s ease-in forwards;
  animation-delay: 10s; /* 10 second delay */
}


@keyframes showArrow {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

svg.editor-element marker {
  opacity: 1;
}

svg.editor-element line {
  opacity: 1;
}
            
          .no-animation {
            opacity: 1 !important;
            transform: none !important;
          }



          * {
            box-sizing: border-box;
     
          }
       
          .editor-container {
              position: relative;
              height: 600px;
              border: 1px solid #ccc;
              overflow: hidden; 
          }
          .popup {
          animation: popup 0.8s ease-out;
          animation-fill-mode: forwards;
        }

          
          [class*=fadein] {
            animation: fadeIn 1.5s ease-in;
          }
          [class*=slideIn] {
            animation: slideIn 1s ease-out;
          }
          .delay [class*=editor-element] {
    opacity: 0;
    transform: translateY(10px);
    animation: slideIn 0.5s ease forwards;
  }

   [class*=delay] [class*=editor-element]:nth-child(1) {
      animation-delay: 0s;
    }
  
    [class*=delay] [class*=editor-element]:nth-child(2) {
      animation-delay: 0.5s;
    }
  
    [class*=delay] [class*=editor-element]:nth-child(3) {
      animation-delay: 0.10s;
    }
  
    [class*=delay] [class*=editor-element]:nth-child(4) {
      animation-delay: 0.15s;
    }
  
    [class*=delay] [class*=editor-element]:nth-child(5) {
      animation-delay: 0.20s;
    }
      
    [class*=delay] [class*=editor-element]:nth-child(6) {
      animation-delay: 0.25s;
    }
  
    [class*=delay] [class*=editor-element]:nth-child(7) {
      animation-delay: 0.30s;
    }
      
    [class*=delay] [class*=editor-element]:nth-child(8) {
      animation-delay: 0.35s;
    }
    
        [class*=delay] [class*=editor-element]:nth-child(9) {
      animation-delay: 0.40s;
    }
  
    [class*=delay] [class*=editor-element]:nth-child(10) {
      animation-delay: 0.45s;
    }
      
    [class*=delay] [class*=editor-element]:nth-child(11) {
      animation-delay: 0.50s;
    }
  
    [class*=delay] [class*=editor-element]:nth-child(12) {
      animation-delay: 0.55s;
    }
      
    [class*=delay] [class*=editor-element]:nth-child(13) {
      animation-delay: 0.60s;
    }
          [class*=delay] [class*=editor-element]:nth-child(14) {
        animation-delay: 1.05s;
    }
  
    [class*=delay] [class*=editor-element]:nth-child(15) {
animation-delay: 1.15s;
    }
      
    [class*=delay] [class*=editor-element]:nth-child(16) {
    animation-delay: 1.25s;
    }
  
    [class*=delay] [class*=editor-element]:nth-child(17) {
    animation-delay: 1.35s;
    }
      
    [class*=delay] [class*=editor-element]:nth-child(18) {
  animation-delay: 1.45s;
    }

    #sidebarEditor  button {
          border-radius: 6px;
    }
          .slideLeft {
  animation: 3s slide-left;
}
@keyframes slide-left {
  from {
    margin-left: 100%;
  }
  to {
    margin-left: 0%;
    opacity:1;
  }
}

  @keyframes fadeInUp {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

       
            
         @keyframes fadeIn {
            0% {
              opacity: 0;
            }
            100% {
              opacity: 1;
            }
          }

          @keyframes popup {
          0% {
            transform: scale(0.5);
            opacity: 0;
          }
          100% {
            transform: scale(1);
            opacity: 1;
          }
        }

            @keyframes slideIn {
          0% {
            transform: translateX(-100%);
            opacity: 0;
          }
          100% {
            transform: translateX(0);
            opacity: 1;
          }
        }


          @media (max-width: 768px) {
            .editor-element {
              font-size: 12px;
            }
          }
          @media (max-width: 480px) {
            .editor-element {
              font-size: 10px;
            }
          }



            @keyframes Bounce {
            0% {
              transform: scale(0.5);
              opacity: 0;
            }
            50% {
              transform: scale(1.1);
              opacity: 1;
            }
            100% {
              transform: scale(1);
              opacity: 1;
            }
          }


@media (max-width: 1200px) {
  .editor-element {
    transform: scale(0.9);
  }
}

@media (max-width: 992px) {
  .editor-element {
    transform: scale(0.8);
  }
}

@media (max-width: 768px) {
  .editor-element {
    transform: scale(0.7);
  }
}

@media (max-width: 576px) {
  .editor-element {
    transform: scale(0.6);
  }
}


svg [class*=editor-element] {
  animation: popup 0.8s ease-out;
     animation-delay: 1.05s;

}
#ExportToHTML{
  color: #fff !important;
}
button > span > svg {
    background: transparent !important;
}


        </style>
      </head>
      <body class=" findBody" "><div class=" findBody2 ${animationType}" style="${bodyStyle}">
        <div class="Indigo ${animationTypeName}">
    `;



      transformedElements.forEach((el) => {
        const convertToUnicode = (content) => {
          const superscriptMap = {
            '0': '⁰', '1': '¹', '2': '²', '3': '³', '4': '⁴',
            '5': '⁵', '6': '⁶', '7': '⁷', '8': '⁸', '9': '⁹'
          };

          const subscriptMap = {
            '0': '₀', '1': '₁', '2': '₂', '3': '₃', '4': '₄',
            '5': '₅', '6': '₆', '7': '₇', '8': '₈', '9': '₉',
            'a': 'ₐ', 'b': 'ₑ', 'c': 'ₒ', 'd': 'ₖ', 'e': 'ₗ',
            'f': 'ₘ', 'g': 'ₙ', 'h': 'ₒ', 'i': 'ᵢ', 'j': 'ⱼ',
            'k': 'ₖ', 'l': 'ₗ', 'm': 'ₘ', 'n': 'ₙ', 'o': 'ₒ',
            'p': 'ₚ', 'q': 'ₛ', 'r': 'ₜ', 's': 'ₛ', 't': 'ₜ',
            'u': 'ᵤ', 'v': 'ᵥ', 'w': 'ₓ', 'x': 'ₓ', 'y': 'ᵧ',
            'z': '₉'
          };

          const convertTextToList = (content) => {
            const lines = content?.split('\n').filter(line => line.trim() !== "");
            const listItems = lines?.map(line => {
              return `<li>${line?.replace(/^[-*]\s*/, '')}</li>`;
            }).join('');

            return `<ul style="list-style-type: none; padding-left: 0;">${listItems}</ul>`;
          };
          content = content?.replace(/<sub>(.*?)<\/sub>/g, (match, p1) => {
            return p1.split('')?.map(char => subscriptMap[char] || char).join('');
          });

          content = content?.replace(/<sup>(.*?)<\/sup>/g, (match, p1) => {
            return p1.split('')?.map(char => superscriptMap[char] || char).join('');
          });

          return convertTextToList(content);

          // return content;
        };


        const formattedContent = el.type === "formula"
          ? `<span class="latex">${convertToUnicode(el.content)}</span>`
          : convertToUnicode(el.content);

        let elementHTML = "";
        if (el.type === "text" || el.type === "formula") {
          const animationStyle =
            el.animation === "none"
              ? `
              opacity: 1;
              transform: none;
            `
              : `
              animation: ${el.animation};
              animation-delay: ${el.animationDelay}s;
              animation-duration: 1s;
              animation-fill-mode: forwards;
            `;
          elementHTML = `
          <div class="editor-element ${el.animation}" style="          
            width: ${el.width}px;
            height: ${el.height}px;
            top: ${el.y}px;
            left: ${el.x}px;
            background-color: ${el.backgroundColor};
            background-image: ${el.backgroundImage ? `url(${el.backgroundImage})` : 'none'};
            background-size: ${el.backgroundSize};
            background-position: ${el.backgroundPosition};
            background-repeat: no-repeat;
            padding: ${el.padding};
            font-weight: ${el.fontWeight};
            font-style: ${el.fontStyle};
            text-align: ${el.textAlign};
            font-size: ${el.fontSize}px;
            color: ${el.color};
            overflow: hidden;
            text-overflow: ellipsis;
            display: block;
            justify-content: center;
            animation:${el.animation};
            animation-delay:${el.animationDelay}s;
            animation-duration: 1s;
            animation-fill-mode: forwards;
            align-items: center;
            text-align: ${el.textAlign};
            ${animationStyle}"
            >
            ${el.type === "formula" ? `<span class="latex">${formattedContent}</span>` : formattedContent}
          </div>`;
        }

        if (el.type === "image") {
          const isAnimated = el.animation !== "none";
          const animationStyle = isAnimated
            ? `
      animation: ${el.animation};
      animation-delay: ${el.animationDelay}s;
      animation-duration: 1s;
      animation-fill-mode: forwards;
    `
            : `
      opacity: 1;
      transform: none;
    `;

          elementHTML = `
    <img 
     id="${el.id}" 
      src="${el.src}" 
      alt="image" 
      class="editor-element" 
      style="
        position: absolute;
        top: ${el.y}px; 
        left: ${el.x}px;
        width: ${el.width}px; 
        height: ${el.height}px;
        max-width: 100%;
        border: 3px solid ${el.borderColor}; 
        ${animationStyle}
      " 
    />
  `;
        }



        if (el.type === "shape") {
          const animationStyle =
            el.animation === "none"
              ? `
              opacity: 1;
              transform: none;
            `
              : `
              animation: ${el.animation};
              animation-delay: ${el.animationDelay}s;
              animation-duration: 1s;
              animation-fill-mode: forwards;
            `;
          let shapeHTML = "";
          const shapeStyle = `
          width: ${el.width}px;
          height: ${el.height}px;
          top: ${el.y}px;
          left: ${el.x + 15}px;
          background-color: ${el.fill};
          border: ${el.strokeWidth}px ${el.lineStyle} ${el.stroke};
          animation:${el.animation};
          animation-delay:${el.animationDelay}s;
           animation-duration: 1s;
            animation-fill-mode: forwards;
          display: flex; justify-content: center; align-items: center; text-align: center;
          ${animationStyle}
        `;
          if (el.shapeType === "rectangle") {
            shapeHTML = `
            <div class="editor-element ${el.animation}" style="${shapeStyle} border: none !important;">
              <span class="latex" style="color: ${el.formulaColor}; background-color: ${el.backgroundColor};">$$${el.formula}$$</span>
            </div>`;
          } else if (el.shapeType === "circle") {
            shapeHTML = ` <div class="editor-element ${el.animation}" style="${shapeStyle} border: none !important; border-radius: 50%;">
              <span class="latex"style="color: ${el.formulaColor}; background-color: ${el.backgroundColor};">$$${el.formula}$$</span>
            </div>`;
          } else if (el.shapeType === "hexagon") {
            shapeHTML = `<div class="editor-element ${el.animation}" style="${shapeStyle} border: none !important; clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%);"><span class="latex"><span class="latex" style="color: ${el.formulaColor}; background-color: ${el.backgroundColor};">$$${el.formula}$$</span></span></div>`;
          } else if (el.shapeType === "pentagon") {
            shapeHTML = `<div class="editor-element ${el.animation}" style="${shapeStyle} border: none !important; clip-path: polygon(50% 0%, 100% 38%, 82% 100%, 18% 100%, 0% 38%);"><span class="latex"><span class="latex"style="color: ${el.formulaColor}; background-color: ${el.backgroundColor};">$$${el.formula}$$</span></span></div>`;
          }
          else if (el.shapeType === "triangle") {
            shapeHTML = `<div class="editor-element ${el.animation}" style="${shapeStyle} border: none !important; clip-path: polygon(50% 0%, 0% 100%, 100% 100%);"><span class="latex"><span class="latex" style="color: ${el.formulaColor}; background-color: ${el.backgroundColor};">$$${el.formula}$$</span></span></div>`;
          }
          else if (el.shapeType === "arrow") {
            const arrowId = `arrow-${el.id}`;

            let x1;
            let y1;
            let x2;
            let y2;

            switch (el.arrowDirection) {
              case "left":
                x1 = "100";
                y1 = "50";
                x2 = "0";
                y2 = "50";
                break;
              case "right":
                x1 = "0";
                y1 = "50";
                x2 = "100";
                y2 = "50";
                break;
              case "up":
                x1 = "50";
                y1 = "100";
                x2 = "50";
                y2 = "0";
                break;
              case "down":
                x1 = "50";
                y1 = "0";
                x2 = "50";
                y2 = "100";
                break;
              default:
                x1 = "0";
                y1 = "50";
                x2 = "100";
                y2 = "50";
            }

            shapeHTML = `
              <svg 
                class="editor-element"
                style="position: absolute; 
                       top: ${el.y}px; 
                       left: ${el.x}px; 
                       width: ${el.width}px; 
                       height: ${el.height}px;
                       overflow: visible;"
                viewBox="0 0 100 100"
              >
                <defs>
                  <marker 
                    id="${arrowId}"
                    viewBox="0 0 10 10" 
                    refX="5"
                    refY="5"
                    markerWidth="6" 
                    markerHeight="6"
                    orient="auto"
                  >
                    <path d="M 0 0 L 10 5 L 0 10 z" fill="${el.stroke}"/>
                  </marker>
                </defs>
                <line
                  x1="${x1}%"
                  y1="${y1}%"
                  x2="${x2}%"
                  y2="${y2}%"
                  stroke="${el.stroke}"
                  stroke-width="${el.strokeWidth || 2}"
                  stroke-dasharray="${getDashArray(el.lineStyle)}"
                  marker-end="url(#${arrowId})"
                />
              </svg>`;
          }


          elementHTML = shapeHTML;
        }

        htmlContent += elementHTML;

      });

      htmlContent += `</div></body></html>`;
      onSubmit(htmlContent, elements, backgroundImageSettings, animationTypeName)
    }
  };


  const getJustifyContent = (textAlign) => {
    if (textAlign === "center") return "center";
    if (textAlign === "right") return "flex-end";
    return "flex-start";
  };
  function getDashArray(lineStyle) {
    if (lineStyle === "dashed") return "5,5";
    if (lineStyle === "dotted") return "2,2";
    return "0";
  }
  function getStrokeDasharray(lineStyle) {
    if (lineStyle === "dashed") {
      return "4,4";
    }
    if (lineStyle === "dotted") {
      return "1,4";
    }
    return "0";
  }
  const handleMouseOver = (e) => {
    e.target.style.backgroundColor = "#bdbdbd";
  };

  const handleMouseOut = (e) => {
    e.target.style.backgroundColor = "#d3d3d3";
  };



  const applyFormatting = (formatType) => {
    setElements((prevElements) =>
      prevElements.map((el) =>
        el.id === selectedElement
          ? {
            ...el,
            [formatType]: !el[formatType],
          }
          : el
      )
    );
  };



  return (
    <Grid container>
      <Grid item xs={9}
        className="editor-container"
        id="EditorContainer"
        style={{
          maxWidth: '750px',
          position: "relative",
          height: "450px",
          border: "1px solid #333",
          backgroundImage: backgroundImageSettings?.url ? `url(${backgroundImageSettings?.url})` : 'none',
          backgroundSize: "contain",
          backgroundPosition: "top",
          backgroundRepeat: "no-repeat"
        }}
        onClick={(e) => {
          if (e.target === e.currentTarget) {
            setSelectedElement(null);
            setVisibleDeleteId(null);
          }
        }}>
        {previewMode ? (
          <div
            style={{
              padding: "20px",
              position: "relative",
              width: "100%",
              height: "100%",
            }}
          >
            {/* <span>1234 <CloseIcon /></span> */}
            <button
              style={{
                fontSize: '12px',
                width: '24px',
                height: '24px',
                backgroundColor: 'rgb(189 68 68)',
                color: 'white',
                position: 'absolute',
                right: '-25px',
                top: '-25px',
                cursor: 'pointer',
                border: 'none',
                padding: 0,
              }}
              onClick={() => {
                setPreviewMode(!previewMode);
                setSelectedElement(null);
              }}
            >
              <CloseIcon />
            </button>

            {elements.map((el) => (
              <div
                key={el.id}
                style={{
                  position: "absolute",
                  left: `${((el.x || 0) / window.innerWidth) * 100}vw`,
                  top: `${((el.y || 0) / window.innerHeight) * 100}vh`,
                  // width: `${((el.width || 100) / window.innerWidth) * 100}vw`,
                  width: `${(el.width / 756) * 100}%`,
                  height: `${((el.height || 100) / window.innerHeight) * 100}vh`,
                  fontSize: el.fontSize,
                  fontWeight: el.fontWeight,
                  color: el.color,
                  backgroundColor: el.backgroundColor,
                  textAlign: el.textAlign,
                  display: el.type === "text" ? "flex" : "block",
                  alignItems: "center",
                  justifyContent: getJustifyContent(el.textAlign),
                  padding: "5px",
                  backgroundImage: el.backgroundImage ? `url(${el.backgroundImage})` : "none",
                  backgroundSize: "contain",
                  backgroundPosition: "top",
                  backgroundRepeat: "no-repeat"
                  // ...getAnimationStyles(el.animationType),
                }}
              >
                {el.type === "text" && (
                  <div
                    dangerouslySetInnerHTML={{
                      __html: el.content.replace(/\n/g, "<br/>")
                    }}
                  />
                )}                {el.type === "formula" && (
                  <div>
                    <Latex>{`$${el.content}$`}</Latex>
                  </div>
                )}
                {el.type === "image" && <img src={el.src} alt="" style={{ width: "100%", height: "100%" }} />}

                {el.type === "shape" && el.shapeType === "rectangle" && (
                  <div
                    style={{
                      width: "100%",
                      height: "100%",
                      backgroundColor: el.fill,
                      border: `${el.strokeWidth}px ${el.lineStyle} ${el.stroke}`,
                      position: "relative",
                    }}
                  >
                    {el.text && (
                      <div
                        style={{
                          position: "absolute",
                          top: "50%",
                          left: "50%",
                          transform: "translate(-50%, -50%)",
                          fontSize: el.fontSize || "12px",
                          color: el.textColor || "#000",
                          fontWeight: el.fontWeight || "normal",
                          textAlign: "center",
                        }}
                        dangerouslySetInnerHTML={{ __html: el.content }}
                      />


                    )}
                    {el.formula && (
                      <div
                        style={{
                          position: "absolute",
                          top: "50%",
                          left: "50%",
                          transform: "translate(-50%, -50%)",
                          fontSize: el.fontSize || "12px",
                          color: el.formulaColor || "#000",
                          fontWeight: el.fontWeight || "normal",
                          textAlign: "center",
                        }}
                      >
                        <Latex>{`$${el.formula}$`}</Latex>
                      </div>
                    )}
                  </div>
                )}

                {el.type === "shape" && el.shapeType === "circle" && (
                  <div
                    style={{
                      width: "100%",
                      height: "100%",
                      borderRadius: "50%",
                      backgroundColor: el.fill,
                      border: `${el.strokeWidth}px ${el.lineStyle} ${el.stroke}`,
                      position: "relative",
                    }}
                  >
                    {el.text && (
                      <div
                        style={{
                          position: "absolute",
                          top: "50%",
                          left: "50%",
                          transform: "translate(-50%, -50%)",
                          fontSize: el.fontSize || "12px",
                          color: el.textColor || "#000",
                          fontWeight: el.fontWeight || "normal",
                          textAlign: "center",
                        }}
                        dangerouslySetInnerHTML={{ __html: el.content }}
                      />

                    )}
                    {el.formula && (
                      <div
                        style={{
                          position: "absolute",
                          top: "50%",
                          left: "50%",
                          transform: "translate(-50%, -50%)",
                          fontSize: el.fontSize || "12px",
                          color: el.formulaColor || "#000",
                          fontWeight: el.fontWeight || "normal",
                          textAlign: "center",
                        }}
                      >
                        <Latex>{`$${el.formula}$`}</Latex>
                      </div>
                    )}
                  </div>
                )}

                {el.type === "shape" && el.shapeType === "triangle" && (
                  <div
                    style={{
                      width: el.size || "100%",
                      height: el.size || "100%",
                      backgroundColor: el.fill,
                      clipPath: "polygon(50% 0%, 0% 100%, 100% 100%)",
                      border: getStrokeDasharray(el.lineStyle, el.strokeWidth, el.stroke),
                      position: "relative",
                    }}
                  >
                    {el.text && (
                      <div
                        style={{
                          position: "absolute",
                          top: "50%",
                          left: "50%",
                          transform: "translate(-50%, -50%)",
                          fontSize: el.fontSize || "12px",
                          color: el.textColor || "#000",
                          fontWeight: el.fontWeight || "normal",
                          textAlign: "center",
                        }}
                        dangerouslySetInnerHTML={{ __html: el.content }}
                      />

                    )}
                    {el.formula && (
                      <div
                        style={{
                          position: "absolute",
                          top: "50%",
                          left: "50%",
                          transform: "translate(-50%, -50%)",
                          fontSize: el.fontSize || "12px",
                          color: el.formulaColor || "#000",
                          fontWeight: el.fontWeight || "normal",
                          textAlign: "center",
                        }}
                      >
                        <Latex>{`$${el.formula}$`}</Latex>
                      </div>
                    )}
                  </div>
                )}



                {el.type === "shape" && el.shapeType === "hexagon" && (
                  <div
                    style={{
                      width: el.size || "100%",
                      height: el.size || "100%",
                      backgroundColor: el.fill,
                      clipPath: "polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%)",
                      border: getStrokeDasharray(el.lineStyle, el.strokeWidth, el.stroke),
                      position: "relative",
                    }}
                  >
                    {el.text && (
                      <div
                        style={{
                          position: "absolute",
                          top: "50%",
                          left: "50%",
                          transform: "translate(-50%, -50%)",
                          fontSize: el.fontSize || "12px",
                          color: el.textColor || "#000",
                          fontWeight: el.fontWeight || "normal",
                          textAlign: "center",
                        }}
                        dangerouslySetInnerHTML={{ __html: el.content }}
                      />

                    )}
                    {el.formula && (
                      <div
                        style={{
                          position: "absolute",
                          top: "50%",
                          left: "50%",
                          transform: "translate(-50%, -50%)",
                          fontSize: el.fontSize || "12px",
                          color: el.formulaColor || "#000",
                          fontWeight: el.fontWeight || "normal",
                          textAlign: "center",
                        }}
                      >
                        <Latex>{`$${el.formula}$`}</Latex>
                      </div>
                    )}
                  </div>
                )}

                {el.type === "shape" && el.shapeType === "pentagon" && (
                  <div
                    style={{
                      width: el.size || "100%",
                      height: el.size || "100%",
                      backgroundColor: el.fill,
                      clipPath: "polygon(50% 0%, 100% 38%, 82% 100%, 18% 100%, 0% 38%)",
                      border: `${el.strokeWidth}px ${el.lineStyle} ${el.stroke}`,
                      position: "relative",
                    }}
                  >
                    {el.text && (
                      <div
                        style={{
                          position: "absolute",
                          top: "50%",
                          left: "50%",
                          transform: "translate(-50%, -50%)",
                          fontSize: el.fontSize || "12px",
                          color: el.textColor || "#000",
                          fontWeight: el.fontWeight || "normal",
                          textAlign: "center",
                        }}
                        dangerouslySetInnerHTML={{ __html: el.content }}
                      />

                    )}
                    {el.formula && (
                      <div
                        style={{
                          position: "absolute",
                          top: "50%",
                          left: "50%",
                          transform: "translate(-50%, -50%)",
                          fontSize: el.fontSize || "12px",
                          color: el.formulaColor || "#000",
                          fontWeight: el.fontWeight || "normal",
                          textAlign: "center",
                        }}
                      >
                        <Latex>{`$${el.formula}$`}</Latex>
                      </div>
                    )}
                  </div>
                )}

                {el.type === "shape" && el.shapeType === "arrow" && (
                  <svg width="100%" height="100%">
                    {el.arrowDirection === "right" && (
                      <line
                        x1="0"
                        y1="50%"
                        x2="100%"
                        y2="50%"
                        stroke={el.stroke}
                        strokeWidth={el.strokeWidth}
                        strokeDasharray={getDashArray(el.lineStyle)}
                        markerEnd={el.arrowHead === "end" || el.arrowHead === "both" ? "url(#arrowheadRight)" : ""}
                        markerStart={el.arrowHead === "start" || el.arrowHead === "both" ? "url(#arrowheadRight)" : ""}
                      />
                    )}
                    {el.arrowDirection === "left" && (
                      <line
                        x1="100%"
                        y1="50%"
                        x2="0"
                        y2="50%"
                        stroke={el.stroke}
                        strokeWidth={el.strokeWidth}
                        strokeDasharray={getDashArray(el.lineStyle)}
                        markerEnd={el.arrowHead === "end" || el.arrowHead === "both" ? "url(#arrowheadLeft)" : ""}
                        markerStart={el.arrowHead === "start" || el.arrowHead === "both" ? "url(#arrowheadLeft)" : ""}
                      />
                    )}
                    {el.arrowDirection === "up" && (
                      <line
                        x1="50%"
                        y1="100%"
                        x2="50%"
                        y2="0"
                        stroke={el.stroke}
                        strokeWidth={el.strokeWidth}
                        strokeDasharray={getDashArray(el.lineStyle)}
                        markerEnd={el.arrowHead === "end" || el.arrowHead === "both" ? "url(#arrowheadUp)" : ""}
                        markerStart={el.arrowHead === "start" || el.arrowHead === "both" ? "url(#arrowheadUp)" : ""}
                      />
                    )}
                    {el.arrowDirection === "down" && (
                      <line
                        x1="50%"
                        y1="0"
                        x2="50%"
                        y2="100%"
                        stroke={el.stroke}
                        strokeWidth={el.strokeWidth}
                        strokeDasharray={getDashArray(el.lineStyle)}
                        markerEnd={el.arrowHead === "end" || el.arrowHead === "both" ? "url(#arrowheadDown)" : ""}
                        markerStart={el.arrowHead === "start" || el.arrowHead === "both" ? "url(#arrowheadDown)" : ""}
                      />
                    )}
                    <defs>
                      {['Right', 'Left', 'Up', 'Down'].map((dir) => (
                        <marker
                          key={dir}
                          id={`arrowhead${dir}`}
                          markerWidth="10"
                          markerHeight="7"
                          refX="10"
                          refY="3.5"
                          orient="auto"
                        >
                          <polygon points="0 0, 10 3.5, 0 7" fill={el.stroke} />
                        </marker>
                      ))}
                    </defs>
                  </svg>
                )}
              </div>
            ))}
          </div>
        ) : (
          elements?.map((el) => (
            <Rnd
              bounds=".editor-container"
              key={el.id}
              size={{ width: el.width, height: el.height }}
              position={{
                x: Math.min(Math.max(0, el.x), containerDimensions.width - el.width),
                y: Math.min(Math.max(0, el.y), containerDimensions.height - el.height)
              }}
              onDragStop={(e, d) => {
                const x = Math.min(Math.max(0, d.x), containerDimensions.width - el.width);
                const y = Math.min(Math.max(0, d.y), containerDimensions.height - el.height);
                updateElement(el.id, { x, y });
              }}
              onResizeStop={(e, arrowDirection, ref, delta, position) =>
                updateElement(el.id, {
                  width: ref.offsetWidth,
                  height: ref.offsetHeight,
                  ...position,
                })
              }
              style={{
                border: "1px dashed gray",
                background: el.backgroundColor,
                backgroundImage: el.backgroundImage ? `url(${el.backgroundImage})` : "none",
                backgroundSize: "contain",
                backgroundPosition: "top",
                backgroundRepeat: "no-repeat"
              }}
              onClick={() => setSelectedElement(prev => prev === el.id ? null : el.id)}
              onDoubleClick={() => setVisibleDeleteId(visibleDeleteId === el.id ? null : el.id)}
            >
              {visibleDeleteId === el.id && (
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    deleteElement(el.id);
                  }}
                  style={{
                    position: "absolute",
                    top: 5,
                    right: 5,
                    background: "black",
                    color: "white",
                    border: "none",
                    cursor: "pointer",
                    borderRadius: "50%",
                    width: "24px",
                    height: "24px",
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                    fontSize: "16px",
                    zIndex: 10,
                  }}
                >
                  <DeleteOutlineIcon fontSize="small" />
                </button>
              )}

              {el.type === "text" && (
                <div
                  style={{
                    padding: el.padding,
                    fontWeight: el.fontWeight,
                    fontStyle: el.fontStyle,
                    textAlign: el.textAlign,
                    fontSize: el.fontSize,
                    color: el.color,
                    width: "100%",
                    height: "100%",
                    alignItems: "center",
                    justifyContent: getJustifyContent(el.textAlign),
                    whiteSpace: "pre-wrap",
                    overflow: "hidden",
                    wordBreak: "break-word",
                    textDecoration: el.underline ? "underline" : "none",
                  }}
                  dangerouslySetInnerHTML={{ __html: el.content }}
                />
              )}


              {el.type === "formula" && (
                <div
                  style={{
                    padding: el.padding,
                    fontWeight: el.fontWeight,
                    fontStyle: el.fontStyle,
                    textAlign: el.textAlign,
                    fontSize: el.fontSize,
                    color: el.color,
                    width: "100%",
                    height: "100%",
                    display: "flex",
                    alignItems: "center",
                    justifyContent: getJustifyContent(el.textAlign),
                    whiteSpace: "nowrap",
                    overflow: "hidden",
                    textOverflow: "ellipsis",
                  }}
                >
                  <Latex>{`$${el.content}$`}</Latex>
                </div>
              )}

              {el.type === "image" && (
                <img src={el.src} alt="" style={{
                  width: "100%", height: "100%",
                  border: `3px solid ${el.borderColor}`
                }} />
              )}

              {el.type === "shape" && el.shapeType === "rectangle" && (
                <div
                  style={{
                    width: "100%",
                    height: "100%",
                    backgroundColor: el.fill,
                    border: `${el.strokeWidth}px ${el.lineStyle} ${el.stroke}`,
                    position: "relative",
                  }}
                >
                  {el.text && (
                    <div
                      style={{
                        position: "absolute",
                        top: "50%",
                        left: "50%",
                        transform: "translate(-50%, -50%)",
                        fontSize: el.fontSize || "12px",
                        color: el.textColor || "#000",
                        fontWeight: el.fontWeight || "normal",
                        textAlign: "center",
                      }}
                    >
                      {el.text}
                    </div>
                  )}
                  {el.formula && (
                    <div
                      style={{
                        position: "absolute",
                        top: "50%",
                        left: "50%",
                        transform: "translate(-50%, -50%)",
                        fontSize: el.fontSize || "12px",
                        color: el.formulaColor || "#000",
                        fontWeight: el.fontWeight || "normal",
                        textAlign: "center", // Ensure the text is centered
                      }}
                    >
                      <Latex>{`$${el.formula}$`}</Latex>
                    </div>
                  )}
                </div>
              )}

              {el.type === "shape" && el.shapeType === "circle" && (
                <div
                  style={{
                    width: "100%",
                    height: "100%",
                    borderRadius: "50%",
                    backgroundColor: el.fill,
                    border: `${el.strokeWidth}px ${el.lineStyle} ${el.stroke}`,
                    position: "relative",
                  }}
                >
                  {el.text && (
                    <div
                      style={{
                        position: "absolute",
                        top: "50%",
                        left: "50%",
                        transform: "translate(-50%, -50%)",
                        fontSize: el.fontSize || "12px",
                        color: el.textColor || "#000",
                        fontWeight: el.fontWeight || "normal",
                        textAlign: "center",
                      }}
                    >
                      {el.text}
                    </div>
                  )}
                  {el.formula && (
                    <div
                      style={{
                        position: "absolute",
                        top: "50%",
                        left: "50%",
                        transform: "translate(-50%, -50%)",
                        fontSize: el.fontSize || "12px",
                        color: el.formulaColor || "#000",
                        fontWeight: el.fontWeight || "normal",
                        textAlign: "center",
                      }}
                    >
                      <Latex>{`$${el.formula}$`}</Latex>
                    </div>
                  )}
                </div>
              )}


              {el.type === "shape" && el.shapeType === "line" && (
                <div
                  style={{
                    width: "100%",
                    height: `${el.strokeWidth}px`,
                    backgroundColor: el.stroke,
                    borderTop: `${el.strokeWidth}px ${el.lineStyle} ${el.stroke}`,
                  }}
                />
              )}

              {el.type === "shape" && el.shapeType === "arrow" && (
                <svg width="100%" height="100%">
                  {el.arrowDirection === "right" && (
                    <line
                      x1="0"
                      y1="50%"
                      x2="100%"
                      y2="50%"
                      stroke={el.stroke}
                      strokeWidth={el.strokeWidth}
                      strokeDasharray={getDashArray(el.lineStyle)}
                      markerEnd={el.arrowHead === "end" || el.arrowHead === "both" ? "url(#arrowheadRight)" : ""}
                      markerStart={el.arrowHead === "start" || el.arrowHead === "both" ? "url(#arrowheadRight)" : ""}
                    />
                  )}

                  {el.arrowDirection === "left" && (
                    <line
                      x1="100%"
                      y1="50%"
                      x2="0"
                      y2="50%"
                      stroke={el.stroke}
                      strokeWidth={el.strokeWidth}
                      strokeDasharray={getDashArray(el.lineStyle)}
                      markerEnd={el.arrowHead === "end" || el.arrowHead === "both" ? "url(#arrowheadLeft)" : ""}
                      markerStart={el.arrowHead === "start" || el.arrowHead === "both" ? "url(#arrowheadLeft)" : ""}
                    />
                  )}

                  {el.arrowDirection === "up" && (
                    <line
                      x1="50%"
                      y1="100%"
                      x2="50%"
                      y2="0"
                      stroke={el.stroke}
                      strokeWidth={el.strokeWidth}
                      strokeDasharray={getDashArray(el.lineStyle)}
                      markerEnd={el.arrowHead === "end" || el.arrowHead === "both" ? "url(#arrowheadUp)" : ""}
                      markerStart={el.arrowHead === "start" || el.arrowHead === "both" ? "url(#arrowheadUp)" : ""}
                    />
                  )}

                  {el.arrowDirection === "down" && (
                    <line
                      x1="50%"
                      y1="0"
                      x2="50%"
                      y2="100%"
                      stroke={el.stroke}
                      strokeWidth={el.strokeWidth}
                      strokeDasharray={getDashArray(el.lineStyle)}
                      markerEnd={el.arrowHead === "end" || el.arrowHead === "both" ? "url(#arrowheadDown)" : ""}
                      markerStart={el.arrowHead === "start" || el.arrowHead === "both" ? "url(#arrowheadDown)" : ""}
                    />
                  )}

                  <defs>
                    {['Right', 'Left', 'Up', 'Down'].map((dir) => (
                      <marker
                        key={dir}
                        id={`arrowhead${dir}`}
                        markerWidth="10"
                        markerHeight="7"
                        refX="10"
                        refY="3.5"
                        orient="auto"
                      >
                        <polygon points="0 0, 10 3.5, 0 7" fill={el.stroke} />
                      </marker>
                    ))}
                  </defs>
                </svg>
              )}

              {el.type === "shape" && el.shapeType === "triangle" && (
                <svg
                  width={el.base || "100%"}
                  height={el.height || "100%"}
                  viewBox="0 0 100 100"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <polygon
                    points="50,0 0,100 100,100"
                    fill={el.fill}
                    stroke={el.stroke}
                    strokeWidth={el.strokeWidth}
                    strokeDasharray={getStrokeDasharray(el.lineStyle)}
                  />

                  {el.formula && (
                    <foreignObject x="25" y="30" width="50" height="40">
                      <div
                        xmlns="http://www.w3.org/1999/xhtml"
                        style={{
                          display: "flex",
                          justifyContent: "center",
                          alignItems: "center",
                          width: "100%",
                          height: "100%",
                          fontSize: el.fontSize || "12px",
                          color: el.formulaColor || "#000",
                          fontWeight: el.fontWeight || "normal",
                          textAlign: "center",
                        }}
                      >
                        <Latex>{`$${el.formula}$`}</Latex>
                      </div>
                    </foreignObject>
                  )}
                </svg>
              )}


              
              {el.type === "shape" && el.shapeType === "hexagon" && (
                <div
                  style={{
                    width: el.size || "100%",
                    height: el.size || "100%",
                    backgroundColor: el.fill,
                    clipPath: "polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%)",
                    border: getStrokeDasharray(el.lineStyle, el.strokeWidth, el.stroke),
                    position: "relative",
                  }}
                >
                  {el.text && (
                    <div
                      style={{
                        position: "absolute",
                        top: "50%",
                        left: "50%",
                        transform: "translate(-50%, -50%)",
                        fontSize: el.fontSize || "12px",
                        color: el.textColor || "#000",
                        fontWeight: el.fontWeight || "normal",
                        textAlign: "center",
                      }}
                    >
                      {el.text}
                    </div>
                  )}
                  {el.formula && (
                    <div
                      style={{
                        position: "absolute",
                        top: "50%",
                        left: "50%",
                        transform: "translate(-50%, -50%)",
                        fontSize: el.fontSize || "12px",
                        color: el.formulaColor || "#000",
                        fontWeight: el.fontWeight || "normal",
                        textAlign: "center",
                      }}
                    >
                      <Latex>{`$${el.formula}$`}</Latex>
                    </div>
                  )}
                </div>
              )}
              {el.type === "shape" && el.shapeType === "pentagon" && (
                <div
                  style={{
                    width: el.size || "100%",
                    height: el.size || "100%",
                    backgroundColor: el.fill,
                    clipPath: "polygon(50% 0%, 100% 38%, 82% 100%, 18% 100%, 0% 38%)",
                    border: getStrokeDasharray(el.lineStyle, el.strokeWidth, el.stroke),
                    position: "relative",
                  }}
                >
                  {el.text && (
                    <div
                      style={{
                        position: "absolute",
                        top: "50%",
                        left: "50%",
                        transform: "translate(-50%, -50%)",
                        fontSize: el.fontSize || "12px",
                        color: el.textColor || "#000",
                        fontWeight: el.fontWeight || "normal",
                        textAlign: "center",
                      }}
                    >
                      {el.text}
                    </div>
                  )}
                  {el.formula && (
                    <div
                      style={{
                        position: "absolute",
                        top: "50%",
                        left: "50%",
                        transform: "translate(-50%, -50%)",
                        fontSize: el.fontSize || "12px",
                        color: el.formulaColor || "#000",
                        fontWeight: el.fontWeight || "normal",
                        textAlign: "center",
                      }}
                    >
                      <Latex>{`$${el.formula}$`}</Latex>
                    </div>
                  )}
                </div>
              )}
            </Rnd>

          ))
        )}
      </Grid>

      <Grid item xs={previewMode ? 0 : 3} id="sidebarEditor" style={{ padding: "10px", borderLeft: "1px solid #ccc", display: previewMode ? 'none' : 'block' }}>
        {selectedElement ?
          <>
            {selectedType?.type === 'text' && <Paper style={{ padding: "10px", marginTop: "10px" }}>
              <TextareaAutosize
                minRows={3}
                maxRows={6}
                name="Edit Text"
                value={elements.find(el => el.id === selectedElement)?.content || ""}
                onChange={(e) => updateElement(selectedElement, { content: e.target.value })}
                onSelect={(e) => {
                  const textarea = e.target;
                  const start = textarea.selectionStart;
                  const end = textarea.selectionEnd;
                  setSelectedRange({ start, end });
                }}
                style={{ width: '100%', padding: '8px', fontSize: '16px', borderRadius: '4px', border: '1px solid #ccc' }}
              />
              {selectedRange.start !== selectedRange.end && <ToggleButtonGroup fullWidth style={{ marginTop: "10px" }}>
                <ToggleButton
                  selected={/<u>.*<\/u>/.test(elements.find(el => el.id === selectedElement)?.content || "")}
                  onClick={() => toggleTextFormatting("underline")}
                >
                  <FormatUnderlinedIcon />
                </ToggleButton>

                <ToggleButton
                  selected={/<sup>.*<\/sup>/.test(elements.find(el => el.id === selectedElement)?.content || "")}
                  onClick={() => toggleTextFormatting("superscript")}
                >
                  <SuperscriptIcon />
                </ToggleButton>

                <ToggleButton
                  selected={/<sub>.*<\/sub>/.test(elements.find(el => el.id === selectedElement)?.content || "")}
                  onClick={() => toggleTextFormatting("subscript")}
                >
                  <SubscriptIcon />
                </ToggleButton>
              </ToggleButtonGroup>}
              <ToggleButtonGroup
                value={elements.find(el => el.id === selectedElement)?.fontWeight || "normal"}
                exclusive
                onChange={(e, value) => updateElement(selectedElement, { fontWeight: value })}
                fullWidth
                style={{ marginTop: "10px" }}
              >
                <ToggleButton value="normal">Normal</ToggleButton>
                <ToggleButton value="bold">Bold</ToggleButton>
              </ToggleButtonGroup>

              <ToggleButtonGroup
                value={elements.find(el => el.id === selectedElement)?.fontStyle || "normal"}
                exclusive
                onChange={(e, value) => updateElement(selectedElement, { fontStyle: value })}
                fullWidth
                style={{ marginTop: "10px" }}
              >
                <ToggleButton value="normal">Normal</ToggleButton>
                <ToggleButton value="italic">Italic</ToggleButton>
              </ToggleButtonGroup>

              <ToggleButtonGroup
                value={elements.find(el => el.id === selectedElement)?.textAlign || "left"}
                exclusive
                onChange={(e, value) => updateElement(selectedElement, { textAlign: value })}
                fullWidth
                style={{ marginTop: "10px" }}
              >
                <ToggleButton value="left">Left</ToggleButton>
                <ToggleButton value="center">Center</ToggleButton>
                <ToggleButton value="right">Right</ToggleButton>
              </ToggleButtonGroup>

              {/* <ToggleButtonGroup
                value={elements.find(el => el.id === selectedElement)?.underline || false}
                exclusive
                onChange={() => applyFormatting("underline")}
                fullWidth
                style={{ marginTop: "10px" }}
              >
                <ToggleButton value={true}>Underline</ToggleButton>
              </ToggleButtonGroup> */}

              {/* <ToggleButtonGroup
                value={elements.find(el => el.id === selectedElement)?.superscript || false}
                exclusive
                onChange={() => applyFormatting("superscript")}
                fullWidth
                style={{ marginTop: "10px" }}
              >
                <ToggleButton value={true}>Superscript</ToggleButton>
              </ToggleButtonGroup>

              <ToggleButtonGroup
                value={elements.find(el => el.id === selectedElement)?.subscript || false}
                exclusive
                onChange={() => applyFormatting("subscript")}
                fullWidth
                style={{ marginTop: "10px" }}
              >
                <ToggleButton value={true}>Subscript</ToggleButton>
              </ToggleButtonGroup> */}


              <TextField
                type="number"
                variant="outlined"
                label="Font Size"
                value={elements.find(el => el.id === selectedElement)?.fontSize ?? ''}
                onChange={(e) => {
                  const value = e.target.value === '' ? '' : parseInt(e.target.value, 10);
                  updateElement(selectedElement, { fontSize: value });
                }}
                onBlur={(e) => {
                  if (e.target.value === '') {
                    updateElement(selectedElement, { fontSize: 12 });
                  }
                }}
                fullWidth
                style={{ marginTop: "15px" }}
              />


              <Box display="flex" gap={2} alignItems="center" padding={'0 10px'} style={{ marginTop: "20px", marginBottom: "10px" }}>
                <TextField
                  name="Text Color"
                  label="Text Color"
                  variant="outlined"
                  type="color"
                  value={elements.find(el => el.id === selectedElement)?.color || "#000000"}
                  onChange={(e) => updateElement(selectedElement, { color: e.target.value })}
                  style={{ flex: 1 }}
                />
                <TextField
                  name="Background Color"
                  variant="outlined"
                  label="BG Color"
                  type="color"
                  value={elements.find(el => el.id === selectedElement)?.backgroundColor === "transparent" ? "#ffffff" : (elements.find(el => el.id === selectedElement)?.backgroundColor || "#a3a3a3")}
                  onChange={(e) => updateElement(selectedElement, { backgroundColor: e.target.value })}
                  style={{ flex: 1 }}
                />
              </Box>

              {/* <TextField
                variant="outlined"
                label="Background Image URL"
                value={elements.find(el => el.id === selectedElement)?.backgroundImage || ""}
                onChange={(e) => updateElement(selectedElement, { backgroundImage: e.target.value })}
                fullWidth
                style={{ marginTop: "10px" }}
              /> */}

              {/* <Select
                value={elements.find(el => el.id === selectedElement)?.backgroundSize || "cover"}
                onChange={(e) => updateElement(selectedElement, { backgroundSize: e.target.value })}
                fullWidth
                style={{ marginTop: "10px" }}
              >
                <MenuItem value="cover">Cover</MenuItem>
                <MenuItem value="contain">Contain</MenuItem>
                <MenuItem value="auto">Auto</MenuItem>
              </Select>
              <Select
                value={elements.find(el => el.id === selectedElement)?.backgroundPosition || "center"}
                onChange={(e) => updateElement(selectedElement, { backgroundPosition: e.target.value })}
                fullWidth
                style={{ marginTop: "10px" }}
              >
                <MenuItem value="left">Left</MenuItem>
                <MenuItem value="center">Center</MenuItem>
                <MenuItem value="right">Right</MenuItem>
              </Select> */}
              <Typography>Animation Style</Typography>
              <Select
                value={elements.find(el => el.id === selectedElement)?.animation || "none"}
                onChange={(e) => updateElement(selectedElement, { animation: e.target.value })}
                fullWidth
                style={{ marginTop: "10px" }}
              >
                <MenuItem value="none">None</MenuItem>
                {/* <MenuItem value="slideIn 0.5s ease forwards">Slide out</MenuItem> */}
                <MenuItem value="slideIn 1s ease-out">Slide In</MenuItem>
                {/* <MenuItem value="fadeIn 1.5s ease-in">Fade In</MenuItem> */}
                <MenuItem value="Bounce 1s ease-out">Bounce</MenuItem>
              </Select>
              <TextField
                type="number"
                variant="outlined"
                label="Animation Delay (s)"
                value={elements.find(el => el.id === selectedElement)?.animationDelay ?? ''}
                onChange={(e) => {

                  let inputValue = e.target.value;
                  if (inputValue.length > 2) {
                    inputValue = inputValue.slice(0, 2);
                  }
                  const value = inputValue === '' ? "" : parseFloat(inputValue);
                  updateElement(selectedElement, { animationDelay: value });
                }}
                onKeyDown={(e) => {
                  if (e.key === 'Backspace' && e.target.value === '0') {
                    updateElement(selectedElement, { animationDelay: '' });
                  }
                }}
                fullWidth
                style={{ marginTop: "10px" }}
                inputProps={{
                  maxLength: 2,
                  min: 0,
                  max: 99
                }}
              />
            </Paper>}
            {selectedType?.type === 'formula' && <Paper style={{ padding: "10px", marginTop: "10px" }}>
              <Typography>Formula (kaTex)</Typography>
              <TextField
                variant="outlined"
                value={elements.find(el => el.id === selectedElement)?.content || ""}
                onChange={(e) => updateElement(selectedElement, { content: e.target.value })}
                fullWidth
                style={{ marginTop: "10px" }}
              />
              <Typography variant="body2" style={{ marginTop: "10px", textAlign: "center" }}>
                To get formula  check out the <a href="https://katex.org/docs/supported.html" target="_blank" rel="noopener noreferrer">KaTeX documentation</a>.
              </Typography>
              <ToggleButtonGroup
                value={elements.find(el => el.id === selectedElement)?.fontWeight || "normal"}
                exclusive
                onChange={(e, value) => updateElement(selectedElement, { fontWeight: value })}
                fullWidth
                style={{ marginTop: "10px" }}
              >
                <ToggleButton value="normal">Normal</ToggleButton>
                <ToggleButton value="bold">Bold</ToggleButton>
              </ToggleButtonGroup>

              <ToggleButtonGroup
                value={elements.find(el => el.id === selectedElement)?.fontStyle || "normal"}
                exclusive
                onChange={(e, value) => updateElement(selectedElement, { fontStyle: value })}
                fullWidth
                style={{ marginTop: "10px" }}
              >
                <ToggleButton value="normal">Normal</ToggleButton>
                <ToggleButton value="italic">Italic</ToggleButton>
              </ToggleButtonGroup>

              <ToggleButtonGroup
                value={elements.find(el => el.id === selectedElement)?.textAlign || "left"}
                exclusive
                onChange={(e, value) => updateElement(selectedElement, { textAlign: value })}
                fullWidth
                style={{ marginTop: "10px" }}
              >
                <ToggleButton value="left">Left</ToggleButton>
                <ToggleButton value="center">Center</ToggleButton>
                <ToggleButton value="right">Right</ToggleButton>
              </ToggleButtonGroup>

              <TextField
                type="number"
                variant="outlined"
                label="Font Size"
                value={elements.find(el => el.id === selectedElement)?.fontSize ?? ''}
                onChange={(e) => {
                  const value = e.target.value === '' ? '' : parseInt(e.target.value, 10);
                  updateElement(selectedElement, { fontSize: value });
                }}
                onBlur={(e) => {
                  if (e.target.value === '') {
                    updateElement(selectedElement, { fontSize: 12 });
                  }
                }}
                fullWidth
                style={{ marginTop: "15px" }}
              />

              <Box display="flex" gap={2} alignItems="center" padding={'0 10px'} style={{ marginTop: "20px" }}>
                <TextField
                  name="Text Color"
                  label="Text Color"
                  variant="outlined"
                  type="color"
                  value={elements.find(el => el.id === selectedElement)?.color || "#000000"}
                  onChange={(e) => updateElement(selectedElement, { color: e.target.value })}
                  style={{ flex: 1 }}
                />
                <TextField
                  name="Background Color"
                  variant="outlined"
                  label="BG Color"
                  type="color"
                  value={elements.find(el => el.id === selectedElement)?.backgroundColor === "transparent" ? "#ffffff" : (elements.find(el => el.id === selectedElement)?.backgroundColor || "#a3a3a3")}
                  onChange={(e) => updateElement(selectedElement, { backgroundColor: e.target.value })}
                  style={{ flex: 1 }}
                />
              </Box>
              <Typography>Animation Style</Typography>
              <Select
                value={elements.find(el => el.id === selectedElement)?.animation || "none"}
                onChange={(e) => updateElement(selectedElement, { animation: e.target.value })}
                fullWidth
                style={{ marginTop: "10px" }}
              >
                <MenuItem value="none">None</MenuItem>
                {/* <MenuItem value="slideIn 0.5s ease forwards">Slide out</MenuItem> */}
                <MenuItem value="slideIn 1s ease-out">Slide In</MenuItem>
                {/* <MenuItem value="fadeIn 1.5s ease-in">Fade In</MenuItem> */}
                <MenuItem value="Bounce 1s ease-out">Bounce</MenuItem>
              </Select>
              <TextField
                type="number"
                variant="outlined"
                label="Animation Delay (s)"
                value={elements.find(el => el.id === selectedElement)?.animationDelay ?? ''}
                onChange={(e) => {

                  let inputValue = e.target.value;
                  if (inputValue.length > 2) {
                    inputValue = inputValue.slice(0, 2);
                  }
                  const value = inputValue === '' ? "" : parseFloat(inputValue);
                  updateElement(selectedElement, { animationDelay: value });
                }}
                onKeyDown={(e) => {
                  if (e.key === 'Backspace' && e.target.value === '0') {
                    updateElement(selectedElement, { animationDelay: '' });
                  }
                }}
                fullWidth
                style={{ marginTop: "10px" }}
                inputProps={{
                  maxLength: 2,
                  min: 0,
                  max: 99
                }}
              />
            </Paper>}
            {selectedType?.type === 'image' &&
              <Paper style={{ padding: "10px", marginTop: "10px" }}>
                <Box>
                  <Button
                    id="UploadImageId"
                    variant="contained"
                    component="label"
                    color="primary"
                    startIcon={<CloudUploadTwoToneIcon />}
                  >
                    Upload Images
                    <input
                      type="file"
                      accept="image/*"
                      hidden
                      multiple
                      onChange={handleFileChange}
                    />
                  </Button>
                  <Typography variant='body2' style={{ marginTop: '3px' }}>
                    1920 x 1080 only image size allowed
                  </Typography>
                </Box>

                {/* <Select
                  value={elements.find(el => el.id === selectedElement)?.backgroundSize || "cover"}
                  onChange={(e) => updateElement(selectedElement, { backgroundSize: e.target.value })}
                  fullWidth
                  style={{ marginTop: "10px" }}
                >
                  <MenuItem value="cover">Cover</MenuItem>
                  <MenuItem value="contain">Contain</MenuItem>
                  <MenuItem value="auto">Auto</MenuItem>
                </Select>
                <Select
                  value={elements.find(el => el.id === selectedElement)?.backgroundPosition || "center"}
                  onChange={(e) => updateElement(selectedElement, { backgroundPosition: e.target.value })}
                  fullWidth
                  style={{ marginTop: "10px" }}
                >
                  <MenuItem value="left">Left</MenuItem>
                  <MenuItem value="center">Center</MenuItem>
                  <MenuItem value="right">Right</MenuItem>
                </Select> */}

                {/* <Box>
                  <TextField type="number" variant="outlined" label="Border Width"
                    value={selectedType?.borderWidth || 1}
                    onChange={(e) => updateElement(selectedElement, { borderWidth: `${e.target.value}px` })}
                    fullWidth style={{ flex: '1' }} />
                </Box> */}

                {/* <TextField name="Border Color" variant="outlined" type="color"
                  value={selectedType?.borderColor || "#000000"}
                  onChange={(e) => updateElement(selectedElement, { borderColor: e.target.value })}
                  fullWidth style={{ marginTop: "10px" }} /> */}

                {/* <TextField type="number" variant="outlined" label="Padding"
                  value={selectedType?.padding || 10}
                  onChange={(e) => updateElement(selectedElement, { padding: `${e.target.value}px` })}
                  fullWidth style={{ marginTop: "10px" }} /> */}
                <Typography>Animation Style</Typography>
                <Select
                  value={elements.find(el => el.id === selectedElement)?.animation || "none"}
                  onChange={(e) => updateElement(selectedElement, { animation: e.target.value })}
                  fullWidth
                  style={{ marginTop: "10px" }}
                >
                  <MenuItem value="none">None</MenuItem>
                  {/* <MenuItem value="slideIn 0.5s ease forwards">Slide out</MenuItem> */}
                  <MenuItem value="slideIn 1s ease-out">Slide In</MenuItem>
                  {/* <MenuItem value="fadeIn 1.5s ease-in">Fade In</MenuItem> */}
                  <MenuItem value="Bounce 1s ease-out">Bounce</MenuItem>
                </Select>
                <TextField
                  type="number"
                  variant="outlined"
                  label="Animation Delay (s)"
                  value={elements.find(el => el.id === selectedElement)?.animationDelay ?? ''}
                  onChange={(e) => {

                    let inputValue = e.target.value;
                    if (inputValue.length > 2) {
                      inputValue = inputValue.slice(0, 2);
                    }
                    const value = inputValue === '' ? "" : parseFloat(inputValue);
                    updateElement(selectedElement, { animationDelay: value });
                  }}
                  onKeyDown={(e) => {
                    if (e.key === 'Backspace' && e.target.value === '0') {
                      updateElement(selectedElement, { animationDelay: '' });
                    }
                  }}
                  fullWidth
                  style={{ marginTop: "10px" }}
                  inputProps={{
                    maxLength: 2,
                    min: 0,
                    max: 99
                  }}
                />
              </Paper>}

            {selectedType?.shapeType === "arrow" && (
              <Paper style={{ padding: "10px", marginTop: "10px" }}>
                <Typography variant="h6">Arrow Settings</Typography>
                <ButtonGroup fullWidth variant="outlined" style={{ marginTop: "10px" }}>
                  <Button onClick={(e) => updateElement(selectedElement, { arrowDirection: "left" })}><ArrowBack /></Button>
                  <Button onClick={(e) => updateElement(selectedElement, { arrowDirection: "right" })}><ArrowForward /></Button>
                  <Button onClick={(e) => updateElement(selectedElement, { arrowDirection: "up" })}><ArrowUpward /></Button>
                  <Button onClick={(e) => updateElement(selectedElement, { arrowDirection: "down" })}><ArrowDownward /></Button>
                </ButtonGroup>

                <Select
                  value={elements.find((el) => el.id === selectedElement)?.arrowHead || "none"}
                  onChange={(e) => updateElement(selectedElement, { arrowHead: e.target.value })}
                  fullWidth
                  style={{ marginTop: "10px" }}
                >
                  <MenuItem value="none">No Arrowhead</MenuItem>
                  <MenuItem value="start">Start Arrowhead</MenuItem>
                  <MenuItem value="end">End Arrowhead</MenuItem>
                  <MenuItem value="both">Both Ends</MenuItem>
                </Select>

                <TextField
                  name="Arrow Color"
                  variant="outlined"
                  type="color"
                  value={elements.find((el) => el.id === selectedElement)?.stroke || "#000000"}
                  onChange={(e) => updateElement(selectedElement, { stroke: e.target.value })}
                  fullWidth
                  style={{ marginTop: "10px" }}
                />

                <TextField
                  type="number"
                  variant="outlined"
                  label="Arrow Thickness"
                  value={elements.find((el) => el.id === selectedElement)?.strokeWidth || 2}
                  onChange={(e) => updateElement(selectedElement, { strokeWidth: parseInt(e.target.value, 10) })}
                  fullWidth
                  style={{ marginTop: "10px" }}
                />
                <Typography>Animation Style</Typography>
                <Select
                  value={elements.find(el => el.id === selectedElement)?.animation || "none"}
                  onChange={(e) => updateElement(selectedElement, { animation: e.target.value })}
                  fullWidth
                  style={{ marginTop: "10px" }}
                >
                  <MenuItem value="none">None</MenuItem>
                  {/* <MenuItem value="slideIn 0.5s ease forwards">Slide out</MenuItem> */}
                  <MenuItem value="slideIn 1s ease-out">Slide In</MenuItem>
                  {/* <MenuItem value="fadeIn 1.5s ease-in">Fade In</MenuItem> */}
                  <MenuItem value="Bounce 1s ease-out">Bounce</MenuItem>
                </Select>
                <TextField
                  type="number"
                  variant="outlined"
                  label="Animation Delay (s)"
                  value={elements.find(el => el.id === selectedElement)?.animationDelay ?? ''}
                  onChange={(e) => {

                    let inputValue = e.target.value;
                    if (inputValue.length > 2) {
                      inputValue = inputValue.slice(0, 2);
                    }
                    const value = inputValue === '' ? "" : parseFloat(inputValue);
                    updateElement(selectedElement, { animationDelay: value });
                  }}
                  onKeyDown={(e) => {
                    if (e.key === 'Backspace' && e.target.value === '0') {
                      updateElement(selectedElement, { animationDelay: '' });
                    }
                  }}
                  fullWidth
                  style={{ marginTop: "10px" }}
                  inputProps={{
                    maxLength: 2,
                    min: 0,
                    max: 99
                  }}
                />
              </Paper>
            )}
            {(selectedType?.shapeType === "circle" || selectedType?.shapeType === "rectangle" || selectedType?.shapeType === "triangle" || selectedType?.shapeType === "hexagon" || selectedType?.shapeType === "square" || selectedType?.shapeType === "pentagon") && (
              <Paper style={{ padding: "10px", marginTop: "10px" }}>
                <Typography variant="h6">Shape Properties</Typography>
                {/* <Typography variant="body 2">color</Typography>
               <TextField
                  name="Stroke Color"
                  variant="outlined"
                  type="color"
                  label="Stroke Color"
                  value={selectedType?.stroke || "#000000"}
                  onChange={(e) => updateElement(selectedElement, { stroke: e.target.value })}
                  fullWidth
                  style={{ marginTop: "10px" }}
                /> */}
                <Typography variant="body 2">backgroundColor</Typography>

                <TextField
                  name="backgroundColor"
                  variant="outlined"
                  type="color"
                  label="Background Color"
                  value={selectedType?.fill}
                  onChange={(e) => {
                    // console.log("Setting background color to:", e.target.value, selectedType?.fill);
                    updateElement(selectedElement, { fill: e.target.value });
                  }}
                  fullWidth
                  style={{ marginTop: "10px" }}
                />

                {/* <TextField
                  type="number"
                  variant="outlined"
                  label="Stroke Width"
                  value={selectedType?.strokeWidth || 2}
                  onChange={(e) => updateElement(selectedElement, { strokeWidth: parseInt(e.target.value, 10) })}
                  fullWidth
                  style={{ marginTop: "10px" }}
                />

                <Select
                  value={selectedType?.lineStyle || "solid"}
                  onChange={(e) => updateElement(selectedElement, { lineStyle: e.target.value })}
                  fullWidth
                  style={{ marginTop: "10px" }}
                >
                  <MenuItem value="solid">Solid</MenuItem>
                  <MenuItem value="dashed">Dashed</MenuItem>
                  <MenuItem value="dotted">Dotted</MenuItem>
                </Select> */}



                <Typography variant="body2" style={{ marginTop: "10px" }}>Formula</Typography>
                <TextField
                  variant="outlined"
                  fullWidth
                  value={selectedType?.formula}
                  style={{ marginTop: "0px" }}
                  onChange={(e) => {
                    const isFirstEntry = !selectedType?.formula && e.target.value;
                    updateElement(selectedElement, {
                      formula: e.target.value,
                      ...(isFirstEntry && { formulaColor: "#ffffff" })
                    });
                  }}
                />
                {selectedType?.formula && (
                  <>

                    <Typography variant="body2" style={{ marginTop: "10px" }}>Formula Color</Typography>
                    <TextField
                      variant="outlined"
                      type="color"
                      label="Text Color"
                      value={selectedType?.formulaColor || "#ffffff"}
                      onChange={(e) => {
                        const isFirstEntry = !selectedType?.formula && e.target.value;
                        updateElement(selectedElement, {
                          formulaColor: e.target.value
                        });
                        console.log("check here", selectedType?.formulaColor);
                      }}
                      fullWidth
                      style={{ marginTop: "10px" }}
                    />
                  </>
                )}
                <Typography style={{ marginTop: "10px" }}>Animation Style</Typography>
                <Select
                  value={elements.find(el => el.id === selectedElement)?.animation || "none"}
                  onChange={(e) => updateElement(selectedElement, { animation: e.target.value })}
                  fullWidth
                  style={{ marginTop: "10px" }}
                >
                  <MenuItem value="none">None</MenuItem>
                  {/* <MenuItem value="slideIn 0.5s ease forwards">Slide out</MenuItem> */}
                  <MenuItem value="slideIn 1s ease-out">Slide In</MenuItem>
                  {/* <MenuItem value="fadeIn 1.5s ease-in">Fade In</MenuItem> */}
                  <MenuItem value="Bounce 1s ease-out">Bounce</MenuItem>
                </Select>
                <TextField
                  type="number"
                  variant="outlined"
                  label="Animation Delay (s)"
                  value={elements.find(el => el.id === selectedElement)?.animationDelay ?? ''}
                  onChange={(e) => {

                    let inputValue = e.target.value;
                    if (inputValue.length > 2) {
                      inputValue = inputValue.slice(0, 2);
                    }
                    const value = inputValue === '' ? "" : parseFloat(inputValue);
                    updateElement(selectedElement, { animationDelay: value });
                  }}
                  onKeyDown={(e) => {
                    if (e.key === 'Backspace' && e.target.value === '0') {
                      updateElement(selectedElement, { animationDelay: '' });
                    }
                  }}
                  fullWidth
                  style={{ marginTop: "20px" }}
                  inputProps={{
                    maxLength: 2,
                    min: 0,
                    max: 99
                  }}
                />
              </Paper>
            )}
          </>
          :
          <Box
            display="flex"
            flexDirection="column"
            gap={2}
            p={2}
          >
            <Box display="flex" gap={2}>
              {[
                { textName: "Text", action: addTextElement, icon: <TitleIcon style={{ background: 'transparent' }} /> },
                { textName: "Image", action: addImageElement, icon: <ImageIcon style={{ position: 'relative', background: 'transparent', right: '-5px' }} /> },
                { textName: "Formula", action: addFormulaElement, icon: <FunctionsTwoToneIcon style={{ background: 'transparent' }} /> }

              ].map((btn, index) => (
                <Button
                  key={index}
                  variant="contained"
                  onClick={btn.action}
                  fullWidth
                  disabled={previewMode}
                  startIcon={btn.icon}
                  style={{

                    minWidth: btn.textName === "Image" ? '76px' : '62px',
                    backgroundColor: 'rgb(211, 211, 211)',
                    color: 'rgba(0, 0, 0, 0.7)',
                    border: '1px solid #bbbbbb',
                    borderRadius: '4px',
                  }}
                  id={`button-${btn.textName}`}
                  onMouseOver={handleMouseOver}
                  onMouseOut={handleMouseOut}
                >
                  {btn.text}
                </Button>
              ))}
            </Box>
            <Button
              variant="contained"
              fullWidth
              disabled={previewMode}
              startIcon={<InterestsIcon style={{ background: 'transparent' }} />}
              onClick={() => setBackgroundImage(!backgroundImage)}
              style={{
                minWidth: '75px',
                backgroundColor: 'rgb(211, 211, 211)',
                color: 'rgba(0, 0, 0, 0.7)',
                border: '1px solid #bbbbbb',
                borderRadius: '4px',
              }}
              id='BackgroundImage'
              onMouseOver={handleMouseOver}
              onMouseOut={handleMouseOut}
            >
              Background Image
            </Button>

            {backgroundImage && (
              <Paper style={{ padding: "10px", marginTop: "0px", paddingTop: '2px' }}>
                <Typography variant="h6">Background Image Settings</Typography>

              {backgroundImageSettings && backgroundImageSettings?.url?
                <Button
                  variant="contained"
                  component="label"
                  fullWidth
                  style={{
                    marginTop: "10px", lineHeight: '1.2', marginBottom: '10px',
                    backgroundColor: "#d3d3d3",
                    borderRadius: '4px',
                    border: '1px solid rgb(187, 187, 187)',
                    color: 'rgba(0, 0, 0, 0.7)',

                  }}
                  onClick={handleBackgroundImageDelete}
                  id=' UploadBackgroundImage'
                >
                  delete Background Image
                  
                </Button>:
                <Button
                  variant="contained"
                  component="label"
                  fullWidth
                  style={{
                    marginTop: "10px", lineHeight: '1.2', marginBottom: '10px',
                    backgroundColor: "#d3d3d3",
                    borderRadius: '4px',
                    border: '1px solid rgb(187, 187, 187)',
                    color: 'rgba(0, 0, 0, 0.7)',

                  }}
                  id=' UploadBackgroundImage'
                >
                  Upload Background Image
                  <input
                    type="file"
                    hidden
                    accept="image/*"
                    onChange={handleBackgroundImageUpload}
                  />
                </Button>}

               

                {/* <Select
                  value={backgroundImageSettings?.size}
                  onChange={(e) => setBackgroundImageSettings(prev => ({
                    ...prev,
                    size: e.target.value
                  }))}
                  fullWidth
                  style={{ marginTop: "10px" }}
                >
                  <MenuItem value="cover">Cover</MenuItem>
                  <MenuItem value="contain">Contain</MenuItem>
                  <MenuItem value="auto">Auto</MenuItem>
                </Select>

                <Select
                  value={backgroundImageSettings?.position}
                  onChange={(e) => setBackgroundImageSettings(prev => ({
                    ...prev,
                    position: e.target.value
                  }))}
                  fullWidth
                  style={{ marginTop: "10px" }}
                >
                  <MenuItem value="center">Center</MenuItem>
                  <MenuItem value="top">Top</MenuItem>
                  <MenuItem value="bottom">Bottom</MenuItem>
                  <MenuItem value="left">Left</MenuItem>
                  <MenuItem value="right">Right</MenuItem>
                </Select> 

                <Select
                  value={backgroundImageSettings?.repeat}
                  onChange={(e) => setBackgroundImageSettings(prev => ({
                    ...prev,
                    repeat: e.target.value
                  }))}
                  fullWidth
                  style={{ marginTop: "10px" }}
                >
                  <MenuItem value="no-repeat">No Repeat</MenuItem>
                  <MenuItem value="repeat">Repeat</MenuItem>
                  <MenuItem value="repeat-x">Repeat X</MenuItem>
                  <MenuItem value="repeat-y">Repeat Y</MenuItem>
                </Select> */}
              </Paper>
            )}

            <Box display="flex" gap={2} marginTop="10px">
              <Button
                variant="contained"
                onClick={() => setShapeSelected(!shapeSelected)}
                fullWidth
                disabled={previewMode}
                startIcon={<InterestsIcon />}
                style={{
                  minWidth: '75px',
                  backgroundColor: 'rgb(211, 211, 211)',
                  color: 'rgba(0, 0, 0, 0.7)',
                  border: '1px solid #bbbbbb',
                  borderRadius: '4px',
                }}
                id='Shapes'
                onMouseOver={handleMouseOver}
                onMouseOut={handleMouseOut}
              >
                {'Shapes'}
              </Button>
            </Box>

            {shapeSelected && (
              <>
                <Box marginTop="10px" display="flex" flexWrap="wrap" justifyContent="space-between">
                  {[
                    { text: "Arrow", type: "arrow", icon: <ArrowForwardOutlinedIcon /> },
                    { text: "Triangle", type: "triangle", icon: <ChangeHistoryIcon /> },
                    { text: "Rectangle", type: "rectangle", icon: <Crop32Icon /> },
                    { text: "Circle", type: "circle", icon: <Brightness1OutlinedIcon /> },
                    { text: "Hexagon", type: "hexagon", icon: <HexagonIcon /> },
                    { text: "Pentagon", type: "pentagon", icon: <InterestsIcon /> },
                  ].map((shape, index) => (
                    <Button
                      key={index}
                      variant="contained"
                      onClick={() => addShapeElement(shape.type, '#d3d3d3')}
                      style={{
                        width: 'calc(50% - 5px)',
                        backgroundColor: 'rgb(211, 211, 211)',
                        color: 'rgba(0, 0, 0, 0.7)',
                        marginBottom: '10px',
                        border: '1px solid #bbbbbb',
                        borderRadius: '4px',
                      }}
                      id={`button-${shape.text}`}
                      startIcon={shape.icon}
                      onMouseOver={handleMouseOver}
                      onMouseOut={handleMouseOut}
                    >
                      {shape.text}
                    </Button>
                  ))}
                </Box>
              </>
            )}
            {/* <Button style={{
              minWidth: '75px',
              backgroundColor: 'rgb(211, 211, 211)',
              color: 'rgba(0, 0, 0, 0.7)',
              border: '1px solid #bbbbbb',
              borderRadius: '4px',
            }}
              id="AnimationEffects"
              disabled={previewMode}
              onClick={() => setAnimationType(!animationType)}
            >Animation Effects</Button> */}

            {animationType && (
              <>
                <Box marginTop="10px" display="flex" flexWrap="wrap" justifyContent="space-between" style={{ color: "#000" }}>
                  {[
                    { text: "None", icon: <NoneIcon /> },
                    { text: "Bounce", icon: <BounceIcon /> },
                    { text: "SlideIn", icon: <SlideIcon /> },
                    { text: "Delay", icon: <DelayIcon /> },
                  ].map((shape, index) => (
                    <Button
                      key={index}
                      startIcon={shape.icon}
                      onClick={() => handleAnimationClick(shape.text)}
                      style={{
                        minWidth: '110px',
                        backgroundColor: 'rgb(211, 211, 211)',
                        color: 'rgba(0, 0, 0, 0.7)',
                        marginBottom: '10px',
                        border: '1px solid #bbbbbb',
                        borderRadius: '4px',
                      }}
                      id={`button-${shape.text}`}

                    >
                      {shape.text}
                    </Button>
                  ))}
                </Box>
              </>
            )}


            <Button
              variant="contained"
              color={previewMode ? "secondary" : "primary"}
              onClick={() => { setPreviewMode(!previewMode); setSelectedElement(null) }}
              fullWidth
              style={{
                minWidth: '75px',
                backgroundColor: 'rgb(211, 211, 211)',
                color: 'rgba(0, 0, 0, 0.7)',
                border: '1px solid #bbbbbb',
                borderRadius: '4px',
              }}
              id={previewMode ? 'previewOn' : 'previewOff'}
              onMouseOver={(e) => { e.target.style.backgroundColor = "#bdbdbd"; }}
              onMouseOut={(e) => { e.target.style.backgroundColor = "#d3d3d3"; }}

            >
              {previewMode ? "Exit Preview" : "Preview"}
            </Button>

            <Button
              variant="contained"

              onClick={() => { exportToHTML() }}
              fullWidth
              style={{
                backgroundColor: "#FE7000 !important",
                borderRadius: '4px',
                color: '#fff !important',
              }}
              id=' ExportToHTML'
              onMouseOver={(e) => { e.currentTarget.style.backgroundColor = "#00b673"; }}
              onMouseOut={(e) => { e.currentTarget.style.backgroundColor = "#FE7000"; }}
            >
              Export to HTML
            </Button>
            {elementError && <p style={{ color: 'red', marginBottom: '10px' }}>{elementError}</p>}

          </Box>
        }
      </Grid>
    </Grid>
  );
};

export default CustomTextEditor;
