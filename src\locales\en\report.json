{"trialUsers": "Trial Users", "dashboard": "Dashboard", "subscribedUsers": "Subscribed users", "userCourseProgress": "User course progress", "all": "All", "active": "Active", "canceled": "Canceled", "expired": "Expired", "status": "Status", "plan": "Plan", "createdOn": "Created On", "customer": "Customer", "course": "Course", "courseProgress": "Course progress", "subscribedOn": "Subscribed on", "simulationStarted": "Simulation started", "lastSimulationDate": "Last simulation date", "caseStudyAttempted": "Case study attempted", "caseStudyStartedOn": "Case study started on", "caseStudyResult": "Case study result", "certificateSent": "Certificate sent", "subscriptionStatus": "Subscription Status", "phone": "Phone", "courses": "Courses", "currentPeriodStart": "Current Period Start", "currentPeriodEnd": "Current Period End", "created": "Created", "rowsPerPage": "Rows per page:", "jumpToPage": "Jump to Page:"}