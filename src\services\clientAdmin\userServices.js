import client from '../apiClient';

const getUsersDetails = (page,rowsPerPage,search) => client.get(`/client/users?page=${page}&size=${rowsPerPage}&search=${search}`);
const getCourseList = () => client.get(`/course/screen/allCourseList`);

const postUsersDetails = (data) => client.post(`/client/user`, data);

const deleteUserDetailsByID = (userId) => client.delete(`/client/user?userId=${userId}`);

const editUserDetailsByID = (userId, data) => client.put(`/client/user?userId=${userId}`, data);

const getUserDetalsById = (userId) => client.get(`/client/users/${userId}`);
const getUserDetalsByIdIndividual = (userId) => client.get(`/client/userdetails?userId=${userId}`);

const importUserDetails = (data) => client.post(`/user/importUserInfo`, data);

export default {
  getUsersDetails,
  postUsersDetails,
  deleteUserDetailsByID,
  editUserDetailsByID,
  getUserDetalsById,
  getUserDetalsByIdIndividual,
  importUserDetails,
  getCourseList
};
