import React, { Component } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, But<PERSON>, Box, Tooltip, IconButton } from '@mui/material';
import { makeStyles } from '@mui/styles';
import PlayCircleIcon from '@mui/icons-material/PlayCircle';
import { Icon } from '@iconify/react';

// slider
import Slider from 'react-slick';

// file
import PythoniLargeImage from '../images/PythoniLargeImage.png';

const SliderCarousel = ({ handleClick, image, imagePlayButton, imagePlayButtonOne }) => {
  const classes = useStyles();
  const ArrowLeft = (props) => (
    <div {...props} className={props.prev}>
      <Icon icon="material-symbols:arrow-back-ios-new-rounded" className={classes.iconifystyleArrows} />
    </div>
  );
  const ArrowRight = (props) => (
    <div {...props} className={props.next} style={{ direction: 'rtl' }}>
      <Icon icon="material-symbols:arrow-forward-ios-rounded" className={classes.iconifyRightArrow} />
    </div>
  );

  const settings = {
    dots: false,
    infinite: true,
    slidesToShow: 1,
    slidesToScroll: 1,
    autoplay: true,
    autoplaySpeed: 2500,
    pauseOnHover: true,
    nextArrow: <ArrowRight />,
    prevArrow: <ArrowLeft />,
    responsive: [
      {
        breakpoint: 480,
        settings: {
          slidesToShow: 1,
          slidesToScroll: 1,
          infinite: true,
          dots: true,
        },
      },
    ],
  };
  return (
    <div>
      <Slider {...settings}>
        <div>
          <span>{image && image}</span>
        </div>

        <div>
          <span>{imagePlayButton && imagePlayButton}</span>
        </div>
        {imagePlayButtonOne && (
          <div>
            <span>{imagePlayButtonOne}</span>
          </div>
        )}
      </Slider>
    </div>
  );
};

const useStyles = makeStyles((theme) => ({
  iconifyRightArrow: {
    // display: 'none',
    color: '#FE7000',
    minWidth: '25px',
    height: '25px',
    position: 'absolute',
    top: '50%',
    // display: 'block',
    transform: 'translate(0, -50%);',
    cursor: 'pointer;',
    marginRight: '0px',

    [theme.breakpoints.down('sm')]: {
      display: 'none',
    },
  },
  iconifystyleArrows: {
    // display: 'none',
    color: '#FE7000',
    minWidth: '25px',
    height: '25px',
    position: 'absolute',
    top: '50%',
    // display: 'block',
    transform: 'translate(0, -50%);',
    marginLeft: '22px',
    zIndex: '999 ',
    cursor: 'pointer;',
    [theme.breakpoints.down('sm')]: {
      display: 'none',
    },
  },
  playbutton: {
    position: 'absolute',
  },
  buttonAlignment: {
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
  },
  playicon: {
    color: '#FE7000',
    padding: '0px',
    borderRadius: '50%',
    animation: '$pulse 1.5s infinite',
    '&:hover': {
      cursor: 'pointer',
    },
  },
}));
export default SliderCarousel;
