/* eslint-disable no-unneeded-ternary */
/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable consistent-return */
import React, { useState, useEffect, useRef } from 'react';
import { Avatar, Box, Button, Checkbox, FormControlLabel, Grid, Paper, TextField, Typography, Pagination, Tab, Tabs, TextareaAutosize, Container, Card, MenuItem, Tooltip, Chip } from '@mui/material';
import { useSearchParams, useNavigate } from 'react-router-dom';
import { Formik } from 'formik';
import * as Yup from 'yup';
import Table from '@mui/material/Table';
import moment from 'moment';
import { useSelector, useDispatch } from 'react-redux';
import { LocalizationProvider, DatePicker } from '@mui/x-date-pickers';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import { LoadingButton } from '@mui/lab';
import Page from '../../../components/Page';
import PageHeader from '../../../components/PageHeader';
import adminServices from '../../../services/adminServices'
import PhoneNumber from '../../../components/PhoneNumber/Index';
import { getIndividualClientUser, getIndividualClientTable } from '../../../Redux/Action'
import ManProfile from '../../../assets/logo/man_user.png'
import SnackBar from '../../../components/snackbar/snackbar'
import {EmptyUserDetails} from '../../../store/reducer'

const UserProfile = () => {

  const dispatch = useDispatch()
  const navigate = useNavigate()

  const clientId = useSelector((state) => state?.userInfo && state?.userInfo?.id);
  const userDetails = useSelector((state) => state?.clientuserDetails);
  
  const [loadingButton, setLoadingButton] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [value, setValue] = React.useState(0);
  const [preview, setPreview] = React.useState(true);
  // const [userDetails, setUserDetails] = React.useState();
  const [noteseData, setNoteseData] = useState('');
  const [showReviewContainer, setShowReviewContainer] = React.useState(false);
  const [reviewData, setReviewData] = React.useState({ Input: '', Output: '', id: '', review_status: "" });
  const [courseList, setCourseList] = useState([]);
  const [languageCode, setLanguage] = useState([]);
  const [openSnackbar, setOpenSnackbar] = React.useState(false);
  const [snackbarTitle, setSnackbarTitle] = React.useState('');
  const [assessmentList, setAssessmentList] = useState([]);
  
  const [loading, setLoading] = useState(false);
  const [initialData, setInitialData] = React.useState({
    first_name: '',
    last_name: '',
    email: '',
    phone: '',
    birth_date: '',
    personal_notes: '',
    created_date: '',
    assigned_course: [],
    assigned_assessment: [],
    special_attention: false,
    languageCode: ''
  });

  

  const [suggestion, setSuggestion] = useState('');

  const handleSuggestionChange = (event) => {
    setSuggestion(event.target.value);
  };
  const getCourseList = async () => {
    try {
      const response = await adminServices.getUserSubscriptionData(clientId);
      if (response.ok) {
        setCourseList(response.data);
      }
    } catch (error) {
      console.log(error);
    }
  };
  const getAssessmentList = async () => {
    try {
      // const response = await clienServices.getCourse();
      const response = await adminServices.getUserAssessmentSubscriptionData(clientId)

      if (response.ok) {
        setAssessmentList(response.data);
        setLoading(false);
      } else {
        setLoading(false);
      }
    } catch (error) {
      console.log(error);
    }
  };

//  const getUserNeetSubscriptionData = async () => {
//     try {
//       const response = await adminServices.getUserNeetSubscriptionData(clientId)

//       if (response.ok) {
//         setAssessmentList(response.data);
//         setLoading(false);
//       } else {
//         setLoading(false);
//       }
//     } catch (error) {
//       console.log(error);
//     }
//   };


  const getLanguageCode = async () => {
    try {
      const response = await adminServices.getLanguageCode();
      if (response.ok) {
        setLanguage(response.data);

        return true;
      }
    } catch (error) {
      console.log(error);
    }
  }

  useEffect(() => {
    const coursedetails = userDetails?.course && Array.isArray(userDetails.course) && userDetails?.course?.filter(
      (item, index, self) =>
        index === self.findIndex((t) => t.id === item.id && t.title === item.title)
    );
    
  
      setInitialData({
        first_name: userDetails.firstName || '',
        last_name: userDetails.lastName || '',
        email: userDetails.email || '',
        phone: userDetails.phone || '',
        birth_date: userDetails.birth_date || '',
        personal_notes: userDetails.personalNotes || [],
        created_date: userDetails.createdDate || '',
        // assigned_course: userDetails?.course ? courseList?.filter((cl) => userDetails?.course?.some((c) => c.id === cl.courseId))?.map((cl) => cl.id) : "",
        // assigned_course :Array.isArray(userDetails?.course) 
        // ? courseList?.filter(cl => userDetails.course.some(c => c.id === cl.courseId)).map(cl => cl.id) ?? [] 
        // : [],
        assigned_course : Array.isArray(coursedetails)? courseList?.filter(cl => coursedetails.some(c => c.id === cl.courseId)).map(cl => cl.id) ?? []: [],
        assigned_assessment: userDetails?.assessment ? assessmentList?.filter((al) => userDetails.assessment?.some((a) => a.id === al.courseId))?.map((al) => al.id) : "",
        special_attention: userDetails.specialAttention || false,
        languageCode: userDetails.languageCode || ''
      })
    
    getAssessmentList();
    // getUserNeetSubscriptionData();
    getCourseList();
    getLanguageCode();
  }, [userDetails])


  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setInitialData(prev => ({
      ...prev,
      [name]: value
    }));
  }

  const handleInputChangePhone = (e) => {
    setInitialData(prev => ({
      ...prev,
      phone: e
    }));
  }


  const handleInputChangeNotes = (e) => {
    setNoteseData(e.target.value);
  };


  const [searchParams] = useSearchParams();

  const notesPerPage = 3;

  const indexOfLastNote = currentPage * notesPerPage;
  const indexOfFirstNote = indexOfLastNote - notesPerPage;
  const inputRef = useRef(null);
  const currentNotes = initialData?.personal_notes?.slice(indexOfFirstNote, indexOfLastNote) || [];
  const handlePageChange = (event, value) => {
    setCurrentPage(value);
  };

  const validation = () => {
    if (!initialData.first_name) {
      setOpenSnackbar(true);
      setSnackbarTitle("Please fill all the required fields");
      return false;
    }

    return true;
  }


  const handleChange = (event, newValue) => {
    setValue(newValue);

    // Example logic: update userDetails based on selected tab
    // if (newValue === 0) {
    //   setUserDetails({ section: 'Profile', data: {/* profile-related data */ } });
    // } else if (newValue === 1) {
    //   setUserDetails({ section: 'Immediate Action', data: {/* action-related data */ } });
    // }
  };



  useEffect(() => {
    getIndividualUser();
  }, [])

  const getIndividualUser = async () => {
    // const response3 = await userServices.getUserDetalsByIdIndividual(searchParams.get('id'))
    dispatch(getIndividualClientUser(searchParams.get('id')))
    // if (response3.ok) {
    //   console.log(response3.data, 'response3');

    //   setUserDetails(response3.data)
    // }
  };


  const getPersonalNotes = (data) => {
    if (!data.personal_notes || data.personal_notes.length === 0) {
      return [];
    }
    const isEmpty = data.personal_notes.every(note =>
      Object.values(note).every(val => val === "")
    );
    return isEmpty ? [] : data.personal_notes;
  };

  const handleUpdateDetails = async () => {

    const processedData = {
      ...initialData,
      personal_notes: getPersonalNotes(initialData)
    };
    


    const result = await adminServices.Updateusercreation(userDetails.id, processedData);
    if (result.ok) {
      setOpenSnackbar(true)
      setSnackbarTitle("User Profile Updated Successfully")
      setTimeout(() => {
        navigate('/app/users')
        dispatch(EmptyUserDetails())
        dispatch(getIndividualClientTable(0, 10, ''))
      }, 2000);
    }
  }

  const handleButtonClick = () => {
    if (preview) {
      setPreview(false)
    }
    else {
      const validId = validation();
      if (validId) {
        handleUpdateDetails();
        setPreview(true)
      }

    }
  }
  

  const handleCancelClick = () => {
    setPreview(true);
    setOpenSnackbar(false);
    setInitialData({
      first_name: userDetails.firstName || '',
      last_name: userDetails.lastName || '',
      email: userDetails.email || '',
      phone: userDetails.phone || '',
      birth_date: userDetails.birth_date || '',
      personal_notes: userDetails.personalNotes || [],
      created_date: userDetails.createdDate || '',
      assigned_course: userDetails?.course ? courseList?.filter((cl) => userDetails.course?.some((c) => c.id === cl.courseId))?.map((cl) => cl.id) : "",
      assigned_assessment: userDetails?.assessment ? assessmentList?.filter((al) => userDetails.assessment?.some((a) => a.id === al.courseId))?.map((al) => al.id) : "",
      special_attention: userDetails.specialAttention || false,
      languageCode: userDetails.languageCode || ''
    })


  };

  const handleUpdateAI = async (action) => {
    setLoadingButton(action);
    try {
      const result = await adminServices.UpdateChatStatus(
        reviewData?.id,
        userDetails?.id,
        action,
        suggestion
      );
      if (result.ok) {
        setOpenSnackbar(true);
        setSuggestion('');
        setSnackbarTitle("Status Updated Successfully!");
      
        setTimeout(() => {
          
          navigate('/app/users');
          dispatch(EmptyUserDetails())
        }, 2000);
      }
    } catch (error) {
      console.error("Error updating status:", error);
    } finally {
      setLoadingButton('');
    }
  };
  


  const handleReviewBtn = (data) => {
    setShowReviewContainer(true);
    setReviewData({ Input: data?.prompt, Output: data?.model_output, id: data?.id, review_status: data.review_status });


    setTimeout(() => {
      if (inputRef.current) {
        inputRef.current.scrollIntoView({ behavior: 'smooth', block: 'center' });
        inputRef.current.focus();
      }
    }, 300);
  }

  const handleNoteInput = () => {
    if (!noteseData.trim()) return;

    const newNote = {
      date: new Date().toISOString(),
      value: noteseData.trim(),
    };

    setInitialData((prev) => ({
      ...prev,
      personal_notes: [...(prev.personal_notes || []), newNote],
    }));
    setNoteseData('');
  };


  const hasValidNotes = initialData?.personal_notes !== '' && initialData?.personal_notes?.some(
    note => note.date || note.value
  );


  const getStatusColor = (status) => {
    if (status === 'Pending') return 'warning';
    if (status === 'Approved') return 'success';
    return 'error';
  };



  return (
    <Page title="User Profile">
      <PageHeader pageTitle={'User Profile'} />
      <Paper elevation={1} sx={{ borderRadius: 0, mt: 2, boxShadow: 'none !important' }}>
        <Tabs
          value={value}
          onChange={handleChange}
          variant="scrollable"
          scrollButtons="auto"
          textColor="primary"
          indicatorColor="primary"
          aria-label="tabs"
          sx={{
            borderBottom: 1,
            borderColor: 'divider',
            mb: 2,
            '& .MuiTab-root': {
              textTransform: 'none',
              fontWeight: 500,
              minWidth: 100,
            },
          }}
        >
          <Tab label="Profile" value={0} />
          {initialData.special_attention &&
            <Tab label="Immediate Action " value={1} />}
        </Tabs>


        {value === 0 && (
          <Box sx={{ boxShadow: 'none !important'}}>
            <Box sx={{ p: 4 , boxShadow: 'none !important'}}>
              <Typography variant="h5" gutterBottom>{userDetails?.firstName} {userDetails?.lastName} </Typography>
              <Box sx={{ display: 'flex', gap: 4, flexWrap: 'wrap', padding: '15px 10px 0px !important' }}>
                <Paper elevation={3} sx={{ p: 3, flex: 2, minWidth: 400 }}>
                  <Grid container spacing={2}>
                    <Grid item xs={12} sm={4} md={3}>
                      <Avatar
                        src={ManProfile}
                        alt="Profile"
                        sx={{ width: 120, height: 120, mx: 'auto' }}
                      />
                    </Grid>
                    {userDetails && <Grid item xs={12} sm={8} md={9}>
                      <Formik
                        enableReinitialize
                        initialValues={initialData}
                        validationSchema={Yup.object().shape({
                          firstName: Yup.string().trim().min(3).matches(/^[A-Za-z ]+$/, "Only alphabets allowed").required('Required'),
                          // phone: Yup.string()
                          // .required('Required')
                          // .matches(/^\+?[1-9]\d{1,14}$/, 'Invalid phone number'),
                          email: Yup.string().required('Required').email()
                        })}>

                        <Grid key={userDetails.id} container spacing={2}>

                          <Grid item xs={6}>
                            <TextField fullWidth
                              label="First Name"
                              name="first_name"
                              value={initialData.first_name}
                              onChange={handleInputChange}
                              disabled={preview} />
                          </Grid>
                          <Grid item xs={6}>
                            <TextField fullWidth
                              label="Last Name"
                              name="last_name"
                              value={initialData.last_name}
                              onChange={handleInputChange}
                              disabled={preview} />
                          </Grid>
                          <Grid item xs={6}>
                            <TextField fullWidth
                              label="Email"
                              name="email"
                              value={initialData.email}
                              // onChange={handleInputChange}
                              disabled />
                          </Grid>
                          <Grid item xs={6}>
                            <PhoneNumber value={initialData.phone} onChange={handleInputChangePhone} disabled={preview} fullWidth country="us" specialLabel="" />
                          </Grid>
                          <Grid item xs={12} sm={6}>
                            <LocalizationProvider dateAdapter={AdapterDateFns}>
                              <DatePicker
                                disabled={preview}
                                value={initialData.birth_date ? new Date(initialData.birth_date) : null}
                                onChange={(date) => handleInputChange({ target: { name: 'birth_date', value: date?.toISOString() } })}
                                maxDate={new Date()}
                                slotProps={{ textField: { fullWidth: true } }}
                                renderInput={(params) => (
                                  <TextField
                                    {...params}
                                    placeholder="MM/DD/YYYY"
                                    inputProps={{
                                      ...params.inputProps,
                                      readOnly: true,
                                    }}
                                    fullWidth
                                    sx={{
                                      'input::placeholder': {
                                        textTransform: 'uppercase',
                                      },
                                      '& .MuiOutlinedInput-root': {
                                        '& fieldset': { borderColor: 'black' },
                                        '&:hover fieldset': { borderColor: 'black' },
                                        '&.Mui-focused fieldset': { borderColor: 'black' },
                                      },
                                    }}
                                    disabled={preview}
                                  />
                                )}
                              />
                            </LocalizationProvider>
                          </Grid>

                          <Grid item xs={12} sm={6}>
                            <TextField
                              select
                              fullWidth
                              label="Language"
                              name="languageCode"
                              value={initialData?.languageCode}
                              onChange={handleInputChange}
                              variant="outlined"
                              disabled={preview}
                            >
                              {languageCode && languageCode?.length > 0 && languageCode?.map((option) => (
                                <MenuItem key={option.value} value={option.value}>
                                  {option.code.charAt(0).toUpperCase() + option.code.slice(1)}
                                </MenuItem>
                              ))}
                            </TextField>
                          </Grid>

                          <Grid item xs={12}>
                            <TextField
                              fullWidth
                              select
                              label="Assigned Course"
                              SelectProps={{
                                multiple: true
                              }}
                              value={initialData?.assigned_course || []}
                              disabled={preview}
                              onChange={(e) => {
                                const { value } = e.target;
                                setInitialData((prev) => ({
                                  ...prev,
                                  assigned_course: typeof value === 'string' ? value.split(',') : value
                                }));
                              }}
                            >
                              {courseList &&
                                courseList.length > 0 &&
                                courseList.map((option) => (
                                  <MenuItem disabled={+option?.noOfLicenseAllocated === +option?.noOfLicenseAssigned} key={option.id} value={option.id}>
                                    {option.courseName ? option.courseName :option.planName}
                                  </MenuItem>
                                ))}

                            </TextField>
                          </Grid>
                          <Grid item xs={12}>
                            <TextField
                              fullWidth
                              select
                              label="Assigned Assessment"
                              SelectProps={{
                                multiple: true
                              }}
                              value={initialData?.assigned_assessment || []}
                              disabled={preview}
                              onChange={(e) => {
                                const { value } = e.target;
                                setInitialData((prev) => ({
                                  ...prev,
                                  assigned_assessment: typeof value === 'string' ? value.split(',') : value
                                }));
                              }}
                            >
                              {assessmentList &&
                                assessmentList.length > 0 &&
                                assessmentList.map((option1) => (
                                  <MenuItem disabled={+option1?.noOfLicenseAllocated === +option1?.noOfLicenseAssigned} key={option1.id} value={option1.id}>
                                    {/* {option1?.assessmentTitle} */}
                                    {/* {option1?.planName || option1?.assessmentTitle || option1?.title} */}
                                    {/* {option1?.assessmentTitle || option1?.planName} */}
                                    {option1?.assessmentTitle || option1.title || option1.planName}

                                    </MenuItem>
                                ))}

                            </TextField>
                          </Grid>


                          <Grid item xs={12}>
                            <FormControlLabel
                              control={
                                <Checkbox
                                  checked={initialData.special_attention || false}
                                  onChange={(e) => {
                                    setInitialData(prev => ({
                                      ...prev,
                                      special_attention: e.target.checked
                                    }));
                                  }}
                                  disabled={preview}
                                />
                              }
                              label="User needs special attention"
                            />
                          </Grid>
                          {initialData.special_attention && !preview && (
                            <>
                              <Grid item xs={12}>
                                <Typography variant="subtitle1">Personal Notes</Typography>
                                <TextareaAutosize
                                  minRows={4}
                                  style={{ width: '100%', padding: 10 }}
                                  name="personal_notes"
                                  value={noteseData}
                                  onChange={handleInputChangeNotes}
                                  disabled={preview}
                                />
                              </Grid>
                              <Grid>
                                <Button onClick={handleNoteInput}>Add Notes</Button>
                              </Grid>
                            </>
                          )}

                        </Grid>
                      </Formik>
                    </Grid>}

                    {preview ? <Grid item xs={12} sx={{ display: 'flex', justifyContent: 'center', gap: 2 }}>
                      <Button variant="contained" color="primary" onClick={handleButtonClick}>Edit</Button>
                    </Grid> :
                      <Grid item xs={12} sx={{ display: 'flex', justifyContent: 'center', gap: 2 }}>
                        <Button variant="outlined" color="error" onClick={handleCancelClick}>Cancel</Button>
                        <Button variant="contained" color="primary" onClick={handleButtonClick}>Save Changes</Button>
                      </Grid>
                    }
                  </Grid>
                </Paper>

                <Paper elevation={3} sx={{ p: 3, flex: 1, minWidth: 300 }}>
                  <Typography variant="h6" gutterBottom>Notes List</Typography>

                  {hasValidNotes && <Box sx={{ minHeight: 400, maxHeight: 400, overflowY: 'auto', mb: 2 }}>
                    {currentNotes.length !== 0 &&
                      currentNotes.map((note, index) => (
                        <Card
                          key={index}
                          variant="outlined"
                          sx={{
                            mb: 2,
                            p: 2,
                            boxShadow: 2,
                            borderRadius: 1,
                            backgroundColor: '#f9f9f9'
                          }}
                        >
                          <Typography variant="caption" color="textSecondary" gutterBottom>
                            {note.date ? moment(note.date).format('ddd, MMM DD, YYYY hh:mm A') : ''}
                          </Typography>
                          <Typography variant="body2" color="textPrimary" style={{ whiteSpace: 'pre-line' }}>
                            {note.value}
                          </Typography>
                        </Card>
                      ))}
                  </Box>}
                  {hasValidNotes ? (
                    <Pagination
                      count={Math.ceil(initialData?.personal_notes?.length / notesPerPage)}
                      page={currentPage}
                      onChange={handlePageChange}
                      color="primary"
                    />) : (
                    <Typography>No Notes Available</Typography>
                  )}
                </Paper>

              </Box>
            </Box>
          </Box>
        )}
        {value === 1 && (
          <>
            <Box>
              <TableContainer component={Paper}>
                <Box sx={{ width: '100%', typography: 'body1' }}>

                  <Table sx={{ minWidth: 650 }} aria-label="chat history table">
                    <TableHead>
                      <TableRow>
                        <TableCell width="15%" sx={{ fontWeight: 'bold' }}>TimeStamp</TableCell>
                        <TableCell width="25%" sx={{ fontWeight: 'bold' }}>Input</TableCell>
                        <TableCell width="35%" sx={{ fontWeight: 'bold' }}>Output</TableCell>
                        <TableCell width="10%" align="center" sx={{ fontWeight: 'bold' }}>Status</TableCell>
                        <TableCell width="15%" align="center" sx={{ fontWeight: 'bold' }}>Action</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {userDetails && userDetails?.chatbot && userDetails?.chatbot?.length > 0 ? userDetails?.chatbot.map((row) => (
                        <TableRow key={row.name} sx={{ '&:last-child td, &:last-child th': { border: 0 } }}>
                          <TableCell component="th" scope="row" sx={{ verticalAlign: 'top' }}>
                            {row.response_timestamp ? moment(row.response_timestamp).format('ddd, MMM DD, YYYY hh:mm A') : ''}
                          </TableCell>
                          <TableCell sx={{ 
                            verticalAlign: 'top', 
                            maxWidth: '250px', 
                            overflow: 'hidden', 
                            textOverflow: 'ellipsis',
                            whiteSpace: 'nowrap'
                          }}>
                            <Tooltip title={row?.prompt} placement="top">
                              <Typography style={{overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap'}}>{row?.prompt}</Typography>
                           </Tooltip>
                          </TableCell>
                          <TableCell sx={{ 
                            verticalAlign: 'top', 
                            maxWidth: '350px', 
                            overflow: 'hidden', 
                            textOverflow: 'ellipsis',
                             whiteSpace: 'nowrap'
                          }}>
                          <Tooltip title={row?.model_output} placement="top">
                              <Typography style={{overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap'}}>{row?.model_output}</Typography>
                       </Tooltip>
                          </TableCell>
                          <TableCell align="center" sx={{ verticalAlign: 'top' }}>
                            <Chip 
                              label={row?.review_status} 
                              color={getStatusColor(row?.review_status)}
                              size="small"
                            />
                          </TableCell>
                          <TableCell align="center" sx={{ verticalAlign: 'top' }}>
                            <Button 
                              variant="outlined" 
                              size="small"
                              onClick={() => handleReviewBtn(row)}
                              disabled={row?.review_status !== 'Pending'}
                            >
                              Review
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))
                      :
                      <TableRow>
                        <TableCell align="center" colSpan={5} sx={{ height: 100, verticalAlign: 'middle' }}> 
                          No Chats Found
                        </TableCell>
                      </TableRow>
                      }
                    </TableBody>
                  </Table>
                </Box>
              </TableContainer>
            </Box>

            {showReviewContainer &&
              <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
                <Typography variant="h5" component="h1" gutterBottom>
                  Patient expressing confusion in Module 3
                </Typography>

        


                <Box sx={{ display: 'flex', gap: 3, mt: 3, mb: 5 }}>
                  {/* Patient Input */}
                  <Paper elevation={1} sx={{ flex: 1, p: 0, borderRadius: 1 }}>
                    <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                      Patient Input
                    </Typography>
                   <TextField
                      inputRef={inputRef}
                      fullWidth
                      multiline
                      rows={3}
                      value={reviewData.Input}
                      InputProps={{
                        readOnly: true,
                      }}
                      variant="outlined"
                      sx={{
                        '& .MuiOutlinedInput-root': {
                          backgroundColor: '#f5f5f5',
                          padding: "0px"
                        },
                      }}
                    />
                  </Paper>

                  {/* AI Output */}
                  <Paper elevation={1} sx={{ flex: 1, p: 0, borderRadius: 1 }}>
                    <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                      AI Output
                    </Typography>
                    <TextField
                      fullWidth
                      multiline
                      rows={3}
                      value={reviewData.Output}
                      InputProps={{
                        readOnly: true,
                      }}
                      variant="outlined"
                      sx={{
                        '& .MuiOutlinedInput-root': {
                          backgroundColor: '#f5f5f5', 
                          padding: "0px"
                        },
                      }}
                    />
                  </Paper>
                </Box>


                {reviewData && reviewData?.review_status === 'Pending' &&
                  <>
                    <Typography variant="h6" component="h2" gutterBottom>
                      Provide Suggestions
                    </Typography>

                    <Paper elevation={1} sx={{ p: 1.5, mt: 3, borderRadius: 1, boxShadow: 'none !important' }}>
                      <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                        Editable Suggestions
                      </Typography>
                      <TextField
                        required
                        fullWidth
                        multiline
                        onChange={handleSuggestionChange}
                        rows={5}
                        placeholder="Enter your Suggestions"
                        variant="outlined"
                      />
                    </Paper>

                    {/* <Button variant="outlined" color="error" onClick={() => handleUpdateAI('Approve')}>Approve </Button>
                <Button variant="contained" color="primary" onClick={() => handleUpdateAI('Reject')}>Reject</Button> */}
                    <Box sx={{    textAlign: 'end'}}>
                    <LoadingButton
                      variant="outlined"
                      color="error"
                      sx={{ margin: '35px 20px 0px 0px' }}
                      loading={loadingButton === 'Approve'}
                      onClick={() => handleUpdateAI('Approve')}
                    >
                      Approve
                    </LoadingButton>

                    <LoadingButton
                      variant="contained"
                      color="primary"
                      sx={{ margin: '35px 0px 0px 0px', height: '34px' }}
                      loading={loadingButton === 'Reject'}
                      onClick={() => handleUpdateAI('Reject')}
                    >
                      Reject
                    </LoadingButton>
                    </Box>
                  </>
                }
              </Container>
            }
          </>
        )}
      </Paper>
      <SnackBar open={openSnackbar} snackbarTitle={snackbarTitle} close={() => setOpenSnackbar(false)} />
    </Page>

  );
};

export default UserProfile;
