//

import Card from './Card';
import Paper from './Paper';
import Input from './Input';
import Button from './Button';
import Tooltip from './Tooltip';
import Backdrop from './Backdrop';
// import Typography from './Typography';
import CssBaseline from './CssBaseline';

import Autocomplete from './Autocomplete';
import GlobalStyles from './GlobalStyles';
import Lists from './Lists';
import Tabs from './Tab';

// ----------------------------------------------------------------------

export default function ComponentsOverrides(theme) {
  return Object.assign(
    Card(theme),
    Input(theme),
    Paper(theme),
    But<PERSON>(theme),
    Toolt<PERSON>(theme),
    Backdrop(theme),
    // Typo<PERSON>(theme),
    Css<PERSON><PERSON><PERSON>(theme),
    Autocomplete(theme),
    GlobalStyles(theme),
    Lists(theme),
    Tabs(theme)
  );
}
