/* eslint-disable react/jsx-key */
/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable react/prop-types */
/* eslint-disable no-unused-vars */
/* eslint-disable arrow-body-style */
import React, { useState, useEffect } from 'react';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import PersonAddIcon from '@mui/icons-material/PersonAdd';
import { makeStyles } from '@mui/styles';
import { Grid, Typography, Button, Snackbar, Alert, Chip,Table, TableBody, TableCell,
  TableContainer, TableHead, TableRow, Paper,TablePagination, 
  Box,
  TextField} from '@mui/material';
  import { Search, Add, Delete } from '@mui/icons-material';
import MUIDataTable from 'mui-datatables';
import { Link as RouterLink, useLocation } from 'react-router-dom';
import moment from 'moment';
import IconButton from '@mui/material/IconButton';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import MenuItem from '@mui/material/MenuItem';
import Menu from '@mui/material/Menu';
import { LoadingButton } from '@mui/lab';
import { createTheme, useTheme, ThemeProvider } from '@mui/material/styles';

// import AddSubscriptionForm from './AddSubscriptionForm';
// import AddUsersForm from './AddUsers';
import AddUser from './AddUser';
import UsersTable from './UsersTable';
import BasicModel from '../../../../components/modal/BasicModel';
import adminServices from '../../../../services/adminServices';
import SnackBar from '../../../../components/snackbar/snackbar';
import DeleteAlert from '../../../../components/modal/DeleteModal';
import ClientAdminServices from '../../../../services/clientAdmin/course/clientAdminServices';

export default function EnrolledUser(props) {  
  const classes = useStyles();
  const location = useLocation();
  const [handleOpenUserModal, setHandleOpenUserModal] = React.useState(false);
  const [handleOpenUserTable, setHandleOpenUserTable] = React.useState(false);
  const [openSnackbar, setOpenSnackbar] = React.useState(false);
  const [snackbarTitle, setSnackbarTitle] = React.useState('');
  const [anchorEl, setAnchorEl] = React.useState(null);
  const [subscriptionId, setSubscriptionId] = React.useState();
  const [clientPlanId, setClientPlanId] = React.useState();
  const [loading, setLoading] = React.useState(true);
  const [values, setValues] = React.useState('');
  const [coursePlanId, setCoursePlanId] = React.useState();
  const [subscriptionData, setSubscriptionData] = React.useState([]);
  const [error, setError] = React.useState([]);
  const [enrolledUser, setEnrolledUser] = useState([]);
  const [openAlertModal, setOpenAlertModal] = React.useState(false);
  const [deleteAlert, setDeleteAlert] = React.useState(false);
  const [planeId, setPlaneId] = React.useState(0);
  const [userId, setUserId] = React.useState(0);

  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [sortKey, setSortKey] = useState('');
  const [sortOrder, setSortOrder] = useState('asc');

  const { from } = location.state;

  const open = Boolean(anchorEl);

  const handleClose = (event, reason) => {
    if (reason === 'clickaway') {
      return;
    }
    setOpenAlertModal(false);
  };

  const handleErrorMessage = (message) => {
    setError(message);
  };

  const handleClickMenu = (event, id, clientId, courseId) => {
    setAnchorEl(event.currentTarget);
    setSubscriptionId(id);
    setClientPlanId(clientId);
    setCoursePlanId(courseId);
  };
  const handleClickTable = (event, planId) => {
    getEnrolledUserById(planId);
  };

  const handleCloseMenu = () => {
    setAnchorEl(null);
  };

  const snakbarHandle = (message) => {
    setOpenSnackbar(true);
    setSnackbarTitle(message);
  };

  const handleUserTableClose = () => {
    setHandleOpenUserTable(false);
    setEnrolledUser([]);
  };

  const handleAddUser = () => {
    setHandleOpenUserModal(true);
  };

  const handleUserClose = () => {
    setHandleOpenUserModal(false);
  };

  const snackbarHandle = (message) => {
    setOpenSnackbar(true);
    setSnackbarTitle(message);
  };

  const getEnrolledUserById = async (planId) => {
    setLoading(true);
    console.log(subscriptionId, 'subscriptionId');
    try {
      const result = await ClientAdminServices.getEnrolledUser(props.courseId, props.planId, page, rowsPerPage);
   
      if (result.ok) {
        setEnrolledUser(result.data);
        setLoading(false);
      }
    } catch (error) {
      console.log(error);
    }
  };

  const getSubscription = async () => {
    setLoading(true);
    try {
      const response = await ClientAdminServices.getSubscriptionData(props.courseId);
      if (response.ok) {
        setSubscriptionData(response.data);
        setLoading(false);
        // getEnrolledUserById();
      }
    } catch (error) {
      console.log(error);
    }
  };

  useEffect(() => {
    getEnrolledUserById();
    getSubscription();
  }, [page, rowsPerPage]);
   

    const conformDelete = async () => {
      setLoading(true);
      try {
        const response = await ClientAdminServices.deleteEnrolledUser(planeId, userId);
        if (response.ok) {
          setLoading(false);
          getEnrolledUserById();        
          setDeleteAlert(false);
          setOpenSnackbar(true);
          setSnackbarTitle(response.data.message);
        } else {
          setLoading(false);
          setOpenSnackbar(true);
          setSnackbarTitle(response.data.message);
        }
      } catch (error) {
        console.log(error);
      }
    };

   const handleDelete = (userId, planeId) =>{
     setPlaneId(planeId);
     setUserId(userId)
     setDeleteAlert(true)
   }
 // console.log(page,  rowsPerPage,'subscriptionId111');
  return (
    <Grid container spacing={3}>
      <Grid item xs="12">
 <Paper>
      <TableContainer>
        <Table>
          <TableHead sx={{ backgroundColor: '#f4f6f8' }}>
            <TableRow>
                <TableCell>Name</TableCell>
                <TableCell>Email</TableCell>
                <TableCell>Phone No</TableCell>
                <TableCell>Last Login</TableCell>
              <TableCell align="center">Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {enrolledUser?.map((entry) => (
              <TableRow key={entry.id}>
                <TableCell>{entry.fullName}</TableCell>
                <TableCell>{entry.email}</TableCell>
                <TableCell>{entry.phone ? entry.phone :'-'}</TableCell>
                <TableCell>{entry?.lastLoginDateTime ? moment(entry?.lastLoginDateTime).format('MM-DD-YYYY HH:mm:ss'):'-'}</TableCell>
                <TableCell align="center">
                  <IconButton color="error" onClick={() => handleDelete(entry.id, entry.planId)}>
                    <Delete />
                  </IconButton>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      <TablePagination
        component="div"
        count={enrolledUser.length}
        page={page}
        rowsPerPage={rowsPerPage}
        onPageChange={(e, newPage) => setPage(newPage)}
        onRowsPerPageChange={(e) => {
          setRowsPerPage(parseInt(e.target.value, 10));
          setPage(0);
        }}
        rowsPerPageOptions={[10, 25, 50, 75, 100]}
      />


       
      </Paper>
        
      </Grid>

      <DeleteAlert
        open={deleteAlert}
        title="Are you sure you want delete this Subscription"
        confirm={conformDelete}
        close={() => setDeleteAlert(false)}
      />

      <BasicModel
        maxWidth={'md'}
        openModel={handleOpenUserTable}
        title={'Enrolled Users'}
        closeModel={handleUserTableClose}
        children={
          <UsersTable
            enrolledUser={enrolledUser}
            getEnrolledUserById={(id) => getEnrolledUserById(id)}
            snackBarControl={snackbarHandle}
            closeModel={handleUserTableClose}
            getSubscription={getSubscription}
            loading={loading}
            setLoading={setLoading}
            // planId={planId}
          />
        }
      />
      <BasicModel
        openModel={handleOpenUserModal}
        title={'Add User'}
        closeModel={handleUserClose}
        children={
          <AddUser
            subscriptionId={subscriptionId}
            clientPlanId={clientPlanId}
            coursePlanId={coursePlanId}
            getSubscription={getSubscription}
            snackBarControl={snakbarHandle}
            closeModel={handleUserClose}
            setOpenAlertModal={setOpenAlertModal}
            setErrorMessage={handleErrorMessage}
          />
        }
      />
      <SnackBar open={openSnackbar} snackbarTitle={snackbarTitle} close={() => setOpenSnackbar(false)} />
      <Snackbar anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }} open={openAlertModal} onClose={handleClose}>
        {error.length > 0 ? (
          <Alert onClose={handleClose} severity="error" sx={{ width: '100%' }}>
            <div>
              {' '}
              <table style={{ borderCollapse: 'collapse', padding: '2rem,0rem' }}>
                <tr style={{ textAlign: 'left' }}>
                  <th className={classes.tableData}>Email</th>
                  <th className={classes.tableData}>Error</th>
                  <th className={classes.tableData}>Plan Name</th>
                </tr>
                {error.map((items, index) => {
                  return (
                    <tr>
                      <td className={classes.tableData}>{items.email}</td>
                      <td className={classes.tableData}>{items.error}</td>
                      <td className={classes.tableData}>{items.clientPlanName}</td>
                    </tr>
                  );
                })}
              </table>
            </div>
          </Alert>
        ) : (
          <Alert onClose={handleClose} severity="success" sx={{ width: '100%' }}>
            <Typography variant="body2">User Enrolled Successfully</Typography>
          </Alert>
        )}
      </Snackbar>
    </Grid>
  );
}
const useStyles = makeStyles((theme) => ({
  button: {
    display: 'flex',
    justifyContent: 'end',
    [theme.breakpoints.down(514)]: {
      marginTop: '14px',
    },
  },
  tableData: {
    border: '1px solid #dddddd',
    textAlign: 'left',
    padding: '8px',
  },
}));

const customeTheme = createTheme({
  components: {
    MuiBackdrop: {
      styleOverrides: {
        root: {
          '&.MuiModal-backdrop': {
            background: 'transparent',
          },
        },
      },
    },
    MuiTypography: {
      styleOverrides: {
        root: {
          fontFamily: 'Inter,SemiBold',
        },
      },
    },
    MuiLoadingButton: {
      styleOverrides: {
        root: {
          color: '#FE7000',
          border: '1px solid #FE7000',
          '&:hover': {
            border: '1px solid #FE7000',
          },
        },
      },
    },
    MuiPaper: {
      styleOverrides: {
        root: {
          '&.MuiPopover-paper': {
            boxShadow: 'rgba(0, 0, 0, 0.1) 0px 4px 12px;',
          },
        },
      },
    },
  },
});
