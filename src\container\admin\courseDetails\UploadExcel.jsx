/* eslint-disable arrow-body-style */
/* eslint-disable no-undef */
/* eslint-disable no-async-promise-executor */
/* eslint-disable no-alert */
/* eslint-disable react/button-has-type */
/* eslint-disable no-irregular-whitespace */
import React, { useState } from 'react';
import { Button, TextField,MenuItem,Grid,FormControl,InputLabel,Select } from '@mui/material';
import axios from 'axios';
import './styles.css';
import adminServices from '../../../services/adminServices';
import Page from '../../../components/Page';
import PageHeader from '../../../components/PageHeader';



const FileUploadExcel = () => {

    const [excelfile,setExcelfile] = useState('')
    const [type,setType] = useState('SAT')
    

    const handleChangeExcel = (event) => {
        const file = event.currentTarget.files[0];
        setExcelfile(file);
    }

    const preSignedUrlUpload = async (url, file) => {
        const response = await axios.put(url, file, {
          headers: {
            'Content-Type': file?.type,
          },
        });
      
        return response;
      };


    const sendQuestionsToBackendNew = async() => {
        const data = new FormData();
        console.log('file is ',excelfile)
        data.append('excelfile', excelfile);
        data.append('type', type);
        console.log('data is ',data)
        const response = await adminServices.ExcelUploads(data);
        console.log('response is =>',response?.data?.url)
        const generatedUrl=response?.data?.url
        if(generatedUrl){ 
            console.log('before upload is =>',generatedUrl)
            const responseUpload=await preSignedUrlUpload(generatedUrl,excelfile);
            console.log('after is =>',responseUpload)
        }
    }

    return (
        <>
 <Page title="Sub-module details">
      <PageHeader
             />
<Grid item xs={6}>
                        <FormControl >
                            <InputLabel id="demo-simple-select-standard-label">Type*</InputLabel>
                            <Select
                                labelId="demo-simple-select-standard-label"
                                id="questionType"
                                name="questionType"
                                label="Type"
                                value={type}
                                onChange={(e) => setType(e.target.value)}
                                displayEmpty
                            >
                                <MenuItem value="SAT">SAT</MenuItem>
                                <MenuItem value="GA">GA</MenuItem>
                                <MenuItem value="NEET">NEET</MenuItem>
                            </Select>
                            
                        </FormControl>
                    </Grid>
            <TextField
                style={{ width: '550px', display: 'block' }}
                id="outlinedBasic"
                name="userFile"
                // onBlur={handleBlur}
                // inputProps={{
                //     accept: '.xlsx, .xls, .xlsm, .xlsb',
                //     style: { color: 'transparent' }
                // }}
                accept="*/*" 
                onChange={(event) => {
                    handleChangeExcel(event)

                }}
                type="file"
                variant="outlined"
            />

            <Button id='uploadbutton' variant="outlined" color="primary" onClick={sendQuestionsToBackendNew} style={{ marginLeft: '0px', marginTop: '25px', marginBottom: '10px' }}>
                Upload Questions
            </Button>

            </Page >
     
        </>
    )

}

export default FileUploadExcel;