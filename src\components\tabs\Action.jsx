import { <PERSON><PERSON>, Container, <PERSON><PERSON>, Divider, IconButton } from '@mui/material';
import React, { useState } from 'react';
import { makeStyles } from '@mui/styles';
import { DropzoneArea } from 'material-ui-dropzone';
import FormControl from '@mui/material/FormControl';
import FormHelperText from '@mui/material/FormHelperText';
import { useTranslation } from 'react-i18next';
import { Editor } from 'react-draft-wysiwyg';
import { convertFromRaw, EditorState, convertToRaw, ContentState, convertFromHTML } from 'draft-js';
import draftToHtml from 'draftjs-to-html';
import 'react-draft-wysiwyg/dist/react-draft-wysiwyg.css';

import TextField from '@mui/material/TextField';
import FormatAlignLeftIcon from '@mui/icons-material/FormatAlignLeft';
import FormatAlignCenterIcon from '@mui/icons-material/FormatAlignCenter';
import FormatAlignRightIcon from '@mui/icons-material/FormatAlignRight';
import FormatAlignJustifyIcon from '@mui/icons-material/FormatAlignJustify';
import ToggleButton from '@mui/material/ToggleButton';
import ToggleButtonGroup from '@mui/material/ToggleButtonGroup';
import Typography from '@mui/material/Typography';
import { Form, Formik } from 'formik';
import * as Yup from 'yup';

import AppleIcon from '@mui/icons-material/Apple';
import DragHandleIcon from '@mui/icons-material/DragHandle';

import BasicModals from '../modal/BasicModel';
import './dropzone.css';

export default function Action() {
  const classes = useStyles();
  const [alignment, setAlignment] = React.useState('left');

  const handleAlignment = (event, newAlignment) => {
    setAlignment(newAlignment);
  };

  const [addKeys, setAddKeys] = React.useState(false);
  console.log(addKeys);

  const handleIndividualKeys = () => {
    setAddKeys(true);
  };

  const [openModal, setOpenModal] = React.useState(false);

  const handleModal = () => {
    setOpenModal(true);
  };

  const handleClose = () => {
    setOpenModal(false);
  };

  const content = {
    blocks: [
      {
        key: '637gr',
        text: '',
        type: 'unstyled',
        depth: 0,
        inlineStyleRanges: [],
        entityRanges: [],
        data: {},
      },
    ],
    entityMap: {},
  };
  const contentBlock = convertFromRaw(content);
  const [editorState, setEditorState] = useState(EditorState.createWithContent(contentBlock));
  const [descriptionContent, setDescriptionContent] = useState(
    draftToHtml(convertToRaw(editorState.getCurrentContent()))
  );
  const [descErr, setDescErr] = useState(false);

  const onEditorStateChange = (_editorState) => {
    setEditorState(_editorState);
    setDescriptionContent(draftToHtml(convertToRaw(_editorState.getCurrentContent())));
  };

 const { t } = useTranslation(); 
 
  return (
    <div>
      <Formik
        initialValues={{
          email: '',
          password: '',
        }}
        validationSchema={Yup.object().shape({
          email: Yup.string().required().email().label('Email'),
          password: Yup.string()
            .trim()
            .min(8)
            .nullable()
            .matches(
              /^.*(?=.{6,})((?=.*[!@#$%^&*()\-_=+{};:,<.>]){1})(?=.*\d)((?=.*[a-z]){1})((?=.*[A-Z]){1}).*$/,
              'The password must contain 8 characters, one upper case, one number and one special case character'
            )
            .required(t('Password is required.')),
        })}
        onSubmit={(values) => {
          console.log(values);
        }}
      >
        {({ values, errors, touched, handleBlur, handleChange, onSubmit, setFieldValue }) => (
          <Form>
            <Grid container spacing={3}>
              <Grid item xs="12">
                <Typography className={classes.background}>Screen Title( For Internal Use )</Typography>
                <TextField className={classes.dropzone}
                 fullWidth id="outlined-basic" variant="outlined" />
              </Grid>
              <Grid item xs="12">
                <Typography className={classes.background}>Background Image</Typography>
                <div className={classes.dropzone}>
                  <FormControl
                    required
                    component="fieldset"
                    color="primary"
                    variant="outlined"
                    fullWidth
                    name="thumbImage"
                    value={values.thumbImage}
                  >
                    <DropzoneArea
                      acceptedFiles={['image/jpeg', 'image/png', 'image/bmp']}
                      // showPreviews={true}
                      dropzoneText="Drag and Drop Image or Browse File"
                      showPreviewsInDropzone={false}
                      maxFileSize={104857600}
                      filesLimit={2}
                      value={values.thumbImage}
                      onChange={(file) => setFieldValue('thumbImage', file)}
                      showFileNamesInPreview
                    />
                    <FormHelperText style={{ color: '#F44336' }}>
                      {errors.thumbImage && touched.thumbImage && errors.thumbImage}
                    </FormHelperText>
                  </FormControl>
                </div>
              </Grid>
              <Grid item xs="12">
                <Typography className={classes.background}>Description</Typography>
                <div className={classes.dropzone}>
                  <Editor
                    editorState={editorState}
                    editorClassName={classes.textEditor}
                    toolbarClassName={classes.toolbarEditor}
                    onEditorStateChange={onEditorStateChange}
                  />
                </div>
              </Grid>

              <Grid item xs="12">
                <div className={classes.combined}>
                  <Button onClick={handleIndividualKeys} className={classes.individual}>
                    <Typography style={{ color: '#00B673' }}>Individual Hot Keys</Typography>
                  </Button>

                  <div className={classes.individual}>
                    <Typography style={{ color: '#00B673' }}>Combined hot keys</Typography>
                  </div>
                  <div className={classes.individual}>
                    <Typography>String</Typography>
                  </div>
                  <div className={classes.individual}>
                    <Typography>Pattern</Typography>
                  </div>
                </div>
              </Grid>
              {addKeys && (
                <Grid item xs="12">
                  <div className={classes.combined}>
                    <div>
                      <Typography>Hot Key and Images</Typography>
                    </div>
                    <div>
                      <Button onClick={handleModal} className={classes.individual}>
                        <Typography style={{ color: '#00B673' }}>Add Keys</Typography>
                      </Button>
                    </div>
                  </div>
                  <div style={{ marginTop: '1rem' }}>
                    <Divider />
                  </div>
                </Grid>
              )}
              <Grid item xs="12">
                <Typography className={classes.background}>Box Position</Typography>
                <div style={{ display: 'flex' }}>
                  <div className={classes.dropzone}>
                    <Typography gutterBottom>Horizontally Align</Typography>
                    <ToggleButtonGroup
                      value={alignment}
                      exclusive
                      onChange={handleAlignment}
                      aria-label="text alignment"
                    >
                      <ToggleButton value="left" aria-label="left aligned">
                        <FormatAlignLeftIcon />
                      </ToggleButton>
                      <ToggleButton value="center" aria-label="centered">
                        <FormatAlignCenterIcon />
                      </ToggleButton>
                      <ToggleButton value="right" aria-label="right aligned">
                        <FormatAlignRightIcon />
                      </ToggleButton>
                    </ToggleButtonGroup>
                  </div>
                  <div style={{ marginLeft: '1rem', marginTop: '10px' }}>
                    <Typography gutterBottom>Vertically Align</Typography>
                    <ToggleButtonGroup
                      value={alignment}
                      exclusive
                      onChange={handleAlignment}
                      aria-label="text alignment"
                    >
                      <ToggleButton value="left" aria-label="left aligned">
                        <FormatAlignLeftIcon />
                      </ToggleButton>
                      <ToggleButton value="center" aria-label="centered">
                        <FormatAlignCenterIcon />
                      </ToggleButton>
                      <ToggleButton value="right" aria-label="right aligned">
                        <FormatAlignRightIcon />
                      </ToggleButton>
                    </ToggleButtonGroup>
                  </div>
                </div>
              </Grid>
              <Grid item xs="12">
                <Button variant="contained" color="primary" fullWidth>
                  Add
                </Button>
              </Grid>
            </Grid>
          </Form>
        )}
      </Formik>
    </div>
  );
}
const useStyles = makeStyles((theme) => ({
  ctrl: {
    border: '1px solid #6D6969',
    borderRadius: '4px',
    padding: '10px',
    height: '35px',
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    margin: '3px',
  },
  grid: {
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'space-between',
  },
  iconPosition: {
    marginBottom: '1rem',
    [theme.breakpoints.down('sm')]: {
      marginBottom: '0rem',
    },
  },
  actionkeys: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#FCFFFF',
    border: '1px solid #BCBCBC',
    padding: '7px',
    borderRadius: '5px',
  },
  combined: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
  },

  individual: {
    border: '1px dotted #BCBCBC',
    padding: '5px',
  },
  dropzone: {
    marginTop: '10px',
  },
  background: {
    fontWeight: 'bold',
    color: '#000000',
  },
  dragHandIcon: {
    border: '1px solid #BCBCBC',
    borderRadius: '10px',
    padding: '1rem',
  },
  apple: {
    color: '#A0A8AE',
  },
}));
