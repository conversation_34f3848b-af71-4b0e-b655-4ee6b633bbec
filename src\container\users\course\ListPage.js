/* eslint-disable react/prop-types */
import React from 'react';
import { Box, MenuItem, Typography } from '@mui/material';
import { useTranslation } from 'react-i18next';

const ListPage = ( {searchResults, searchComplete, handleStartSimulation, setOpenVideoModal, setVideoTitle, isQuery} ) => {
    const { t } = useTranslation();
    
    return (
        <>
            {searchComplete && (
                <Box sx={{
                    maxHeight: '150px',
                    overflowY: searchResults.length > 1 ? 'auto' : 'hidden',
                    scrollbarWidth: 'thin',
                    whiteSpace: 'nowrap',
                    textOverflow: 'ellipsis',
                    overflowX: 'hidden'
                }}>
                    {searchResults.map((searchRes) => (
                        <>
                            {"topic" in searchRes && (
                                <MenuItem key={searchRes.sid} onClick={() => handleStartSimulation(searchRes.sid,searchRes.mod)}>
                                    {`Topic "${searchRes.topic}" in ${searchRes.sub}`}
                                </MenuItem>
                            )}
                            {"video" in searchRes && searchRes.video && (
                                <MenuItem
                                    key={searchRes.id}
                                    onClick={() => {
                                        setOpenVideoModal(true);
                                        // eslint-disable-next-line no-unused-vars
                                        const arrLength = searchRes.video.split('/');
                                        setVideoTitle({
                                            title: searchRes.sub,
                                            // video: arrLength[arrLength.length - 1],
                                            video: searchRes.video,
                                        });
                                    }}
                                >
                                    {`Video for ${searchRes.sub}`}
                                </MenuItem>
                            )}
                        </>
                    ))}
                    {searchResults.length === 0 && isQuery.length > 0 && (
                        <Typography>
                            {t('No results found')}
                        </Typography>
                    )}  
                </Box>
            )}
        </>
    );
};

export default ListPage