import React from 'react';
import { Con<PERSON><PERSON>, <PERSON>rid, <PERSON><PERSON><PERSON>, Button, Divider, Box, TextField } from '@mui/material';
import Link from '@mui/material/Link';
import Paper from '@mui/material/Paper';
import { createTheme, MuiThemeProvider } from '@material-ui/core/styles';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
// import Paper from '@mui/material/Paper';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';

import { makeStyles } from '@mui/styles';
import MUIDataTable from 'mui-datatables';
import PageHeader from '../../../components/PageHeader';
import Page from '../../../components/Page';

const columns = [
  {
    name: 'createDate',
    label: 'Created Date',
    options: {
      filter: true,
      sort: true,
    },
  },
  {
    name: 'courseName',
    label: 'Course Name',
    options: {
      filter: true,
      sort: false,
      customBodyRender: (value, tableMeta, updateValue) => {
        return (
          <>
            <Link href="#" color="secondary" {...label}>
              Excel Efficiancy
            </Link>
          </>
        );
      },
    },
  },
  {
    name: 'license',
    label: 'No of License',
    options: {
      filter: true,
      sort: false,
    },
  },
  {
    name: 'price',
    label: 'Price',
    options: {
      filter: true,
      sort: false,
    },
  },
  {
    name: 'term',
    label: 'Term',
    options: {
      filter: true,
      sort: false,
    },
  },
  {
    name: 'status',
    label: 'Status',
    options: {
      filter: true,
      sort: false,
      customBodyRender: (value, tableMeta, updateValue) => {
        return (
          <>
            <Typography color="secondary" {...label}>
              active
            </Typography>
          </>
        );
      },
    },
  },
  {
    name: 'validFrom',
    label: 'Valid From',
    options: {
      filter: true,
      sort: false,
    },
  },
  {
    name: 'validTo',
    label: 'Valid Till',
    options: {
      filter: true,
      sort: false,
    },
  },
];

function createData(CreatedDate, courseName, license, price, term, status, validfrom, validTo) {
  return { CreatedDate, courseName, license, price, term, status, validfrom, validTo };
}

const rows = [
  createData('02-28-2022', 'Excel Efficiency', '10', '$149', 'monthly', 'active', '02-28-2022', '03-27-2022'),
];

const muiData = (
  <>
    <div style={{ fontWeight: 'bold', color: '#000000' }}>Subscription ID</div>
    <div style={{ color: '#000000', borderBottom: '1px solid #BCBCBC' }}>EXDX2098</div>
    <div>subscription details</div>
  </>
);

const label = { inputProps: { 'aria-label': 'Switch demo' } };

export default function Subscription() {
  const classes = useStyles();

  return (
    <Page title="Subscription">
      <div className={classes.main}>
        <PageHeader pageTitle={'Subscription'} />
        <div className={classes.subscription}>
          <div className={classes.table}>
            <Grid container spacing={3}>
              <Grid item xs="12">
                <div className={classes.tableBorder}>
                  <TableContainer component={Paper}>
                    <Table sx={{ minWidth: '100%' }} aria-label="simple table">
                      <TableHead>
                        <TableRow>
                          <TableCell align="left" colSpan={8}>
                            <Typography className={classes.tablecontent}>Subscription ID</Typography>
                            <Typography className={classes.tableData}>EXDX2098</Typography>
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <Typography className={classes.TableCell}>Subcription Details</Typography>
                        </TableRow>
                        <TableRow>
                          <TableCell>Create Date</TableCell>
                          <TableCell>Course Name</TableCell>
                          <TableCell>No of License</TableCell>
                          <TableCell>Price</TableCell>
                          <TableCell>Term</TableCell>
                          <TableCell>Status</TableCell>
                          <TableCell>Valid From</TableCell>
                          <TableCell>Valid Till</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {rows.map((row) => (
                          <TableRow key={row.name} sx={{ '&:last-child td, &:last-child th': { border: 0 } }}>
                            <TableCell component="th" scope="row">
                              {row.CreatedDate}
                            </TableCell>
                            <TableCell>
                              <Link href="#" underline="none" color={'secondary'}>
                                {row.courseName}
                              </Link>
                            </TableCell>
                            <TableCell>{row.license}</TableCell>
                            <TableCell>{row.price}</TableCell>
                            <TableCell>{row.term}</TableCell>
                            <TableCell>
                              <Typography className={classes.footerContent}>{row.status}</Typography>
                            </TableCell>
                            <TableCell>{row.validfrom}</TableCell>
                            <TableCell>{row.validTo}</TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </TableContainer>
                </div>
              </Grid>
            </Grid>
          </div>
          <div className={classes.footer}>
            <Grid container spacing={3}>
              <Grid item xs="12" sm="6" md="3">
                <Typography>Email </Typography>
                <Typography variant="h6"><EMAIL></Typography>
                <Typography>For payments and billing questions</Typography>
              </Grid>
              <Grid item xs="12" sm="6" md="3">
                <Typography>Call </Typography>
                <Typography variant="h6">******* 105 1234</Typography>
                <Typography>10AM to 10 PM - Monday to Friday </Typography>
              </Grid>
            </Grid>
          </div>
        </div>
      </div>
    </Page>
  );
}
const useStyles = makeStyles((theme) => ({
  subscription: {
    minHeight: '74vh',
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'space-between',
    background: '#fff',
    borderRadius: '10px',
    marginTop: '2rem',
    border: '1px solid #0000000A',
  },
  main: {
    padding: '16px',
  },
  table: {
    borderRadius: '8px',
  },
  tableBorder: {
    borderBottom: '1px solid #D9D9D9',
  },
  TableCell: {
    fontSize: '14px',
    paddingLeft: '16px',
    paddingTop: '16px',
  },
  tablecontent: {
    fontWeight: 'bold',
    color: '#000000',
    fontSize: '16px',
    padding: '2px',
  },
  tableData: {
    color: '#000000',
    padding: '2px',
  },
  footer: {
    background: '#FBFBFB91',
    border: '1px solid #0000000A',
    paddingLeft: '16px',
    borderBottomLeftRadius: '10px',
    borderBottomRightRadius: '10px',
    paddingTop: '10px',
    paddingBottom: '10px',
  },
  footerContent: {
    color: '#00B673',
  },
  title: {
    marginBottom: '12px',
    display: 'flex',
    flexWrap: 'no-wrap',
  },
  tableheader: {
    paddingBottom: '3.5rem',
    [theme.breakpoints.down('md')]: {
      paddingBottom: '4.5rem',
    },
  },
}));
