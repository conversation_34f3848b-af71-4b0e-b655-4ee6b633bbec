import React from 'react';
import { makeStyles } from '@mui/styles';
import { Box, Grid, Typography } from '@mui/material';
import { Link } from 'react-router-dom';

import Webpage from '../components/WebPage';

function Sitemap() {
  const classes = useStyles();

  return (
    <Webpage>
      <div className={classes.sectionFis}>
        <Typography variant="h1" className={classes.sitemapHeader} gutterBottom>
          Sitemap
        </Typography>
        <Box mt={4} sx={{ padding: '0 16px' }}>
          <Grid container spacing={4}>
            <Grid item xs={12} sm={6} md={4} lg={3}>
              <Typography variant="body1" className={classes.subtitle}>
                Home
              </Typography>
              <ul>
                <li className={classes.list}>
                  <Link to="/">Home</Link>
                </li>
              </ul>
            </Grid>
            <Grid item xs={12} sm={6} md={4} lg={3}>
              <Typography variant="body1" className={classes.subtitle}>
                Solutions
              </Typography>
              <ul>
                <li className={classes.list}>
                  <Link to="/self-study">Individual Learning Solutions</Link>
                </li>
                <li className={classes.list}>
                  <Link to="/business">Business</Link>
                </li>
                <li className={classes.list}>
                  <Link to="/higher-education">Higher Education</Link>
                </li>
              </ul>
            </Grid>
            <Grid item xs={12} sm={6} md={4} lg={3}>
              <Typography variant="body1" className={classes.subtitle}>
                Courses
              </Typography>
              <ul>
                <li className={classes.list}>
                  <Link to="/excel-efficiency">Excel Efficiency</Link>
                </li>
                <li className={classes.list}>
                  <Link to="/quickbooks">QuickBooks Online</Link>
                </li>
                <li className={classes.list}>
                  <Link to="/financial-modeling">Financial Modeling</Link>
                </li>
                <li className={classes.list}>
                  <Link to="/powerpoint">Powerpoint Speed</Link>
                </li>
                <li className={classes.list}>
                  <Link to="/ms-word">Microsoft Word</Link>
                </li>
              </ul>
            </Grid>
            <Grid item xs={12} sm={6} md={4} lg={3}>
              <Typography variant="body1" className={classes.subtitle}>
                Resources
              </Typography>
              <ul>
                <li className={classes.list}>
                  <Link to="/pricing">Pricing</Link>
                </li>

                <li className={classes.list}>
                  <Link to="/login">Login</Link>
                </li>
                <li className={classes.list}>
                  <Link to="/sign-up">Sign up</Link>
                </li>
              </ul>
            </Grid>
            <Grid item xs={12} sm={6} md={4} lg={3}>
              <Typography variant="body1" className={classes.subtitle}>
                Explore Us
              </Typography>
              <ul>
                <li className={classes.list}>
                  <Link to="/terms">Terms</Link>
                </li>
                <li className={classes.list}>
                  <Link to="/faqs">EAQ's</Link>
                </li>
                <li className={classes.list}>
                  <Link to="/privacy">Privacy</Link>
                </li>
                <li className={classes.list}>
                  <Link to="/aboutUs">About Us</Link>
                </li>
              </ul>
            </Grid>
          </Grid>
        </Box>
      </div>
    </Webpage>
  );
}

const useStyles = makeStyles((theme) => ({
  sectionFis: {
    maxWidth: '1440px',
    margin: 'auto',
    overflow: 'hidden',
    padding: '0 94px',
    [theme.breakpoints.down('sm')]: {
      padding: '0 2rem',
    },
    marginBottom: '2rem',
  },
  list: {
    lineHeight: '32px',
    textDecoration: 'none',
    marginLeft: '1rem',
  },
  subtitle: {
    fontSize: '1.18rem',
    fontWeight: 600,
  },
  //   sitemapHeader: {
  //     marginBottom: theme.spacing(2),
  //   },
  //   listItem: {
  //     '&:hover': {
  //       backgroundColor: theme.palette.action.hover,
  //     },
  //   },
}));

export default Sitemap;
