<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="260" height="240" viewBox="0 0 260 240">
  <defs>
    <clipPath id="clip-path">
      <rect id="Rectangle_1680" data-name="Rectangle 1680" width="260" height="240" rx="6" transform="translate(863 -621)" fill="#6333b0"/>
    </clipPath>
    <filter id="Rectangle_1679" x="-9" y="-6" width="278" height="258" filterUnits="userSpaceOnUse">
      <feOffset dy="3" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="3" result="blur"/>
      <feFlood flood-opacity="0.161"/>
      <feComposite operator="in" in2="blur"/>
      <feComposite in="SourceGraphic"/>
    </filter>
  </defs>
  <g id="Mask_Group_9" data-name="Mask Group 9" transform="translate(-863 621)" clip-path="url(#clip-path)">
    <g transform="matrix(1, 0, 0, 1, 863, -621)" filter="url(#Rectangle_1679)">
      <rect id="Rectangle_1679-2" data-name="Rectangle 1679" width="260" height="240" rx="6" fill="#008ae5"/>
    </g>
    <ellipse id="Ellipse_120" data-name="Ellipse 120" cx="45.5" cy="45" rx="45.5" ry="45" transform="translate(1000 -641)" fill="#0078d3"/>
    <circle id="Ellipse_121" data-name="Ellipse 121" cx="51.5" cy="51.5" r="51.5" transform="translate(1042 -637)" fill="#0067c0"/>
  </g>
</svg>
