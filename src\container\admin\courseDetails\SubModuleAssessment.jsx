/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable react/prop-types */

import React, { useState, useEffect } from 'react';
import MenuItem from '@mui/material/MenuItem';
import { TextField, Button, Chip, Box, Paper, Typography, InputAdornment, Select, Grid, FormControl, IconButton  }from "@mui/material";
import SearchIcon from "@mui/icons-material/Search";
import { LoadingButton } from '@mui/lab';
import InputLabel from '@mui/material/InputLabel';
import DOMPurify from 'dompurify';
import DialogModal from '../../../components/modal/DialogModal';
import SnackBar from '../../../components/snackbar/snackbar';
import adminServices from '../../../services/adminServices';

const SubModuleAssessment = ({ open, onClose, submoduleId, comingfrom  }) => {
  const [loading, setLoading] = useState(false);
  const [questionList, setQuestionList] = useState([]);
  // eslint-disable-next-line no-unused-vars
  const [hashtag, setHashtag] = useState([]);
  // eslint-disable-next-line no-unused-vars
  const [category, setCategory] = useState([]);
  const [tags, setTags] = useState([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedQuestions, setSelectedQuestions] = useState([]);
  const [isSearched, setIsSearched] = useState(false);
  const [openSnackbar, setOpenSnackbar] = useState(false);
  const [snackbarTitle, setSnackbarTitle] = useState('');
  const [level, setLevel] = useState('Easy');



  const [nodata, setNodata] = useState(true);

  useEffect(async () => {
    await getLookUpDetails();
  }, []);

  useEffect(() => {
    setLoading(false)
  }, [open]);


  const handleAddTag = () => {
    if (searchTerm && !tags.includes(searchTerm)) {
      setTags([...tags, searchTerm]);
      getQuestionList([...tags, searchTerm]);
      setIsSearched(true);
    }
    setSearchTerm("");
  };

  const handleRemoveTag = (index) => {
    const newTags = tags.filter((_, i) => i !== index);
    setTags(newTags);
    setNodata(true)
    if (newTags?.length > 0) {
      getQuestionList([...newTags]);
    }
    else {
      setQuestionList([])
    }

    setIsSearched(false);
  };


  const handleSearchChange = (e) => {
    setSearchTerm(e.target.value);
  };

  const handleAddQuestion = (question) => {
    if (!selectedQuestions.some(q => q.id === question.id)) {
      setSelectedQuestions([...selectedQuestions, question]);
    }
  };

  const handleRemoveQuestion = (question) => {
    setSelectedQuestions(selectedQuestions.filter(q => q.id !== question.id));
  };


  const getQuestionList = async (data) => {
    setNodata(false)
    const result = await adminServices.getQuestionDataMCQNew(data);
    if (result.ok) {
      if (result.data?.length === 0) {
        setNodata(true)
      }
      else {
        setQuestionList(result.data)
        setNodata(false)
      }

    }
  };


  const getLookUpDetails = async () => {
    const key = ['hashtag', 'course_category'];
    const result = await adminServices.getLookUpData(key);
    if (result.ok) {
      setHashtag(result.data.hashtag);
      setCategory(result.data.course_category);
    }
  };


  const handleSubmit = async () => {
    setLoading(true);
    const filteredquestions = selectedQuestions.map((data) => data.id)
    const time1 = time
    const totalSeconds = (time1.hours * 3600) + (time1.minutes * 60) + time1.seconds;
    const details = {
      filteredquestions,
      level,
      totalSeconds,
    }
  //  console.log(filteredquestions, selectedQuestions, "details2122");
    try {
      const response = await adminServices.createCourseAssessMent(submoduleId, comingfrom, details);
      if (response.ok) {
        setSnackbarTitle('Assignment created successfully');
        setOpenSnackbar(true);
      
      //  console.log(onClose , typeof onClose, 'onclose11')
        if (typeof onClose === 'function') {
           onClose(true, submoduleId);
        }
        CloseFunction();
      }
    } catch (error) {
      console.log(error);
    }
    setLoading(false);
  };

  const [time, setTime] = useState({
    hours: 0,
    minutes: 0,
    seconds: 0,
  });



  const CloseFunction = () => {
    setQuestionList([])
    setSelectedQuestions([])
    setTags([])
    onClose()
    setIsSearched(false);
    setTime({
      hours: 0,
      minutes: 0,
      seconds: 0,
    });
    setLevel('Easy')
  }



  // Handle change for each select dropdown
  const handleChange = (event) => {
    const { name, value } = event.target;
    setTime((prevTime) => ({
      ...prevTime,
      [name]: value,
    }));
  };

  const hoursArray = Array.from({ length: 24 }, (_, i) => i); 
  const minutesArray = Array.from({ length: 60 }, (_, i) => i); 
  const secondsArray = Array.from({ length: 60 }, (_, i) => i); 


  return (
    <>
      <DialogModal open={open} handleClose={CloseFunction} title={comingfrom === 'module' ? "Assessment Form for Module" : "Assessment Form for Sub Module"}>
        <Grid style={{ marginBottom: 1 }} container spacing={2}>
          {/* Hours */}
          <Grid item xs={2.5}>
            <FormControl fullWidth>
              <InputLabel>Hours</InputLabel>
              <Select
                name="hours"
                value={time.hours}
                onChange={handleChange}
                label="Hours"
              >
                {hoursArray.map((hour) => (
                  <MenuItem key={hour} value={hour}>
                    {hour}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>

          {/* Minutes */}
          <Grid item xs={2.5}>
            <FormControl fullWidth>
              <InputLabel>Minutes</InputLabel>
              <Select
                name="minutes"
                value={time.minutes}
                onChange={handleChange}
                label="Minutes"
              >
                {minutesArray.map((minute) => (
                  <MenuItem key={minute} value={minute}>
                    {minute}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>

          {/* Seconds */}
          <Grid item xs={2.5}>
            <FormControl fullWidth>
              <InputLabel>Seconds</InputLabel>
              <Select
                name="seconds"
                value={time.seconds}
                onChange={handleChange}
                label="Seconds"
              >
                {secondsArray.map((second) => (
                  <MenuItem key={second} value={second}>
                    {second}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>

          <Grid item xs={4}>
            <FormControl fullWidth>
              <InputLabel>Level</InputLabel>
              <Select
                name="level"
                value={level}
                onChange={(e) => setLevel(e.target.value)}
                label="Level"
              >
                <MenuItem value={'Easy'}>Easy</MenuItem>
                <MenuItem value={'Medium'}>Medium</MenuItem>
                <MenuItem value={'Complex'}>Complex</MenuItem>
              </Select>
            </FormControl>
          </Grid>
        </Grid>
       
        {selectedQuestions.length > 0 && (
          <>
            <Typography
              variant="body2"
              sx={{ flexGrow: 1, fontSize: '0.875rem', textDecoration: 'underline' }}
            >
              Selected Questions
            </Typography>
            <Paper
              sx={{
                mb: 2,
                p: 1,
                bgcolor: "#e3faf4",
                // bgcolor: "#cfe0ff",
                width: '100%',
                maxHeight: selectedQuestions.length > 4 ? '150px' : 'auto',
                overflowY: 'auto',
                '&::-webkit-scrollbar': {
                  width: '8px',
                },
                '&::-webkit-scrollbar-track': {
                  backgroundColor: '#f1f1f1',
                },
                '&::-webkit-scrollbar-thumb': {
                  backgroundColor: '#c1c1c1',
                  borderRadius: '10px',
                },
                '&::-webkit-scrollbar-thumb:hover': {
                  backgroundColor: '#a1a1a1',
                },
                scrollbarWidth: 'thin',
                scrollbarColor: '#c1c1c1 #f1f1f1',
              }}
            >

              {selectedQuestions.map((question) => (
                <Box
                  key={question.id}
                  sx={{ display: "flex", alignItems: "center", mb: 1 }}
                >
                
                  <Typography
                    id='questionviewedit1'
                    variant="body2"
                    sx={{
                      flexGrow: 1,
                      fontSize: '0.75rem',
                    }}
                    dangerouslySetInnerHTML={{ __html: DOMPurify.sanitize(question.question_text) }}
                  />
                  <Button
                    variant="outlined"
                    color="error"
                    id='deleteQuestion'
                    onClick={() => handleRemoveQuestion(question)}
                    sx={{
                      fontSize: '0.75rem',
                      minWidth: '24px',
                      minHeight: '24px',
                      padding: '2px',
                      borderRadius: '12px',
                      '& .MuiButton-label': { fontSize: '1rem' },
                    }}
                  >
                    x
                  </Button>
                </Box>
              ))}
            </Paper>
          </>
        )}



        <Paper sx={{ mb: 2, p: 1, bgcolor: "#f7f7f7", width: '100%' }}>
          <Box sx={{ display: "flex", mb: 1 }}>
            <TextField
              variant="outlined"
              id={comingfrom === 'module' ?'searchmodule':'searchsubmodule'}
              fullWidth
              placeholder="Search Questions..."
              value={searchTerm}
              onChange={handleSearchChange}
              sx={{
                bgcolor: "#f0f0f0",
                borderRadius: 1,
                height: 36,
                '& .MuiInputBase-input': {
                  fontSize: 14,
                  padding: "8px 12px",
                },
              }}
              InputProps={{
                endAdornment: (
                  <InputAdornment position="end">
                    <IconButton
                      id='searchIcon'
                      onClick={handleAddTag}
                      sx={{ p: 0, color: "#1976d2" }}
                    >
                      <SearchIcon />
                    </IconButton>
                  </InputAdornment>
                ),
              }}
            />
          </Box>
          <Box sx={{ display: "flex", flexWrap: "wrap", gap: 0.5, mb: 1 }}>
            {tags.map((tag, index) => (
              <Chip
                key={index}
                label={tag}
                onDelete={() => handleRemoveTag(index)}
                color="primary"
                size="small"
                sx={{ fontSize: 10 }}
              />
            ))}
          </Box>

          {isSearched && questionList?.length === 0 ? (
            <Typography variant="body2" sx={{ textAlign: 'center', color: 'text.secondary' }}>
              {nodata === false ? "loading..." : "No records found"}
            </Typography>
          ) : (

            <Box
              sx={{
                display: "flex",
                flexDirection: "column",
                maxHeight: questionList.length > 4 ? '140px' : 'auto',
                overflowY: 'auto',
                '&::-webkit-scrollbar': {
                  width: '8px',
                },
                '&::-webkit-scrollbar-track': {
                  backgroundColor: '#f1f1f1',
                },
                '&::-webkit-scrollbar-thumb': {
                  backgroundColor: '#c1c1c1',
                  borderRadius: '10px',
                },
                '&::-webkit-scrollbar-thumb:hover': {
                  backgroundColor: '#a1a1a1',
                },
                scrollbarWidth: 'thin',
                scrollbarColor: '#c1c1c1 #f1f1f1',
              }}
            >
              {questionList?.length > 0 && questionList.map((question) => {
                const isSelected = selectedQuestions.some((selected) => selected.question_text === question.question_text);

                return (
                  <Box
                    key={question.id}
                    sx={{ display: "flex", alignItems: "center", mb: 1 }}
                  >
                  
                    <Typography
                      id='questionview'
                      variant="body2"
                      sx={{
                        flexGrow: 1,
                        fontSize: '0.75rem',
                        bgcolor: isSelected ? "#cfe0ff" : "transparent",
                        padding: '4px',
                        borderRadius: '4px',
                      }}
                      dangerouslySetInnerHTML={{ __html: DOMPurify.sanitize(question.question_text) }}
                    />
                    <Button
                      id='questionadd'
                      variant="outlined"
                      color="primary"
                      onClick={() => handleAddQuestion(question)}
                      sx={{
                        fontSize: '0.75rem',
                        minWidth: '24px',
                        minHeight: '24px',
                        padding: '2px',
                        borderRadius: '12px',
                        '& .MuiButton-label': { fontSize: '1rem' },
                      }}
                    >
                      +
                    </Button>
                  </Box>
                );
              })}

            </Box>


          )}
        </Paper>


        <Box sx={{ display: "flex", justifyContent: "flex-start", mt: 2, marginLeft: 1.4 }}>
          
          {selectedQuestions && selectedQuestions?.length > 0 &&
           

            <LoadingButton
              type="submit"
              id="SubmissionButton"
              onClick={handleSubmit}
              variant="contained"
              color="primary"
              fullWidth
              loading={loading}
            >
              Submit
            </LoadingButton>
          }
        </Box>

      </DialogModal>
      <SnackBar open={openSnackbar} snackbarTitle={snackbarTitle} close={() => setOpenSnackbar(false)} />

    </>

  );
}
export default SubModuleAssessment;
