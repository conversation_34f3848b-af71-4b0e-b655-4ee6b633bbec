<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="260" height="240" viewBox="0 0 260 240">
  <defs>
    <clipPath id="clip-path">
      <rect id="Rectangle_1686" data-name="Rectangle 1686" width="260" height="240" rx="6" transform="translate(1161 -621)" fill="#fe7000"/>
    </clipPath>
    <filter id="Rectangle_1685" x="-9" y="-6" width="278" height="258" filterUnits="userSpaceOnUse">
      <feOffset dy="3" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="3" result="blur"/>
      <feFlood flood-opacity="0.161"/>
      <feComposite operator="in" in2="blur"/>
      <feComposite in="SourceGraphic"/>
    </filter>
  </defs>
  <g id="Mask_Group_10" data-name="Mask Group 10" transform="translate(-1161 621)" clip-path="url(#clip-path)">
    <g transform="matrix(1, 0, 0, 1, 1161, -621)" filter="url(#Rectangle_1685)">
      <rect id="Rectangle_1685-2" data-name="Rectangle 1685" width="260" height="240" rx="6" fill="#fe7000"/>
    </g>
    <ellipse id="Ellipse_124" data-name="Ellipse 124" cx="45.5" cy="45" rx="45.5" ry="45" transform="translate(1298 -641)" fill="#ffad6c"/>
    <circle id="Ellipse_125" data-name="Ellipse 125" cx="51.5" cy="51.5" r="51.5" transform="translate(1340 -637)" fill="#f69a51"/>
  </g>
</svg>
