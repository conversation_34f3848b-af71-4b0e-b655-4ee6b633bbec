<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="260" height="240" viewBox="0 0 260 240">
  <defs>
    <clipPath id="clip-path">
      <rect id="Rectangle_1683" data-name="Rectangle 1683" width="260" height="240" rx="6" transform="translate(565 -621)" fill="#2ca779"/>
    </clipPath>
    <filter id="Rectangle_1682" x="-9" y="-6" width="278" height="258" filterUnits="userSpaceOnUse">
      <feOffset dy="3" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="3" result="blur"/>
      <feFlood flood-opacity="0.161"/>
      <feComposite operator="in" in2="blur"/>
      <feComposite in="SourceGraphic"/>
    </filter>
  </defs>
  <g id="Mask_Group_8" data-name="Mask Group 8" transform="translate(-565 621)" clip-path="url(#clip-path)">
    <g transform="matrix(1, 0, 0, 1, 565, -621)" filter="url(#Rectangle_1682)">
      <rect id="Rectangle_1682-2" data-name="Rectangle 1682" width="260" height="240" rx="6" fill="#00b673"/>
    </g>
    <ellipse id="Ellipse_122" data-name="Ellipse 122" cx="45.5" cy="45" rx="45.5" ry="45" transform="translate(702 -641)" fill="#2fc690"/>
    <circle id="Ellipse_123" data-name="Ellipse 123" cx="51.5" cy="51.5" r="51.5" transform="translate(744 -637)" fill="#2ca779"/>
  </g>
</svg>
