<svg id="Group_1792" data-name="Group 1792" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="373.104" height="271.5" viewBox="0 0 373.104 271.5">
  <defs>
    <clipPath id="clip-path">
      <rect id="Rectangle_1716" data-name="Rectangle 1716" width="373.104" height="271.5" fill="none"/>
    </clipPath>
  </defs>
  <g id="Group_1791" data-name="Group 1791" clip-path="url(#clip-path)">
    <path id="Path_2691" data-name="Path 2691" d="M337.736,155.673c1.08,5.893,7.07,40.833-14.579,40.572-2.011-.024-4.022,10.016,4.712,14.605,6.388,3.356,8.255,3.09,8.255,3.09-2.388,5.706-12.715,12.734-24.53,12.734s-18.35-7.48-20.612-13.693c5.907-1.014,14.579-5.2,14.328-11.918s.251-19.652-6.018-29.8c-5.682-9.192,6.269-24.09,17.581-25.357s18.727-1.9,20.863,9.762" transform="translate(-133.202 -65.797)" fill="#ffd3c9"/>
    <path id="Path_2692" data-name="Path 2692" d="M354.005,234.8a14.373,14.373,0,0,1-7.931,2.008,11.164,11.164,0,0,1-5.028-1.267" transform="translate(-156.12 -106.364)" fill="none" stroke="#f7bdb5" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/>
    <path id="Path_2693" data-name="Path 2693" d="M306.188,175.766c-.94.775-9.143-3.17-10.934-16.1s5-17.021,9.614-18.067c3.727-.844,4.862-10.141,11.312-11.221,7.007-1.173,10.164,4.869,14.327,4.565,6.5-.476,8.967-7.277,14.328-4.614,6.032,3,7.893,17.244-5.279,18.307-7.054.569-18.569-5.23-23.691,2.377-3.121,4.636-.628,7.925-3.315,10.3s-6.789-4.786-8.816-.935,6.224,12.283,2.453,15.389" transform="translate(-135.023 -58.757)" fill="#362e94"/>
    <path id="Path_2694" data-name="Path 2694" d="M257.074,457.048c-1.219,5.7-1.9,11.094-2.631,21.49h76.282a160.063,160.063,0,0,0-2.393-21.49Z" transform="translate(-116.476 -207.038)" fill="#20196f"/>
    <path id="Path_2695" data-name="Path 2695" d="M100.988,292.156c-2.312,2.1-3.883,5.445-5.112,8.115-.965,2.1-3.652,3.032-5.609,3.94-3.747-1.993-7.126-3.737-8.587-4.348-3.074-1.286-6.252-1.7-1.645,2.643-2.758-1.011-5.066-1.817-6.214-2.129-3.275-.891-6.512-.855-.96,3.065-3.3-.444-6.1-.78-7.4-.839-3.695-.168-7.1.73,1.668,3.959-3.008.3-5.475.59-6.6.812-4.019.795-7.442,2.778,5.933,3.765,12.33.91,19.328,3.618,23.232,6.012.433-.1,10.993-8,10.993-8-1.375-1.546-1.121-6.753,1.553-13.057,2.517-5.933.6-5.619-1.25-3.937" transform="translate(-26.063 -131.864)" fill="#f7d3c9"/>
    <path id="Path_2696" data-name="Path 2696" d="M521.11,324.112a151.659,151.659,0,0,0,17.952-11.448c3.583-2.7,7.739-9.3,9.631-12.121-1.466-8.635-13.389-2.527-17.089,2.389-5.734,3.243-7.311,5.681-9.318,4.25-3.539-2.524,1.355-11.5-.715-14.214-2.922,1.323-3.984,8.9-7.344,12.748s-1.2,7.956-2.323,10.547Z" transform="translate(-234.333 -132.712)" fill="#f7d3c9"/>
    <path id="Path_2697" data-name="Path 2697" d="M189.759,279.568c-8.439,3.513-12.807,11.374-17.457,25.067s-13.291,32.616-13.291,32.616c-3.267-3.043-28.349-29.932-31.074-34.645-1.257.634-15.77,11.395-15.77,11.395,3.519,5.2,43.8,54.281,49.578,54.662s31.671-39.938,31.671-39.938Z" transform="translate(-51.346 -126.642)" fill="#6b67e9"/>
    <path id="Path_2698" data-name="Path 2698" d="M390.623,281.247c8.15,4.151,11.911,12.324,15.515,26.335s10.792,33.542,10.792,33.542c3.488-2.783,30.525-27.668,33.6-32.158,1.205.729,14.864,12.572,14.864,12.572-3.9,4.913-47.765,50.761-53.558,50.7s-28.566-42.254-28.566-42.254Z" transform="translate(-175.447 -127.402)" fill="#6b67e9"/>
    <path id="Path_2699" data-name="Path 2699" d="M331.671,274.685c-2.492-2.341-18.287-9.573-22.183-10.714-4.839,6.53-10.808,7.1-15.144,7.1s-10.306-.571-15.145-7.1c-3.9,1.141-24.781,6.946-27.273,9.287-4.116,3.867,2.262,84.346,3.016,96.327h71.913c.754-11.981,8.931-91.034,4.815-94.9" transform="translate(-114.695 -119.576)" fill="#6b67e9"/>
    <line id="Line_227" data-name="Line 227" x2="73.085" transform="translate(139.776 243.384)" fill="none" stroke="#5652cc" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
    <path id="Path_2700" data-name="Path 2700" d="M330.256,451.561l-3.58-6.626-3.58,6.626-3.58-6.626-3.58,6.626-3.58-6.626-3.58,6.626-3.58-6.626-3.58,6.626-3.58-6.626-3.581,6.626-3.58-6.626-3.58,6.626-3.58-6.626-3.58,6.626-3.58-6.626-3.58,6.626-3.58-6.626-3.58,6.626-3.58-6.626-3.58,6.626" transform="translate(-118.401 -201.551)" fill="none" stroke="#5652cc" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
    <path id="Path_2701" data-name="Path 2701" d="M391.272,397.993c1.972-16.564,4.37-56.3,5.567-71.312" transform="translate(-179.111 -147.983)" fill="none" stroke="#423eaf" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/>
    <path id="Path_2702" data-name="Path 2702" d="M250.731,318.444c.364,14.93,1.693,35.075,2.79,50.365" transform="translate(-114.776 -144.252)" fill="none" stroke="#423eaf" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/>
    <path id="Path_2703" data-name="Path 2703" d="M333.672,263.971c-4.839,6.53-10.809,7.1-15.145,7.1s-10.306-.571-15.145-7.1" transform="translate(-138.879 -119.576)" fill="none" stroke="#5652cc" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
    <path id="Path_2704" data-name="Path 2704" d="M333.571,270.039c-7.165,8.38-14.616,11.538-23.83,11.538s-16.213-3.233-25.552-12.047" transform="translate(-130.092 -122.094)" fill="none" stroke="#5652cc" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
    <path id="Path_2705" data-name="Path 2705" d="M298.186,264.74l-6.858,6.061,10.192-2.724-3.656,7.35,8.211-4.954-1.439,7.848,6.393-6.953,2.357,8.038,3.618-8.242,4.983,7.028.684-8.917,7.1,5.14-2.854-8.647,9.364,2.743" transform="translate(-133.36 -119.924)" fill="none" stroke="#5652cc" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
    <line id="Line_228" data-name="Line 228" y1="11.395" x2="15.77" transform="translate(60.821 175.965)" fill="none" stroke="#5652cc" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
    <line id="Line_229" data-name="Line 229" y1="12.015" x2="15.682" transform="translate(70.332 187.36)" fill="none" stroke="#5652cc" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
    <path id="Path_2706" data-name="Path 2706" d="M114.635,332.6l11.008,10.81-6.88-13.755,11.886,9.919L123.629,326.1l11.577,9.982-6.959-13.319" transform="translate(-52.476 -146.211)" fill="none" stroke="#5652cc" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
    <line id="Line_230" data-name="Line 230" x1="14.864" y1="12.572" transform="translate(275.081 181.563)" fill="none" stroke="#5652cc" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
    <line id="Line_231" data-name="Line 231" x1="14.73" y1="13.185" transform="translate(264.824 192.201)" fill="none" stroke="#5652cc" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
    <path id="Path_2707" data-name="Path 2707" d="M514.859,343.967,503.067,353.9l7.9-13.186-12.6,8.978,8.016-12.9-12.3,9.064,7.944-12.745" transform="translate(-226.175 -150.898)" fill="none" stroke="#5652cc" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
    <path id="Path_2708" data-name="Path 2708" d="M287.761,313.094l-6.441,9.948,6.165,10.124,6.441-9.947Z" transform="translate(-128.779 -141.829)" fill="#807dfb"/>
    <path id="Path_2709" data-name="Path 2709" d="M311.009,313.416l-6.441,9.948,6.165,10.124,6.441-9.948Z" transform="translate(-139.421 -141.974)" fill="#807dfb"/>
    <path id="Path_2710" data-name="Path 2710" d="M334.257,313.738l-6.441,9.948,6.165,10.124,6.441-9.948Z" transform="translate(-150.063 -142.12)" fill="#807dfb"/>
    <path id="Path_2711" data-name="Path 2711" d="M357.5,314.06l-6.441,9.948,6.165,10.124,6.441-9.947Z" transform="translate(-160.705 -142.266)" fill="#807dfb"/>
    <path id="Path_2712" data-name="Path 2712" d="M287.253,349.787l-6.441,9.948,6.165,10.124,6.441-9.947Z" transform="translate(-128.546 -158.45)" fill="#807dfb"/>
    <path id="Path_2713" data-name="Path 2713" d="M310.5,350.109l-6.441,9.948,6.165,10.123,6.441-9.947Z" transform="translate(-139.188 -158.596)" fill="#807dfb"/>
    <path id="Path_2714" data-name="Path 2714" d="M333.748,350.431l-6.441,9.948,6.165,10.124,6.441-9.947Z" transform="translate(-149.831 -158.742)" fill="#807dfb"/>
    <path id="Path_2715" data-name="Path 2715" d="M357,350.753l-6.441,9.948,6.165,10.124,6.441-9.947Z" transform="translate(-160.473 -158.888)" fill="#807dfb"/>
    <path id="Path_2716" data-name="Path 2716" d="M286.745,386.481l-6.441,9.948,6.165,10.124,6.441-9.947Z" transform="translate(-128.314 -175.072)" fill="#807dfb"/>
    <path id="Path_2717" data-name="Path 2717" d="M309.992,386.8l-6.441,9.948,6.165,10.123,6.441-9.947Z" transform="translate(-138.956 -175.218)" fill="#807dfb"/>
    <path id="Path_2718" data-name="Path 2718" d="M333.24,387.125l-6.441,9.948,6.165,10.124,6.441-9.947Z" transform="translate(-149.598 -175.364)" fill="#807dfb"/>
    <path id="Path_2719" data-name="Path 2719" d="M356.488,387.447l-6.441,9.948,6.165,10.123,6.441-9.947Z" transform="translate(-160.24 -175.51)" fill="#807dfb"/>
    <path id="Path_2720" data-name="Path 2720" d="M93.934,234.684a3.921,3.921,0,0,0-2.47-3.158,3.951,3.951,0,0,0,1.5-3.728,3.789,3.789,0,0,0-4.187-3.349l-33.564,4.794a3.813,3.813,0,0,0-3.1,4.389,3.928,3.928,0,0,0,2.47,3.16,4.016,4.016,0,0,0,.967,6.886,3.944,3.944,0,0,0-1.5,3.726,3.787,3.787,0,0,0,4.187,3.35l33.564-4.8a3.808,3.808,0,0,0,3.1-4.39,3.922,3.922,0,0,0-2.469-3.158,3.945,3.945,0,0,0,1.5-3.726" transform="translate(-23.838 -101.657)" fill="#6b67e9"/>
    <path id="Path_2721" data-name="Path 2721" d="M80.22,266.2l-16.782,2.4s3.69,5.935,4.607,6.352a4.307,4.307,0,0,0,2.859.144c1.4-.2,10.357-1.479,10.357-1.479l10.357-1.48a4.312,4.312,0,0,0,2.7-.938c.767-.66,2.679-7.4,2.679-7.4Z" transform="translate(-29.04 -119.499)" fill="#5652cc"/>
    <line id="Line_232" data-name="Line 232" x1="29.771" y2="4.253" transform="translate(34.302 130.375)" fill="none" stroke="#807dfb" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/>
    <line id="Line_233" data-name="Line 233" x1="29.769" y2="4.254" transform="translate(35.27 137.26)" fill="none" stroke="#807dfb" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/>
    <path id="Path_2722" data-name="Path 2722" d="M82.232,99.418a41.326,41.326,0,1,0-68.132,37.2c.087.079.484.374.592.457,10.7,8.361,16.274,16.365,16.687,19.313l16.782-2.4,16.783-2.4c-.413-2.942,2.772-12.156,10.721-23.159.093-.127.338-.514.38-.581a41.812,41.812,0,0,0,6.188-28.432" transform="translate(0 -28.8)" fill="#ffd03e"/>
    <path id="Path_2723" data-name="Path 2723" d="M35.952,159.368c-1.788-5.418-7.579-12.106-15.982-18.871l-1.283-1.118a39.1,39.1,0,0,1,19.8-68.15,38.752,38.752,0,0,1,43.689,33.143,39.232,39.232,0,0,1-5.909,26.785l-.931,1.434c-6.209,8.852-9.869,17.341-10.159,22.6Z" transform="translate(-2.404 -32.09)" fill="#fcba06"/>
    <path id="Path_2724" data-name="Path 2724" d="M103.489,77.54a41.836,41.836,0,0,1,15.6,27.155c1.413,10.066-1.905,20.441-7.415,28.941l-1,1.544c-6.672,9.512-10.606,18.633-10.916,24.289l8.68-1.24c.29-5.262,3.95-13.752,10.159-22.6l.93-1.434A39.153,39.153,0,0,0,103.489,77.54" transform="translate(-45.667 -35.125)" fill="#e19100"/>
    <path id="Path_2725" data-name="Path 2725" d="M10.277,108.36c-1.129,3.6-.487,7.015,1.433,7.629s4.392-1.808,5.522-5.409.487-7.013-1.432-7.628-4.392,1.808-5.523,5.408" transform="translate(-4.449 -46.593)" fill="#fff"/>
    <path id="Path_2726" data-name="Path 2726" d="M61.977,138.788l-1.751.25a32.869,32.869,0,0,1-.556-7.217,28.546,28.546,0,0,1,1.86-6.925,35.472,35.472,0,0,0,2.01-7.1,23.7,23.7,0,0,0-.169-6.327q-.8-5.73-3.406-8.128a6.667,6.667,0,0,0-5.635-1.964,6.145,6.145,0,0,0-3.711,1.8,2.641,2.641,0,0,0-.918,2.153,5.283,5.283,0,0,0,1.114,2.039,12.316,12.316,0,0,1,2.629,5.382,4.837,4.837,0,0,1-.935,3.738,4.916,4.916,0,0,1-3.367,1.931,5.363,5.363,0,0,1-4.254-1.128,6.3,6.3,0,0,1-2.255-4.271,11.726,11.726,0,0,1,3.1-9.541q3.819-4.457,11.635-5.574,8.286-1.183,13.181,2.183A12.7,12.7,0,0,1,76.24,109.1a13.912,13.912,0,0,1-1.16,7.88q-1.735,3.786-7.506,8.917-3.872,3.454-4.906,5.866a15.577,15.577,0,0,0-.692,7.021" transform="translate(-19.464 -44.213)" fill="#ffdd4d"/>
    <path id="Path_2727" data-name="Path 2727" d="M74.116,184.455a6.656,6.656,0,0,1,5.153,1.33,6.767,6.767,0,0,1,2.7,4.625,6.87,6.87,0,0,1-1.315,5.221,6.886,6.886,0,0,1-9.739,1.391,7.107,7.107,0,0,1-1.382-9.846,6.686,6.686,0,0,1,4.585-2.721" transform="translate(-31.19 -83.521)" fill="#ffdd4d"/>
    <path id="Path_2728" data-name="Path 2728" d="M549.9,229.1a4.211,4.211,0,0,0-1.548-4,4.191,4.191,0,0,0,2.685-3.334,4.071,4.071,0,0,0-3.242-4.734l-35.749-5.637a4.045,4.045,0,0,0-4.52,3.509,4.219,4.219,0,0,0,1.548,4,4.287,4.287,0,0,0-1.136,7.335,4.183,4.183,0,0,0-2.683,3.332A4.067,4.067,0,0,0,508.5,234.3l35.749,5.636a4.039,4.039,0,0,0,4.518-3.51,4.21,4.21,0,0,0-1.547-4A4.185,4.185,0,0,0,549.9,229.1" transform="translate(-231.265 -95.738)" fill="#6b67e9"/>
    <path id="Path_2729" data-name="Path 2729" d="M529.153,256.134l-17.874-2.818s1.938,7.217,2.745,7.932,1.38.808,2.875,1.044,11.031,1.739,11.031,1.739l11.031,1.739c1.494.235,2.066.326,3.051-.108s5.016-6.71,5.016-6.71Z" transform="translate(-234.046 -114.75)" fill="#5652cc"/>
    <line id="Line_234" data-name="Line 234" x1="31.71" y1="5" transform="translate(281.595 123.762)" fill="none" stroke="#807dfb" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/>
    <line id="Line_235" data-name="Line 235" x1="31.707" y1="4.999" transform="translate(280.461 131.097)" fill="none" stroke="#807dfb" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/>
    <path id="Path_2730" data-name="Path 2730" d="M571.959,91.731a44.112,44.112,0,1,0-81.037,16.6c.065.108.378.534.463.652,8.347,11.895,11.571,21.817,11.083,24.957l17.874,2.82,17.876,2.817c.486-3.134,6.579-11.542,18.088-20.283.134-.1.5-.418.567-.475a44.408,44.408,0,0,0,15.085-27.088" transform="translate(-221.686 -18.284)" fill="#ffd03e"/>
    <path id="Path_2731" data-name="Path 2731" d="M507.006,137.816c-.156-6.093-4.005-14.739-10.5-24.284l-.966-1.544a41.875,41.875,0,0,1-5.9-28.68,41.267,41.267,0,1,1,67.23,38.354l-1.392,1.173c-9.069,7.089-15.424,14.609-17.343,19.891Z" transform="translate(-223.914 -21.795)" fill="#fcba06"/>
    <path id="Path_2732" data-name="Path 2732" d="M591.513,67.62a44.877,44.877,0,0,1,7.558,32.624c-1.661,10.721-8.248,20.273-16.495,27.223l-1.5,1.263c-9.745,7.619-16.576,15.7-18.637,21.375l9.245,1.458c1.918-5.281,8.273-12.8,17.343-19.891l1.393-1.172a41.837,41.837,0,0,0,1.09-62.879" transform="translate(-257.468 -30.631)" fill="#e19100"/>
    <path id="Path_2733" data-name="Path 2733" d="M505.456,70.444c-2.263,3.323-2.66,7.01-.889,8.239s5.042-.468,7.305-3.79,2.66-7.008.889-8.238-5.042.468-7.306,3.789" transform="translate(-230.467 -30.013)" fill="#fff"/>
    <path id="Path_2734" data-name="Path 2734" d="M552.937,118.537l-2.061-.325a79.241,79.241,0,0,0-.134-13.384L549.9,94.14q-.768-9.61-.144-13.628a8.134,8.134,0,0,1,3.063-5.52,7.555,7.555,0,0,1,5.966-1.335,7.671,7.671,0,0,1,5.25,3.128,7.867,7.867,0,0,1,1.343,6.046q-.556,3.592-4.422,13.053l-4.08,9.912a87.538,87.538,0,0,0-3.942,12.741m-2.031,6.6a7.394,7.394,0,0,1,5.017,3.091,7.574,7.574,0,0,1,1.379,5.81,7.474,7.474,0,0,1-3.064,5.06,7.627,7.627,0,0,1-10.729-1.691,7.512,7.512,0,0,1-1.387-5.763,7.594,7.594,0,0,1,3.044-5.088,7.289,7.289,0,0,1,5.739-1.419" transform="translate(-248.119 -33.31)" fill="#ffdd4d"/>
    <line id="Line_236" data-name="Line 236" y1="12.971" x2="2.01" transform="translate(314.765 1.094)" fill="none" stroke="#ffdd4d" stroke-linecap="round" stroke-linejoin="round" stroke-width="4"/>
    <line id="Line_237" data-name="Line 237" x1="3.612" y1="12.612" transform="translate(288.399 2.934)" fill="none" stroke="#ffdd4d" stroke-linecap="round" stroke-linejoin="round" stroke-width="4"/>
    <line id="Line_238" data-name="Line 238" x1="8.558" y1="9.89" transform="translate(263.453 16.699)" fill="none" stroke="#ffdd4d" stroke-linecap="round" stroke-linejoin="round" stroke-width="4"/>
    <line id="Line_239" data-name="Line 239" x1="11.899" y1="5.315" transform="translate(246.611 39.81)" fill="none" stroke="#ffdd4d" stroke-linecap="round" stroke-linejoin="round" stroke-width="4"/>
    <line id="Line_240" data-name="Line 240" x1="13.011" y2="0.255" transform="translate(241.029 67.681)" fill="none" stroke="#ffdd4d" stroke-linecap="round" stroke-linejoin="round" stroke-width="4"/>
    <line id="Line_241" data-name="Line 241" x1="11.685" y2="5.779" transform="translate(247.752 90.028)" fill="none" stroke="#ffdd4d" stroke-linecap="round" stroke-linejoin="round" stroke-width="4"/>
    <line id="Line_242" data-name="Line 242" y1="10.899" x2="7.255" transform="translate(336.125 11.604)" fill="none" stroke="#ffdd4d" stroke-linecap="round" stroke-linejoin="round" stroke-width="4"/>
    <line id="Line_243" data-name="Line 243" y1="6.784" x2="11.141" transform="translate(351.831 32.393)" fill="none" stroke="#ffdd4d" stroke-linecap="round" stroke-linejoin="round" stroke-width="4"/>
    <line id="Line_244" data-name="Line 244" y1="1.399" x2="12.939" transform="translate(359.08 59.586)" fill="none" stroke="#ffdd4d" stroke-linecap="round" stroke-linejoin="round" stroke-width="4"/>
    <line id="Line_245" data-name="Line 245" x2="12.313" y2="4.249" transform="translate(356.514 83.84)" fill="none" stroke="#ffdd4d" stroke-linecap="round" stroke-linejoin="round" stroke-width="4"/>
    <line id="Line_246" data-name="Line 246" x2="9.38" y2="9.101" transform="translate(344.614 103.46)" fill="none" stroke="#ffdd4d" stroke-linecap="round" stroke-linejoin="round" stroke-width="4"/>
  </g>
</svg>
