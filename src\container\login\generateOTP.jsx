import React, { useState } from 'react';
import { Container, Grid, Typo<PERSON>, <PERSON>, TextField } from '@mui/material';
import { Link as RouterLink } from 'react-router-dom';
import { makeStyles } from '@mui/styles';
import { Form, Formik } from 'formik';
import * as Yup from 'yup';
import { LoadingButton } from '@mui/lab';
import BackgroundImg from '../../assets/logo/images/bg.jpg';
import loginServices from '../../services/loginServices';
import SnackBar from '../../components/snackbar/snackbar';

const ForgetPassword = (props) => {
  const classes = useStyles();
  const [loading, setLoading] = useState(false);
  const [openSnackbar, setOpenSnackbar] = useState(false);
  const [snackbarTitle, setSnackbarTitle] = useState('');

  const handleSubmitForm = async (values) => {
    console.log(values);
    setLoading(true);
    const data = {
      emailAddress: values.email,
    };

    const response = await loginServices.generateOTP(data);
    if (response.ok) {
      setSnackbarTitle(response.data);
      setOpenSnackbar(true);
    } else {
      setSnackbarTitle(response.data.message);
      setOpenSnackbar(true);
    }
    setLoading(false);
  };

  return (
    <Container>
      <Formik
        initialValues={{
          email: '',
        }}
        validationSchema={Yup.object().shape({
          email: Yup.string().required('Email is required').email(),
        })}
        onSubmit={(values) => {
          handleSubmitForm(values);
        }}
      >
        {({ errors, handleBlur, setFieldValue, touched, values }) => (
          <Form>
            <Grid className={classes.form} container spacing={2}>
              <Grid item xs="12">
                <Typography color="primary" className={classes.login} align="left" variant="h4">
                  Generate OTP
                </Typography>
              </Grid>

              <Grid item xs="12">
                <Typography className={classes.email} variant="subtitle1">
                  Email
                </Typography>
                <TextField
                  fullWidth
                  name="email"
                  variant="outlined"
                  onBlur={handleBlur}
                  onChange={(e) => {
                    setFieldValue('email', e.target.value.trim());
                  }}
                  error={Boolean(touched.email && errors.email)}
                  helperText={touched.email && errors.email}
                  value={values.email}
                />
              </Grid>

              <Grid align="center" item xs="12">
                <LoadingButton fullWidth size="medium" type="submit" variant="contained" loading={loading}>
                  Submit
                </LoadingButton>
              </Grid>
              <Grid align="center" item xs="12">
                <Typography variant="body2" align="center" sx={{ mt: 3 }}>
                  Do you have a valid OTP?{' '}
                  <Link
                    variant="subtitle2"
                    component={RouterLink}
                    to="/login"
                    onClick={() => props.setforgotOTP(true)}
                  >
                    Login
                  </Link>
                </Typography>
              </Grid>
            </Grid>
          </Form>
        )}
      </Formik>
      <SnackBar open={openSnackbar} snackbarTitle={snackbarTitle} close={() => setOpenSnackbar(false)} />
    </Container>
  );
};
export default ForgetPassword;

const useStyles = makeStyles((theme) => ({
  boxContainer: {
    backgroundImage: `url(${BackgroundImg})`,
    backgroundSize: 'cover',
    height: '100vh',
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
  },
  gridContainer: {
    background: '#fff',
    borderRadius: '8px',
    boxShadow: '0px 3px 6px #00000029',
    [theme.breakpoints.down('md')]: {
      marginTop: '4rem',
    },
  },
  grid: {
    display: 'flex',
    justifyContent: 'space-between',
  },
  form: {
    paddingBottom: '2rem',
    paddingTop: '2rem',
  },
  divider: {
    height: '2px',
    width: '145px',
    backgroundColor: 'black',
  },
  keySkillsetLogo: {
    position: 'fixed',
    left: '2rem',
    top: '2rem',
  },
  ok: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  email: {
    padding: '4px',
  },
}));
