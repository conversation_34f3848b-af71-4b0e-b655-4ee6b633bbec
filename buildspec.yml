version: 0.2
phases:
  install:
    runtime-versions:
      nodejs: 22.14.0
    commands:
      - echo ${CURRENT_ENVIRONMENT}
      - echo Parameter WEBSITE_BUCKET has value ${WEBSITE_BUCKET}
      - aws configure set region us-east-1
      - API_ENDPOINT=$(aws ssm get-parameter --name /${CURRENT_ENVIRONMENT}/web/config/API_ENDPOINT --query 'Parameter.Value' --output text)
      - echo ${API_ENDPOINT}

      - echo Installing source NPM dependencies...
      - aws configure set region us-east-2
      - rm -rf node_modules
      - rm -f package-lock.json
      - rm -f yarn.lock
      - npm cache clean --force
      - npm install --legacy-peer-deps
      - npm install react-joyride@2.5.3 --force
      - echo API endpoint it ${API_ENDPOINT}
      - echo REACT_APP_ENV=${CURRENT_ENVIRONMENT} >> .env
      - echo REACT_APP_API_ENDPOINT=${API_ENDPOINT} >> .env
      - cat .env
  pre_build:
    commands:
      - echo No Pre build commands to execute...
  build:
    commands:
      - echo Buld started on 'date'
      - npm run build
  post_build:
    commands:
      - echo Deleting files from the s3 bucket ${WEBSITE_BUCKET}
      - aws s3 rm --recursive s3://${WEBSITE_BUCKET}
      # - echo Copying files to S3
  #     - aws s3 cp --recursive ./build s3://keyskillset-web-dev
  #     - echo Copying Index.html to S3
  #     - aws s3 cp --cache-control="max-age=0, no-cache, no-store, must-revalidate" ./build/index.html s3://keyskillset-web-dev
  #     - echo Invalidating the cloudfront distribution
  #     - aws cloudfront create-invalidation --distribution-id E1IYJG7YMMGJDL --paths /index.html
artifacts:
  files:
    - "**/*"
    # - "build/*"
    # - "build/**/*"
  base-directory: "build"
# cache:
#   paths:
#     - node_modules/**/*
