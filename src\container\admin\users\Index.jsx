import React, { useEffect } from 'react';
import { Breadcrumbs, Grid, Typography, Box } from '@mui/material';
import PropTypes from 'prop-types';
import Tabs from '@mui/material/Tabs';
import Tab from '@mui/material/Tab';
import BusinessIcon from '@mui/icons-material/Business';
import PersonIcon from '@mui/icons-material/Person';
import { Link as RouterLink, useLocation } from 'react-router-dom';

// custom components
import Page from '../../../components/Page';
import '../clientDetails/Tabs.css';
import Modal from '../../../components/modal/Index';
import ClientList from './ClientList';
import Individual from './IndividualList';
import AddClientForm from './AddClientForm';
import PageHeader from '../../../components/PageHeader';
import SnackBar from '../../../components/snackbar/snackbar';

function TabPanel(props) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          <Typography>{children}</Typography>
        </Box>
      )}
    </div>
  );
}

TabPanel.propTypes = {
  children: PropTypes.node,
  index: PropTypes.number.isRequired,
  value: PropTypes.number.isRequired,
};

function a11yProps(index) {
  return {
    id: `simple-tab-${index}`,
    'aria-controls': `simple-tabpanel-${index}`,
  };
}

export default function Index() {
  const { pathname } = useLocation();
  const [value, setValue] = React.useState(0);
  const [mode, setMode] = React.useState('');
  const [clientId, setClientId] = React.useState(null);
  const [modalOpen, setModalOpen] = React.useState(false);
  const [trigerData, setTrigerData] = React.useState(false);
  const [openSnackbar, setOpenSnackbar] = React.useState(false);
  const [snackbarTitle, setSnackbarTitle] = React.useState('');

  useEffect(() => {
    if (pathname === '/app/individual-users') {
      setValue(1);
    } else {
      setValue(0);
    }
  }, [pathname]);

  const handleChange = (event, newValue) => {
    setValue(newValue);
  };

  const handleModalOpen = () => {
    setModalOpen(true);
  };

  const handleModalClose = () => {
    setModalOpen(false);
  };

  const addClientButton = () => {
    handleModalOpen(true);
    setMode('create');
  };

  const handleEditButton = (id) => {
    setMode('edit');
    setClientId(id);
    setModalOpen(true);
  };

  const snakbarHandle = (message) => {
    setOpenSnackbar(true);
    setSnackbarTitle(message);
  };

  const apiTrigerdData = (value) => {
    setTrigerData(!value);
  };

  const Breadcrumb = () => (
    <Breadcrumbs aria-label="breadcrumb" separator="›">
      <Typography
        sx={{ textDecoration: 'none' }}
        variant="body2"
        color="primary"
        component={RouterLink}
        to="/app/dashboard"
      >
        Dashboard
      </Typography>
      <Typography variant="body2" color="textPrimary">
        Users
      </Typography>
    </Breadcrumbs>
  );

  return (
    <div>
      <Page title={'users'}>
        <PageHeader pageTitle="Users" breadcrumbs={<Breadcrumb />} />
        <Box style={{ padding: '0px' }}>
          <Grid container spacing={2}>
            <Grid item xs="12">
              <Box>
                <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
                  <Tabs value={value} onChange={handleChange} aria-label="basic tabs example">
                    <Tab iconPosition="start" icon={<BusinessIcon />} label="Client" {...a11yProps(0)} />
                    <Tab iconPosition="start" icon={<PersonIcon />} label="Individual" {...a11yProps(1)} />
                  </Tabs>
                </Box>
                <TabPanel value={value} index={0}>
                  <ClientList
                    addClientButton={addClientButton}
                    handleEditButton={handleEditButton}
                    getTrigerData={apiTrigerdData}
                  />
                </TabPanel>
                <TabPanel value={value} index={1}>
                  <Individual handleEditButton={handleEditButton} getTrigerData={apiTrigerdData} />
                </TabPanel>
              </Box>
            </Grid>
          </Grid>
        </Box>

        <Modal
          title={mode === 'create' ? 'Add Clients' : 'Edit Clients'}
          open={modalOpen}
          toggle={handleModalClose}
          children={
            <AddClientForm
              mode={mode}
              clientId={clientId}
              apiTriger={apiTrigerdData}
              toggle={handleModalClose}
              snackBarControl={snakbarHandle}
            />
          }
        />
        <SnackBar open={openSnackbar} snackbarTitle={snackbarTitle} close={() => setOpenSnackbar(false)} />
      </Page>
    </div>
  );
}
