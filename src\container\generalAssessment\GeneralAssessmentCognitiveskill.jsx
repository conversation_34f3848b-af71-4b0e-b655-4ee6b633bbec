import React from "react";
import { useTranslation } from 'react-i18next';
import { CircularProgressbar, buildStyles } from "react-circular-progressbar";
import "react-circular-progressbar/dist/styles.css";
import ImageView from '../../assets/Images/ProgressFrame.png'

const ProgressBar = () => {
  const styles = {
    container: {
      placeItems: 'flex-start',
      backgroundColor: "#0b1124",
      color: "white",
      display: "flex",
      flexDirection: "column",
      alignItems: "center",
      justifyContent: "center",
      // height: "60vh",
      width:'100%',
      textAlign: "center",
    },
    progressContainer: {
      marginBottom: "20px",
      marginTop: "20px",
      width: "150px",
      height: "150px", 
    },
    percentageText: {
      marginTop: "10px",
      fontSize: "10px",
      whiteSpace: "nowrap",
      overflow: "hidden",
      textOverflow: "ellipsis",
    },
    imageContainer: {
      margin: "20px 0",
    },
    illustration: {
      width: "150px", 
    },
    footer: {
      fontSize: "16px",
      fontWeight: "bold",
    },
  };

  const { t } = useTranslation('translation');

  return (
    <div style={styles.container}>
      <div style={styles.progressContainer}>
        <CircularProgressbar
          value={0} 
          text={`${0.00}%`}
          styles={buildStyles({
            textColor: "white",
            pathColor: "#ffffff",
            trailColor: "#ffffff",
            textSize: "10px",
          })}
        />
        <p style={styles.percentageText}>{t("Correct Answers in percentage")}</p>
      </div>
      <div style={styles.imageContainer}>
        <img src={ImageView} alt="Brain Illustration" style={styles.illustration} />
      </div>
    
    </div>
  );
};

export default ProgressBar;