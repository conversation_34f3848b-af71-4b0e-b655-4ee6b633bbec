/* eslint-disable no-restricted-syntax */
/* eslint-disable no-await-in-loop */
import React, { useEffect, useRef, useState, useImperativeHandle, forwardRef } from 'react';
import ReactQuill, { Quill } from 'react-quill';
import 'react-quill/dist/quill.snow.css';
import 'katex/dist/katex.min.css';
import katex from 'katex';
import adminServices from '../../services/adminServices';
 
let idCounter = 0;
const generateUniqueId = () => {
    const currentId = idCounter;
    idCounter += 1;            
    return `quill-toolbar-${Date.now()}-${currentId}-${Math.random().toString(36).substring(2, 9)}`;
};
 
const loadMathJax = () => {
    if (!window.MathJax) {
        window.MathJax = {
            tex: {
                packages: ['base', 'ams', 'mhchem']
            }
        };
        const script = document.createElement('script');
        script.src = 'https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js';
        script.async = true;
        document.head.appendChild(script);
    }
};
const CustomToolbar = ({ onSymbolInsert, id, onImageInsert, onFormulaInsert}) => {
    const [activeDropdown, setActiveDropdown] = useState(null);
 
    const categories = {
        temp: { label: 'Temperature', icon: '°C', options: [{ label: '°C', value: '°C' }, { label: '°F', value: '°F' }, { label: 'K', value: 'K' }, { label: 'ΔT', value: 'ΔT' }, { label: 'θ', value: 'θ' }, { label: '∇T', value: '∇T' }] },
        greekCap: { label: 'Greek Capital Letters', icon: 'ABΓ', options: [{ label: 'Α', value: 'Α' }, { label: 'Β', value: 'Β' }, { label: 'Γ', value: 'Γ' }, { label: 'Δ', value: 'Δ' }, { label: 'Ε', value: 'Ε' }, { label: 'Ζ', value: 'Ζ' }, { label: 'Η', value: 'Η' }, { label: 'Θ', value: 'Θ' }, { label: 'Ι', value: 'Ι' }, { label: 'Κ', value: 'Κ' }, { label: 'Λ', value: 'Λ' }, { label: 'Μ', value: 'Μ' }, { label: 'Ν', value: 'Ν' }, { label: 'Ξ', value: 'Ξ' }, { label: 'Ο', value: 'Ο' }, { label: 'Π', value: 'Π' }, { label: 'Ρ', value: 'Ρ' }, { label: 'Σ', value: 'Σ' }, { label: 'Τ', value: 'Τ' }, { label: 'Υ', value: 'Υ' }, { label: 'Φ', value: 'Φ' }, { label: 'Χ', value: 'Χ' }, { label: 'Ψ', value: 'Ψ' }, { label: 'Ω', value: 'Ω' }] },
        greekSmall: { label: 'Greek Small Letters', icon: 'αβγ', options: [{ label: 'α', value: 'α' }, { label: 'β', value: 'β' }, { label: 'γ', value: 'γ' }, { label: 'δ', value: 'δ' }, { label: 'ε', value: 'ε' }, { label: 'ζ', value: 'ζ' }, { label: 'η', value: 'η' }, { label: 'θ', value: 'θ' }, { label: 'ι', value: 'ι' }, { label: 'κ', value: 'κ' }, { label: 'λ', value: 'λ' }, { label: 'μ', value: 'μ' }, { label: 'ν', value: 'ν' }, { label: 'ξ', value: 'ξ' }, { label: 'ο', value: 'ο' }, { label: 'π', value: 'π' }, { label: 'ρ', value: 'ρ' }, { label: 'σ', value: 'σ' }, { label: 'ς', value: 'ς' }, { label: 'τ', value: 'τ' }, { label: 'υ', value: 'υ' }, { label: 'φ', value: 'φ' }, { label: 'χ', value: 'χ' }, { label: 'ψ', value: 'ψ' }, { label: 'ω', value: 'ω' }] },
        math: { label: 'Math Operators', icon: '±√', options: [{ label: '±', value: '±' }, { label: '√', value: '√' }, { label: '∛', value: '∛' }, { label: '∫', value: '∫' }, { label: '∞', value: '∞' }, { label: '≈', value: '≈' }, { label: '≠', value: '≠' }, { label: '≤', value: '≤' }, { label: '≥', value: '≥' }] },
        // sub: { label: 'Subscripts', icon: 'X₁₂', options: '₀₁₂₃₄₅₆₇₈₉₊₋₌₍₎'.split('').map((char) => ({ label: char, value: char })) },
        // super: { label: 'Superscripts', icon: 'X¹²', options: '⁰¹²³⁴⁵⁶⁷⁸⁹⁺⁻⁼⁽⁾'.split('').map((char) => ({ label: char, value: char })) },
        arrows: { label: 'Arrows', icon: '→←', options: '→←↑↓↔↕⇒⇐⇑⇓⇔⇕↗↘↙↖'.split('').map((char) => ({ label: char, value: char })) }
    };
 
    const handleSymbolClick = (symbol) => {
        onSymbolInsert(symbol);
        setActiveDropdown(null);
    };
 
    const toggleDropdown = (category) => {
        setActiveDropdown(activeDropdown === category ? null : category);
    };
 
    return (
        <div id={id} style={styles.toolbar}>
            <span className="ql-formats">
                <select className="ql-font"/>
                <select className="ql-size"/>
            </span>
            <span className="ql-formats">
                <button className="ql-bold"/>
                <button className="ql-italic"/>
                <button className="ql-underline"/>
                <button className="ql-strike"/>
            </span>
            <span className="ql-formats">
                <select className="ql-color"/>
                <select className="ql-background"/>
            </span>
            <span className="ql-formats">
                <button className="ql-script" value="sub"/>
                <button className="ql-script" value="super"/>
            </span>
            <span className="ql-formats">
                <button className="ql-list" value="ordered"/>
                <button className="ql-list" value="bullet"/>
            </span>
            <span className="ql-formats">
                 {/* <button className="ql-link"/> */}
                    <button className="ql-image" onClick={onImageInsert}/>
                    <button className="ql-formula" onClick={onFormulaInsert}/>
            </span>
 
            <span className="ql-formats" style={styles.symbolSection}>
                {Object.entries(categories).map(([key, cat]) => (
                    <div key={key} style={styles.symbolDropdown}>
                        <button
                            type="button"
                            style={{
                                ...styles.symbolButton,
                                ...(activeDropdown === key ? styles.symbolButtonActive : {})
                            }}
                            onClick={() => toggleDropdown(key)}
                            title={cat.label}
                        >
                            {cat.icon}
                        </button>
                        {activeDropdown === key && (
                            <div style={styles.dropdownMenu}>
                                <div style={styles.dropdownHeader}>{cat.label}</div>
                                <div style={styles.optionsGrid}>
                                    {cat.options.map((opt) => (
                                        <button
                                            key={opt.value}
                                            type="button"
                                            style={styles.symbolOption}
                                            onClick={() => handleSymbolClick(opt.value)}
                                            title={opt.label}
                                        >
                                            {opt.label}
                                        </button>
                                    ))}
                                </div>
                            </div>
                        )}
                    </div>
                ))}
            </span>
        </div>
    );
};
 
const QuillEditor = forwardRef(({
    onChange,    
    onPaste,    
    maxLength,  
    modules,    
    formats,    
    theme = "snow",
    id,
    ...restProps
}, ref) =>
  {
 
   
    const quillRef = useRef(null);
    const toolbarId = useRef(generateUniqueId());
    const [editorLoaded, setEditorLoaded] = useState(false);
 
    useEffect(() => {
        loadMathJax();
        setEditorLoaded(true);
 
    }, []);
 
    // useImperativeHandle(ref, () => ({
    //     getEditor: () => quillRef.current?.getEditor()
    // }));

    useImperativeHandle(ref, () => ({
        getEditor: () => quillRef.current?.getEditor?.(),
        container: quillRef.current?.editor?.container
      }));

      
    
 
    useEffect(() => {
        if (editorLoaded && window.MathJax && window.MathJax.typeset) {
            window.MathJax.typeset();
        }
    }, [editorLoaded, restProps.value]);
 
    const insertSymbol = (symbol) => {
        const editor = quillRef.current?.getEditor();
        if (!editor) return;
 
        const range = editor.getSelection(true);
        if (!range) return;
 
        editor.insertText(range.index, symbol);
        editor.setSelection(range.index + symbol.length);
 
        setTimeout(() => {
            if (window.MathJax?.typeset) window.MathJax.typeset();
        }, 50);
    };
 
    const handleInternalPaste = (e) => {
        e.preventDefault();
        const clipboardText = e.clipboardData.getData('text');
        const editor = quillRef.current?.getEditor();
        if (editor) {
            const range = editor.getSelection(true);
            if (range) {
                if (maxLength !== undefined && maxLength !== null) {
                    const currentLength = editor.getText().length - 1;
                    const charsToPaste = maxLength - currentLength;
 
                    if (charsToPaste > 0) {
                        const pasteContent = clipboardText.slice(0, charsToPaste);
                        editor.insertText(range.index, pasteContent);
                    }
                    editor.setSelection(range.index + Math.min(clipboardText.length, charsToPaste));
                } else {
                    editor.insertText(range.index, clipboardText);
                    editor.setSelection(range.index + clipboardText.length);
                }
            }
        }
        if (onPaste) {
            onPaste(e);
        }
    };
 
    const handleInternalChange = (content, delta, source, editor) => {
        if (maxLength !== undefined && maxLength !== null) {
            const currentTextLength = editor.getText().length - 1;
            if (currentTextLength > maxLength) {
                const trimmedText = editor.getText().substring(0, maxLength);
                editor.setText(trimmedText);
            }
        }
 
        if (onChange) {
            onChange(content, delta, source, editor);
        }
    };
 
    const handleImageInsert = () => {
      imageHandler(quillRef);
    };
 
    const handleFormulaInsert = () => {
      formulaHandler(quillRef);
    };
 
     
    const imageHandler = () => {
      const input = document.createElement('input');
      input.setAttribute('type', 'file');
      input.setAttribute('accept', 'image/*');
      // input.click();
   
    };
   
   
    const formulaHandler = (quillRef) => {
   
        const range = quillRef.current.getEditor().getSelection(true);
        quillRef.current.getEditor().insertEmbed(range.index, '');
   
    };
 
    const handleImageUpload = () => {
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = 'image/*';
        input.click();
   
        input.onchange = async () => {
          const file = input.files?.[0];
          if (!file) return;
   
          const url = await handleUploadImage(file);
          if (url) {
            const quill = quillRef.current?.getEditor();
            const range = quill.getSelection(true);
            quill.insertEmbed(range.index, 'image', url);
            quill.setSelection(range.index + 1);
          }
        };
      };
 
 
          const handleUploadImage = async (file) => {
              const formData = new FormData();
              formData.append("thumbImage", file);
              try {
                const imageUrl = await adminServices.postImag(formData);
                if (imageUrl?.ok) {
                  console.log(imageUrl.data,"3333333");
                 
                  return imageUrl.data;
                }
              } catch (error) {
                console.error("Image upload failed", error);
              }
              return null;
            };
         
            const defaultModules = {
                toolbar: {
                    container: `#${toolbarId.current}`,
                    handlers: {
                        image: handleImageUpload,
                    },
                },
                clipboard: {
                    matchVisual: false,
         
                },
                imageResize: {
                    parchment: Quill.import('parchment'),
                    modules: ['Resize', 'DisplaySize', 'Toolbar'],
                },
         
            };
        
   
 
 
        //    const modules = React.useMemo(() => ({
        //       toolbar: {
        //         container: [
        //           ["bold", "italic", "underline"],
        //           [{ list: "ordered" }, { list: "bullet" }],
        //           [{ script: "sub" }, { script: "super" }],
        //           [{ header: [1, 2, 3, false] }],
        //           ["image"],
        //           [{ color: [] }, { background: [] }],
        //           [{ align: [] }],
        //           ["clean"],
        //         ],
        //         handlers: {
        //           image: imageHandler, // override default
        //         },
        //       },
        //       clipboard: { matchVisual: false },
        //     }), []);
 
        const mergedModules = {
            ...modules,
            toolbar: modules?.toolbar === false ? false : {
                container: `#${toolbarId.current}`,
                handlers: {
                    image: handleImageUpload,
                    ...modules?.toolbar?.handlers,
                }
            },
            clipboard: {
                matchVisual: false,
            },
            imageResize: {
                parchment: Quill.import('parchment'),
                modules: ['Resize', 'DisplaySize', 'Toolbar'],
            },
        }
 
    // const mergedModules = {
    //     ...defaultModules,
    //     ...modules,
    //     toolbar:
    //       modules?.toolbar === false
    //         ? false
    //         : {
    //             // Always keep default toolbar container (so design stays)
    //             container: defaultModules.toolbar.container,
     
    //             // Merge existing handlers with custom image handler
    //             handlers: {
    //               ...(defaultModules.toolbar.handlers || {}),
    //               ...(modules?.toolbar?.handlers || {}),
    //               image: handleImageUpload,
    //             },
    //           },
    //   };
      // later, after quill is mounted
      useEffect(() => {
        if (quillRef.current) {
          const quill = quillRef.current.getEditor();
          const toolbar = quill.getModule('toolbar');
          if (toolbar) {
            toolbar.addHandler('image', handleImageUpload);
          }
        }
      }, []);
 
 
     
useEffect(() => {
    if (!quillRef.current) return;
 
    const quill = quillRef.current.getEditor();
    const editorEl = quill.root; // .ql-editor
 
    const pasteHandler = (e) => {
      const items = e.clipboardData?.items;
      if (items) {
        const files = [];
        for (const item of items) {
          if (item.type.indexOf('image') !== -1) {
            e.preventDefault();
            files.push(item.getAsFile());
          }
        }
        if (files.length) handleFiles(files);
      }
    };
 
    const dropHandler = (e) => {
      e.preventDefault();
      const files = e.dataTransfer?.files;
      if (files?.length) {
        handleFiles(files);
      }
    };
 
    editorEl.addEventListener('paste', pasteHandler);
    editorEl.addEventListener('drop', dropHandler);
    editorEl.addEventListener('dragover', (e) => e.preventDefault());
 
    return () => {
      editorEl.removeEventListener('paste', pasteHandler);
      editorEl.removeEventListener('drop', dropHandler);
    };
  }, [quillRef]);
 
 
  const handleFiles = async (files) => {
    const quill = quillRef.current?.getEditor();
    if (!quill) return;
 
    for (const file of files) {
      if (file.type.startsWith("image/")) {
        const range = quill.getSelection(true);
 
        // Insert a temporary placeholder image
        const tempId = `temp-${Date.now()}`;
        quill.insertEmbed(range.index, "image", `/loading.gif?${tempId}`);
        quill.setSelection(range.index + 1);
 
        const url = await handleUploadImage(file);
        if (url) {
          // Replace the placeholder with the actual URL
          const editorEl = document.querySelector(".ql-editor");
          const imgEl = editorEl.querySelector(`img[src="/loading.gif?${tempId}"]`);
          if (imgEl) {
            imgEl.setAttribute("src", url);
          }
        }
      }
    }
  };
    // const mergedModules = {
    //     ...defaultModules,
    //     ...modules,
    //     toolbar:
    //       modules?.toolbar === false
    //         ? false
    //         : {
    //             ...(modules?.toolbar || defaultModules.toolbar),
    //             container:
    //               modules?.toolbar?.container || defaultModules.toolbar.container,
    //             handlers: {
    //               ...(modules?.toolbar?.handlers || {}),
    //               image: handleImageUpload,
    //             },
    //           },
    //   };
    const defaultFormats = [
        'header', 'font', 'size',
        'bold', 'italic', 'underline', 'strike',
        'color', 'background',
        'script',
        'list', 'bullet',
        'link', 'image', 'formula'
    ];
 
    const mergedFormats = formats ? Array.from(new Set([...defaultFormats, ...formats])) : defaultFormats;
 
    return (
        <div style={styles.container}>
       
            {editorLoaded && (
                <>
                    <CustomToolbar onSymbolInsert={insertSymbol} onImageInsert={handleImageInsert}
            onFormulaInsert={handleFormulaInsert} id={toolbarId.current}  />
                <ReactQuill
                    ref={quillRef}
                    theme={theme}
                    modules={mergedModules}
                    toolbar={mergedModules.toolbar}
                    formats={mergedFormats}
                    onChange={handleInternalChange}
                    onPaste={handleInternalPaste}
                    style={styles.quillEditor}
                    {...restProps}
                />
               </>
            )}
        </div>
    );
});
 
QuillEditor.displayName = 'QuillEditor';
 
const styles = {
    container: {
        width: '100%',
        marginBottom: '20px',
        fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
    },
    toolbar: {
        borderBottom: '1px solid #ccc',
        backgroundColor: '#f8f9fa',
        padding: '8px',
        display: 'flex',
        alignItems: 'center',
        flexWrap: 'wrap',
        gap: '4px',
    },
    symbolSection: {
        display: 'flex',
        alignItems: 'center',
        gap: '2px',
        marginLeft: '8px',
        borderLeft: '1px solid #ccc',
        paddingLeft: '8px',
    },
    symbolDropdown: {
        position: 'relative',
        display: 'inline-block',
    },
    symbolButton: {
        background: 'none',
        // border: '1px solid #ccc',
        borderRadius: '3px',
        padding: '6px 8px',
        fontSize: '14px',
        cursor: 'pointer',
        fontWeight: '600',
        color: '#444',
        minWidth: '32px',
        marginRight: '3px',
        height: '28px',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        transition: 'all 0.2s ease',
          '&:hover': {
      color: '#06c',
      backgroundColor: 'none',
      background: 'none'
    },
    },
    symbolButtonActive: {
        backgroundColor: '#e3f2fd',
        borderColor: '#2196f3',
        color: '#1976d2',
    },
    dropdownMenu: {
        position: 'absolute',
        top: '100%',
        left: '0',
        backgroundColor: '#ffffff',
        border: '1px solid #ccc',
        borderRadius: '4px',
        boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
        zIndex: 1000,
        minWidth: '200px',
        maxWidth: '300px',
        marginTop: '2px',
    },
    dropdownHeader: {
        padding: '8px 12px',
        backgroundColor: '#f8f9fa',
        borderBottom: '1px solid #e9ecef',
        fontSize: '12px',
        fontWeight: '600',
        color: '#495057',
    },
    quillEditor: {
        maxHeight: '300px',
        overflowY: 'auto',
        '& .ql-editor': {
          minHeight: '150px',
        },
        '& .ql-formula': {
          fontFamily: 'serif',
          fontWeight: 'bold',
        }
      },
    optionsGrid: {
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fill, minmax(40px, 1fr))',
        gap: '2px',
        padding: '8px',
        maxHeight: '200px',
        overflowY: 'auto',
    },
    symbolOption: {
        background: 'none',
        border: '1px solid #e9ecef',
        borderRadius: '3px',
        padding: '6px 4px',
        fontSize: '14px',
        cursor: 'pointer',
        color: '#444',
        minHeight: '28px',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        transition: 'all 0.2s ease',
        '&:hover': {
            backgroundColor: '#e3f2fd',
            borderColor: '#2196f3',
        },
    },
};
 
export default QuillEditor;
 
 