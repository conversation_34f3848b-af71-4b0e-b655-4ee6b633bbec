import React, { useState, useEffect } from 'react';
import { makeStyles } from '@mui/styles';
import CloseIcon from '@mui/icons-material/Close';
import { Button, IconButton } from '@mui/material';
import { useSelector, useDispatch } from 'react-redux';
import { setBanner } from '../../store/reducer';

const Banner = () => {
  const classes = useStyles();
  const [visible, setVisible] = useState(true);
  const dispatch = useDispatch();
  const { isBannerVisible } = useSelector((state) => state);

  //   useEffect(() => {
  //     const dismissed = localStorage.getItem('bannerDismissed');
  //     if (dismissed) {
  //       setVisible(false);
  //     }
  //   }, []);

  //   const handleDismiss = () => {
  //     localStorage.setItem('bannerDismissed', true);
  //     setVisible(false);
  //   };

  //   if (!visible) {
  //     return null;
  //   }

  console.log(isBannerVisible);

  const handleCloseBanner = () => {
    dispatch(setBanner(!isBannerVisible));
  };

  return (
    <div className={classes.banner}>
      <p
        style={{
          color: '#000',
        }}
      >
        Get Quickbooks software license directly from us at a discount of 30% and also get a 20% discount on QB learning
        program &nbsp; &nbsp;
        <a
          style={{
            color: 'rgb(39 73 251)',
          }}
          target="new"
          href="https://quickbooks.intuit.com/partners/qbba/?cid=par_qbppnr_nithinkrishna6068&gspk=bml0aGlua3Jpc2huYTYwNjg&gsxid=6idfwVZm0VpG"
        >
          Click here
        </a>
      </p>
      {/* <IconButton onClick={() => handleCloseBanner()}>
        <CloseIcon sx={{ color: '#fff' }} color="#fff" />
      </IconButton> */}
    </div>
  );
};

const useStyles = makeStyles((theme) => ({
  banner: {
    position: 'fixed',
    top: 66,
    left: 0,
    width: '100%',
    zIndex: 1100,
    backgroundColor: '#ffd141',
    color: theme.palette.primary.contrastText,
    padding: '8px 16px',
    display: 'flex',
    justifyContent: 'space-evenly',
    alignItems: 'center',
  },
}));

export default Banner;
