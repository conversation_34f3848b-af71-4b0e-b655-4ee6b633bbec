/* eslint-disable react/button-has-type */
/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable react/prop-types */
import React, { useEffect, useState } from 'react';
import { Button, Typography, Card, Box, Breadcrumbs, Tooltip } from '@mui/material';
import Tab from '@mui/material/Tab';
import { makeStyles } from '@mui/styles';
import Paper from '@mui/material/Paper';
import Stack from '@mui/material/Stack';
import { styled } from '@mui/material/styles';
import { format, parse } from 'date-fns';
import Tabs from '@mui/material/Tabs';
import { useDispatch, useSelector } from 'react-redux';
import { useLocation, useNavigate } from 'react-router-dom';
import './index.css';
import { getIndividualResultForSat } from '../../../Redux/Action'
import SATAssessmentSimulation from '../../generalAssessment/Satassessmentmain';
import adminServices from '../../../services/adminServices';
import { ComingFrom, FromGeneralAssessmentView,ComingFromSub,languagecodevalue } from '../../../store/reducer';
import AssessmentOverviewImage from '../../../assets/Images/Overview.png'
import Maintenance from '../../../assets/Images/maintenance.png'



function CustomTabPanel(props) {
    const { children, value, index, ...other } = props;

    return (
        <div
            role="tabpanel"
            hidden={value !== index}
            id={`simple-tabpanel-${index}`}
            aria-labelledby={`simple-tab-${index}`}
            {...other}
        >
            {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
        </div>
    );
}

function a11yProps(index) {
    return {
        id: `simple-tab-${index}`,
        'aria-controls': `simple-tabpanel-${index}`,
    };
}


const SATOverview = () => {
    const classes = useStyles();
    const dispatch = useDispatch();
    const location = useLocation();


    const navigate = useNavigate();
    const allassessmentdetails = useSelector((state) => state);
    
    const userid = useSelector((state) => state.userInfo && state.userInfo.id);
    const [value, setValue] = useState(0);
    const [assessmentDetails, setAssessmentDetails] = useState([]);
    // eslint-disable-next-line no-unused-vars
    const [view, setView] = useState(allassessmentdetails?.GetSatIndividualDetails)
    const [assessmentView, setAssessmentView] = useState(false);
    const [resumeLength, setResumeLength] = useState(0);

    const [comingFrom, setComingFrom] = useState('');
    const [fromIndex, setFromIndex] = useState(1);
    const [resumeData, setResumeData] = useState([]);
    
    const userRole = useSelector((state) => state.userInfo && state.userInfo.role);

    const [assessmentid,setAssessmentId] = useState(location.state?.id)
    const [assessmentdetails1,setAssessmentDetails1] = useState(location.state?.data)
    

    useEffect(() => {
        dispatch(getIndividualResultForSat(allassessmentdetails && allassessmentdetails?.userInfo?.id,assessmentid))
    }, [assessmentid])


    useEffect(() => {
        setAssessmentDetails(allassessmentdetails?.GetSatIndividualDetails)
    }, [allassessmentdetails])

    const handleChange = (event, newValue) => {
        setValue(newValue);
        if (newValue === 2) {
            // alert("55", assessmentDetails?.length, "1155")
        }
    };


    useEffect(() => {
        const handleBeforeUnload = () => {
            dispatch(FromGeneralAssessmentView(false));
        };

        window.addEventListener("beforeunload", handleBeforeUnload);

        const handlePopState = () => {
            dispatch(FromGeneralAssessmentView(false));
        };

        window.addEventListener("popstate", handlePopState);

        return () => {
            window.removeEventListener("beforeunload", handleBeforeUnload);
            window.removeEventListener("popstate", handlePopState);
        };
    }, []);

    const rawFrom = location?.state?.data?.user_message?.fromDate;
    const rawTo = location?.state?.data?.user_message?.toDate;

    const INPUT_FORMAT = "dd/MM/yyyy";

    const from = rawFrom
        ? format(parse(rawFrom, INPUT_FORMAT, new Date()), "dd-MM-yyyy")
        : "";

    const to = rawTo
        ? format(parse(rawTo, INPUT_FORMAT, new Date()), "dd-MM-yyyy")
        : "";
        
    const handleNavigate = (id) => {
        setFromIndex(id + 1)
        setComingFrom('analysis')
        setAssessmentView(false)       
        if(allassessmentdetails.userInfo.role === 'AUTH_USER'){
           navigate(`/app/SATAnalysis?id=${assessmentid.id}`, {
            state: {
                ...location.state,
                comingFrom: 'ComingfromAnalysis',
                fromindex: id + 1,
                allassessmentdetails
            }
        })  
        }
        else{
    navigate(`/auth/SATAnalysis?id=${assessmentid.id}`, {
            state: {
                ...location.state,
                comingFrom: 'ComingfromAnalysis',
                fromindex: id + 1,
                allassessmentdetails
            }
        })
    }
    }

    const handleClickTrialButton = async () => {

        // try {
        //     const res = await trialAndStripeSubscriptionService.postTrialAssessmentEnrollmentDetails(
        //         JSON.stringify({ planId, enrollmentType, assessmentId, authUserId: userid })
        //     );
        //     if (res.ok) {
        //         console.log('responec succes', res);

        //     }
        // } catch (error) {
        //     console.log(error);
        // }
    };


    const handleResumeTest = async (data, index) => {
        const lastData = data[data.length - 1];
        let totalMarksEarned = 0;
        let totalPossibleMarks = 0;
        let percentage = 0;
        const questionsList = lastData.questions_list;
        lastData.user_result.forEach((result) => {
            const { multiplier, responseRecorded } = result; 
            if (responseRecorded === 'correct') {
                totalMarksEarned += multiplier;
            }
            totalPossibleMarks += multiplier;
        });
    
        percentage = totalPossibleMarks > 0 ? Math.round((totalMarksEarned / totalPossibleMarks) * 100) : 0;

        const QuestionId = lastData?.user_result.map((item) => item.questionId);
        const  section = lastData.user_result.length >= 2 ? 'Maths' : 'English';
        const result = await adminServices.getSatAssessmentResume(
            userid,
            percentage,
            lastData?.related_assessment_id,
            section, 
            QuestionId,
            questionsList 
        );
    
        if (result.data) {
            setResumeData(result.data); 
            setAssessmentView(true); 
            setFromIndex(index + 1); 
            setResumeLength(data[0]?.user_result.length); 
        }
    };
    
   
       
    const handleStartTest = (id) => {
        setFromIndex(id)
        handleClickTrialButton(view?.assessment_details?.subscriptionplanid, true, view?.assessment_details?.id)
        setComingFrom('start')
        setAssessmentView(true)
    }
    const nbsp = '\u00A0'; // 4 spaces
    const dataTitle = assessmentdetails1?.title
    const assessments = [
        { id: 1, title: "Attempt 1", name: `${dataTitle }${nbsp}1` },
        { id: 2, title: "Attempt 2", name: `${dataTitle }${nbsp}2` },
        { id: 3, title: "Attempt 3", name: `${dataTitle }${nbsp}3` },
        { id: 4, title: "Attempt 4", name: `${dataTitle }${nbsp}4` },
        { id: 5, title: "Attempt 5", name: `${dataTitle }${nbsp}5` },
    ];

    const previousLang = localStorage.getItem('previousLanguage')

    const handleNavigateDetailsMyLearning = () => {
        dispatch(ComingFrom("MyLearning"))
        dispatch(languagecodevalue(previousLang))
        
        navigate("/auth/subscribe")
    }
    
    const handleNavigateSat = () => {
        const comingfromSAT = 'SATsrc';
        if (userRole === 'AUTH_USER') {
            navigate("/app/course", { state: comingfromSAT })
        } else {
            navigate("/auth/subscribe", { state: comingfromSAT })
        }

        dispatch(ComingFrom("Courses"))
        dispatch(ComingFromSub("SAT/ACT"))
        // navigate("/auth/subscribe")
        dispatch(languagecodevalue(previousLang))
    }

    const SATfullTest = () => {
        dispatch(ComingFrom("Courses"))
        dispatch(ComingFromSub("SAT/ACT"))
        // navigate("/app/sat")
        const comingfromSAT = 'SATsrc';
        if (userRole === 'AUTH_USER') {
            navigate("/app/course", { state: comingfromSAT })
        } else {
            navigate("/auth/subscribe", { state: comingfromSAT })
        }
        dispatch(languagecodevalue(previousLang))
    }

   

    const onCallBack = (id,assessment) => {
        setAssessmentId(id)
        setAssessmentDetails1(assessment)
        setAssessmentView(false)
    }


    return (
        <>
            {assessmentdetails1?.on_maintenance === true &&
                <Box>
                    <Box sx={{ margin: '100px auto 15px', width: '93%',}}>
                        {userRole !== 'AUTH_USER' && (
                            <Breadcrumbs
                                aria-label="breadcrumb"
                                sx={{
                                    padding: '15px',
                                    paddingLeft: '0px',
                                    paddingBottom: '0px',
                                    paddingTop: '0px',
                                    display: 'flex',
                                    justifyContent: 'space-between',
                                    alignItems: 'center',
                                }}
                                separator=">"
                            >
                                {location.state?.from !== 'MyLearning' ?
                                    <button style={{
                                        cursor: 'pointer',
                                        textDecoration: 'none',
                                        border: 'none',

                                        background: 'none',
                                        color: '#0000ee',
                                        fontSize: '16px',
                                        fontWeight: '500'
                                    }}
                                        color="#212B36"
                                        onClick={handleNavigateSat}
                                    >
                                        {"SAT Assessment"}
                                    </button>

                                    :
                                    <button style={{
                                        cursor: 'pointer',
                                        textDecoration: 'none',
                                        border: 'none',

                                        background: 'none',
                                        color: '#0000ee',
                                        fontSize: '16px',
                                        fontWeight: '500'
                                    }}
                                        color="#212B36"
                                        onClick={handleNavigateDetailsMyLearning}
                                    >
                                        {"My Learning"}
                                    </button>
                                }
                                <Typography color="black">
                                    {"SAT Assessment Overview"}
                                </Typography>

                            </Breadcrumbs>
                        )}

                        {userRole === 'AUTH_USER' && (
                            <Breadcrumbs
                                aria-label="breadcrumb"
                                sx={{
                                    padding: '15px',
                                    paddingLeft: '0px',
                                    paddingBottom: '0px',
                                    paddingTop: '0px',
                                    display: 'flex',
                                    justifyContent: 'space-between',
                                    alignItems: 'center',
                                }}
                                separator=">"
                            >

                                <button style={{
                                    cursor: 'pointer',
                                    textDecoration: 'none',
                                    border: 'none',

                                    background: 'none',
                                    color: '#0000ee',
                                    fontSize: '16px',
                                    fontWeight: '500'
                                }}
                                    color="#212B36"
                                    onClick={SATfullTest}
                                >
                                    {"SAT Assessment"}
                                </button>

                                <Typography color="black">
                                    {"SAT Assessment Overview"}
                                </Typography>

                            </Breadcrumbs>
                        )}
                    </Box>

                    <Box style={{
                        height: '60vh',
                        display: 'flex',
                        flexDirection: 'column',
                        justifyContent: 'center',
                        alignItems: 'center',
                    }}>
                        <>
                            <img style={{ maxWidth: "350px", marginBottom: '30px' }} src={Maintenance} alt="imgCard" />
                            <Typography variant='h6' style={{ color: '#FE780F', fontSize: '30px' }}>Your Learning Might Have to Wait! </Typography>
                            <Typography variant='h6' >
                                This Assessment is under maintenance from {from} - {to}! Will be back shortly!
                            </Typography>
                        </>
                    </Box>

                </Box>

            }
            {assessmentView === false && assessmentdetails1?.on_maintenance === false &&
                <Box sx={{ width: '92%', margin: 'auto', marginTop: '20px' }}>
                    <Box sx={{ marginTop: '100px', marginBottom: "15px" }}>
                        {userRole !== 'AUTH_USER' && (
                            <Breadcrumbs
                                aria-label="breadcrumb"
                                sx={{
                                    padding: '15px',
                                    paddingLeft: '0px',
                                    paddingBottom: '0px',
                                    paddingTop: '0px',
                                    display: 'flex',
                                    justifyContent: 'space-between',
                                    alignItems: 'center',
                                }}
                                separator=">"
                            >
                                {location.state?.from !== 'MyLearning' ?
                                    <button style={{
                                        cursor: 'pointer',
                                        textDecoration: 'none',
                                        border: 'none',

                                        background: 'none',
                                        color: '#0000ee',
                                        fontSize: '16px',
                                        fontWeight: '500'
                                    }}
                                        color="#212B36"
                                        onClick={handleNavigateSat}
                                    >
                                        {"SAT Assessment"}
                                    </button>

                                    :
                                    <button style={{
                                        cursor: 'pointer',
                                        textDecoration: 'none',
                                        border: 'none',

                                        background: 'none',
                                        color: '#0000ee',
                                        fontSize: '16px',
                                        fontWeight: '500'
                                    }}
                                        color="#212B36"
                                        onClick={handleNavigateDetailsMyLearning}
                                    >
                                        {"My Learning"}
                                    </button>
                                }
                                <Typography color="black">
                                    {"SAT Assessment Overview"}
                                </Typography>

                            </Breadcrumbs>
                        )}

                        {userRole === 'AUTH_USER' && (
                            <Breadcrumbs
                                aria-label="breadcrumb"
                                sx={{
                                    padding: '15px',
                                    paddingLeft: '0px',
                                    paddingBottom: '0px',
                                    paddingTop: '0px',
                                    display: 'flex',
                                    justifyContent: 'space-between',
                                    alignItems: 'center',
                                }}
                                separator=">"
                            >

                                <button style={{
                                    cursor: 'pointer',
                                    textDecoration: 'none',
                                    border: 'none',

                                    background: 'none',
                                    color: '#0000ee',
                                    fontSize: '16px',
                                    fontWeight: '500'
                                }}
                                    color="#212B36"
                                    onClick={SATfullTest}
                                >
                                    {"SAT Assessment"}
                                </button>

                                <Typography color="black">
                                    {"SAT Assessment Overview"}
                                </Typography>

                            </Breadcrumbs>
                        )}
                    </Box>

                    <Box className={classes.greetingCard}>
                        <Box style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', padding: '16px', }}>
                            <Box >
                                <Typography variant="h5" style={{ marginBottom: '10px', fontWeight: "900", color: '#000' }} ><strong>{"Welcome,"} {allassessmentdetails && allassessmentdetails?.userInfo?.firstName}</strong></Typography>
                                <Typography variant="h6" fontWeight="500" style={{ fontSize: '17px', fontWeight: "500", color: '#000' }}>{"Ready to start your day with SAT Assessment?"}</Typography>
                            </Box>
                            <Box style={{ width: "150px", position: 'relative', height: "75px" }}>
                                <img style={{
                                    scale: '2.1',
                                    position: 'absolute',
                                    width: "148px",
                                    bottom: '8px',
                                    transform: 'scaleX(1)',
                                }} src={AssessmentOverviewImage} alt="imgCard" />
                            </Box>
                        </Box>
                    </Box>
                    <Typography variant="h5" style={{ marginBottom: '14px', fontWeight: "900", marginTop: '18px', paddingLeft: '18px' }} ><strong>{assessmentdetails1?.title}
                    </strong></Typography>
                    <Stack direction="row" spacing={2} style={{ paddingLeft: '18px' }}>
                        <Item style={{ lineHeight: 1.1, padding: '4px 8px' }}>No of Attempts Left: {assessmentDetails?.attempts?.length && assessmentDetails?.attempts !== null ? 5 - assessmentDetails?.attempts?.length : 5}</Item>
                        <Item style={{ lineHeight: 1.1, padding: '4px 8px' }}>SAT</Item>

                        {/* <Item style={{ lineHeight: 1.1, padding: '4px 8px' }}>{location.state?.data?.title}</Item> */}
                        {/* <Item style={{ lineHeight: 1.1, padding: '4px 8px' }}>{location.state?.data?.complexity_level?.charAt(0).toUpperCase() + location.state?.data?.complexity_level?.slice(1)}</Item> */}
                    </Stack>
                    <Box sx={{ width: '100%', typography: 'body1', marginTop: '24px' }}>
                        <Card sx={{
                            mx: "auto", boxShadow: 3, borderRadius: '10px',
                            border: '1px solid #dfdede'
                        }}>
                            <Box className="121" sx={{ width: '100%' }}>
                                <Box className="122" sx={{ borderBottom: 1, borderColor: 'divider', height: "40px" }}>
                                    <Tabs value={value} onChange={handleChange} className="tabBasic" aria-label="basic tabs example"
                                        sx={{
                                            "& .MuiTabs-indicator": {
                                                backgroundColor: "#ff0004",
                                            },
                                            "& .MuiTab-root": {
                                                color: "#424242 !important",
                                            },
                                            "& .Mui-selected": {
                                                color: "#424242 !important",
                                            },
                                        }}
                                    >
                                        <Tab label={"All"} {...a11yProps(0)} />
                                        <Tab label={"Not Started"} {...a11yProps(1)} />
                                        {/* <Tab label="In-Progress" {...a11yProps(2)} /> */}
                                        <Tab label={"Completed"} {...a11yProps(3)} />
                                    </Tabs>
                                </Box>
                                <CustomTabPanel value={value} index={0} style={{ padding: '10px 10px 20px' }}>
                                    <Box sx={{ display: "flex", flexDirection: "column", gap: 2, padding: '10px 10px 20px' }}>
                                        {assessments && assessments?.length > 0 && assessments?.map((assessment, index) => {
                                            let borderColor = '';
                                            let Color = '';

                                            if (assessmentDetails && assessmentDetails?.attempts?.length > 0 && assessmentDetails?.attempts[index]?.records &&
                                                assessmentDetails?.attempts[index]?.records?.filter(item => item.response_recorded === "correct" && item.response_recorded !== "incorrect")?.length === assessmentDetails[index]?.records?.length) {
                                                borderColor = '#40C057'
                                                Color = '#EAFBEA'
                                            }
                                            else if (assessmentDetails && assessmentDetails?.attempts?.length > 0 && assessmentDetails?.attempts[index]?.records &&
                                                assessmentDetails?.attempts[index]?.records?.filter(item => item.response_recorded === "incorrect" || "correct")?.length === assessmentDetails?.attempts[index]?.records?.length) {
                                                borderColor = '#FA5252'
                                                Color = '#FCEBEA'
                                            }
                                            else {
                                                borderColor = '#dddada';
                                                Color = '#f5f5f5';

                                            }
                                            const firstEmptyAssessmentIndex = assessments.findIndex((_, i) => {
                                                const results = assessmentDetails?.attempts?.[i]?.records || [];
                                                return results.length === 0;
                                            });

                                            return (
                                                <Box
                                                    key={assessment.id}
                                                    sx={{

                                                        borderRadius: "4px",
                                                        width: '99%',
                                                        padding: '1px 0px 0px !important',
                                                        margin: '5px auto 0px !important',
                                                        minHeight: '63px',
                                                        // backgroundColor: assessment.color,
                                                        backgroundColor: Color,
                                                        borderBottom: `3px solid ${borderColor}`,
                                                        flexWrap: "wrap"
                                                    }}>
                                                    <Box sx={{
                                                        // borderBottom: `3px solid ${borderColor}`,

                                                        display: "flex",
                                                        justifyContent: "space-between", padding: '8px 15px !important', alignItems: "center",
                                                    }}>
                                                        <Box sx={{ flex: 1 }}>
                                                            <Box sx={{ fontWeight: "bold", fontSize: "16px" }}>
                                                                {assessment?.title}: <span style={{ fontWeight: "normal" }}>{assessment?.name}</span>
                                                            </Box>
                                                            {/* {assessmentDetails && assessmentDetails[index]?.user_result ?
                                                            <Box sx={{ marginTop: "5px", fontSize: "14px" }}>{assessmentDetails && assessmentDetails[index]?.user_result && assessmentDetails[index]?.user_result?.filter(item => item.response_recorded === "correct")?.length}/ {assessmentDetails && assessmentDetails[index]?.user_result?.length}</Box>
                                                            : <Box sx={{ marginTop: "5px", fontSize: "14px" }}>{assessmentDetails?.[0]?.user_result?.length} Questions</Box>} */}
                                                        </Box>

                                                        {
                                                            assessmentDetails?.attempts &&
                                                                assessmentDetails?.attempts[index]?.records &&
                                                                assessmentDetails?.attempts[index]?.records?.length > 0 ? (
                                                                <>
                                                                    {assessmentDetails?.attempts[index]?.records[0]?.user_result?.length >= 4 ?
                                                                        <Button
                                                                            variant="outlined"
                                                                            onClick={() => handleNavigate(index)}
                                                                            sx={{
                                                                                borderColor: "#3B82F6",
                                                                                borderRadius: '4px !important',
                                                                                color: "#3B82F6",
                                                                                padding: '4px 18px !important',
                                                                                "&:hover": {
                                                                                    backgroundColor: "#3B82F6",
                                                                                    color: "#fff",
                                                                                    borderColor: '#3B82F6',
                                                                                },
                                                                            }}
                                                                        >
                                                                            {"Analysis"}
                                                                        </Button>
                                                                        :
                                                                        <Button
                                                                            variant="outlined"
                                                                            onClick={() => handleResumeTest(assessmentDetails?.attempts[index]?.records, index)}
                                                                            sx={{
                                                                                borderColor: "#3B82F6",
                                                                                borderRadius: '4px !important',
                                                                                color: "#3B82F6",
                                                                                padding: '4px 18px !important',
                                                                                "&:hover": {
                                                                                    backgroundColor: "#3B82F6",
                                                                                    color: "#fff",
                                                                                    borderColor: '#3B82F6',
                                                                                },
                                                                            }}
                                                                        >
                                                                            {"Resume Test"}
                                                                        </Button>}
                                                                </>
                                                            )
                                                                : (
                                                                    <>
                                                                        <Tooltip title={firstEmptyAssessmentIndex !== index ||
                                                                            (index > 0 &&
                                                                                assessmentDetails?.attempts[index - 1]?.records[0]?.user_result?.length < 4) ?
                                                                            "To unlock this test, please attend the previous test!" :
                                                                            "Click to start the test"
                                                                            //    "To unlock this test, please attend the previous test!"
                                                                        }>
                                                                            <span>
                                                                                <Button
                                                                                    variant="outlined"
                                                                                    onClick={() => handleStartTest(assessment?.id)}
                                                                                    // disabled={firstEmptyAssessmentIndex !== index}
                                                                                    disabled={
                                                                                        firstEmptyAssessmentIndex !== index ||
                                                                                        (index > 0 &&
                                                                                            assessmentDetails?.attempts[index - 1]?.records[0]?.user_result?.length < 4)
                                                                                    }
                                                                                    sx={{
                                                                                        borderColor: "#3B82F6",
                                                                                        borderRadius: '4px !important',
                                                                                        color: "#3B82F6",
                                                                                        padding: '4px 18px !important',
                                                                                        "&:hover": {
                                                                                            backgroundColor: "#3B82F6",
                                                                                            color: "#fff",
                                                                                            borderColor: '#3B82F6',
                                                                                        },
                                                                                    }}
                                                                                >
                                                                                    {"Start"}
                                                                                </Button>
                                                                            </span>
                                                                        </Tooltip>
                                                                    </>
                                                                )
                                                        }



                                                    </Box>
                                                </Box>
                                            )

                                        })}

                                    </Box>
                                </CustomTabPanel>

                                <CustomTabPanel value={value} index={1} style={{ padding: '10px 10px 20px' }}>
                                    {assessments && assessments.length > 0 && assessments.map((assessment, index) => {
                                        const userResults = assessmentDetails?.attempts?.[index]?.records || [];

                                        const userResultsView = !!(index > 0 && assessmentDetails?.attempts?.[index - 1]?.records?.length === 4);


                                        const firstEmptyAssessmentIndex = assessments.findIndex((_, i) => {
                                            const results = assessmentDetails?.attempts?.[i]?.records || [];
                                            return results.length === 0;
                                        });

                                        return userResults.length === 0 ? (
                                            <Box
                                                key={assessment.id}
                                                sx={{
                                                    borderRadius: "4px",
                                                    width: '99%',
                                                    padding: '1px 0px 0px !important',
                                                    margin: '5px auto 15px !important',
                                                    backgroundColor: '#f5f5f5',
                                                    flexWrap: "wrap",
                                                }}
                                            >
                                                <Box
                                                    sx={{
                                                        borderBottom: `3px solid #dddada`,
                                                        display: "flex",
                                                        justifyContent: "space-between",
                                                        padding: '8px 15px !important',
                                                        margin: '5px 0 15px !important',
                                                        alignItems: "center",
                                                    }}
                                                >
                                                    <Box sx={{ flex: 1 }}>
                                                        <Box sx={{ fontWeight: "bold", fontSize: "16px" }}>
                                                            {assessment.title}: <span style={{ fontWeight: "normal" }}>{assessment.name}</span>
                                                        </Box>

                                                    </Box>

                                                    <Tooltip title={firstEmptyAssessmentIndex !== index || userResultsView === false ? "To unlock this test, please attend the previous test!" : "Click to start the test"}>
                                                        <span>
                                                            <Button
                                                                variant="outlined"
                                                                onClick={() => handleStartTest(assessment?.id)}
                                                                // disabled={firstEmptyAssessmentIndex !== index || userResultsView === false}
                                                                disabled={firstEmptyAssessmentIndex !== index || (index > 0 && assessmentDetails?.attempts[index - 1]?.records[0]?.user_result?.length < 4)}
                                                                sx={{
                                                                    borderColor: "#3B82F6",
                                                                    borderRadius: '4px',
                                                                    color: "#3B82F6",
                                                                    padding: '4px 18px',
                                                                    "&:hover": {
                                                                        backgroundColor: "#3B82F6",
                                                                        color: "#fff",
                                                                        borderColor: '#3B82F6',
                                                                    },
                                                                }}
                                                            >
                                                                {"Start"}
                                                            </Button>
                                                           </span>
                                                    </Tooltip>
                                                </Box>
                                            </Box>
                                        ) : null;
                                    })}
                                </CustomTabPanel>


                                <CustomTabPanel value={value} index={2} style={{ padding: '10px 10px 20px' }}>
                                    {assessments && assessments?.length > 0 && assessments?.map((assessment, index) => {
                                        const userResults = assessmentDetails && assessmentDetails?.attempts?.length > 0 && assessmentDetails?.attempts?.[index]?.records || [];
                                        // eslint-disable-next-line no-unused-vars
                                        const correctResponses = userResults?.filter(item => item.response_recorded === "incorrect")?.length === 0;


                                        let borderColor = '';
                                        let Color = '';

                                        if (assessmentDetails && assessmentDetails?.attempts?.length > 0 && assessmentDetails?.attempts[index]?.records &&
                                            assessmentDetails[index]?.records?.filter(item => item.response_recorded === "correct" && item.response_recorded !== "incorrect")?.length === assessmentDetails?.attempts[index]?.records?.length) {
                                            borderColor = '#40C057'
                                            Color = '#EAFBEA'
                                        }
                                        else if (assessmentDetails && assessmentDetails?.attempts?.length > 0 && assessmentDetails?.attempts[index]?.records &&
                                            assessmentDetails?.attempts[index]?.records?.filter(item => item.response_recorded === "incorrect" || "correct")?.length === assessmentDetails?.attempts[index]?.records?.length) {
                                            borderColor = '#FA5252'
                                            Color = '#FCEBEA'
                                        }
                                        else {
                                            borderColor = '#dddada';
                                            Color = '#f5f5f5';

                                        }

                                        return (
                                            userResults?.length > 0 && (
                                                <>
                                                    <Box
                                                        key={assessment.id}
                                                        sx={{

                                                            borderRadius: "4px",
                                                            width: '99%',
                                                            minHeight: '63px',
                                                            padding: '1px 0px 0px !important',
                                                            margin: '5px auto 15px !important',
                                                            backgroundColor: Color,
                                                            flexWrap: "wrap"
                                                        }}>
                                                        <Box sx={{
                                                            borderBottom: `3px solid${borderColor}`,
                                                            display: "flex",
                                                            justifyContent: "space-between", padding: '8px 15px !important', alignItems: "center",
                                                        }}>
                                                            <Box sx={{ flex: 1 }}>
                                                                <Box sx={{ fontWeight: "bold", fontSize: "16px" }}>
                                                                    {assessment.title}: <span style={{ fontWeight: "normal" }}>{assessment.name}</span>
                                                                </Box>

                                                            </Box>

                                                            {assessmentDetails?.attempts[index]?.records[0]?.user_result?.length >= 4 ?
                                                                <Button
                                                                    variant="outlined"
                                                                    onClick={() => handleNavigate(index)}
                                                                    sx={{
                                                                        borderColor: "#3B82F6",
                                                                        borderRadius: '4px !important',
                                                                        color: "#3B82F6",
                                                                        padding: '4px 18px !important',
                                                                        "&:hover": {
                                                                            backgroundColor: "#3B82F6",
                                                                            color: "#fff",
                                                                            borderColor: '#3B82F6'
                                                                        },
                                                                    }}>
                                                                    {"Analysis"}
                                                                </Button>
                                                                :
                                                                <Button
                                                                    variant="outlined"
                                                                    onClick={() => handleResumeTest(assessmentDetails?.attempts[index]?.records, assessment?.id - 1)}
                                                                    sx={{
                                                                        borderColor: "#3B82F6",
                                                                        borderRadius: '4px !important',
                                                                        color: "#3B82F6",
                                                                        padding: '4px 18px !important',
                                                                        "&:hover": {
                                                                            backgroundColor: "#3B82F6",
                                                                            color: "#fff",
                                                                            borderColor: '#3B82F6'
                                                                        },
                                                                    }}>
                                                                    {"Resume Test"}
                                                                </Button>}


                                                        </Box>
                                                    </Box>
                                                </>
                                            )


                                        )
                                    })}

                                    {assessmentDetails?.attempts === null &&
                                        <Box style={{ height: '200px' }}><Typography style={{
                                            textAlign: 'center', fontWeight: '500', paddingTop: '20px'
                                        }}>No assessments completed</Typography></Box>
                                    }
                                </CustomTabPanel>
                            </Box>

                        </Card>
                    </Box>
                </Box>}
            {assessmentView === true && assessmentdetails1?.is_published === true &&
                <SATAssessmentSimulation assessmentId={assessmentid} comingFrom={comingFrom} fromIndex={fromIndex} details={assessmentdetails1} onCallBack={onCallBack} resumeData={resumeData} resumeLength={resumeLength} assessmenttitle={assessmentdetails1?.title} />
            } </>

    )
}
export default SATOverview;


// eslint-disable-next-line no-unused-vars
const Item = styled(Paper)(({ theme }) => ({
    backgroundColor: '#002b50',
    borderRadius: '4px',
    padding: '2px 10px',
    textAlign: 'center',
    color: "#fff",
    fontSize: '14px'
}));

const useStyles = makeStyles(() => ({
    greetingCard: {
        width: '100%',
        backgroundColor: '#d2daff',
        borderRadius: '18px'

    }

}));
