<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="278" height="258" viewBox="0 0 278 258">
  <defs>
    <filter id="Rectangle_1682" x="0" y="0" width="278" height="258" filterUnits="userSpaceOnUse">
      <feOffset dy="3" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="3" result="blur"/>
      <feFlood flood-opacity="0.161"/>
      <feComposite operator="in" in2="blur"/>
      <feComposite in="SourceGraphic"/>
    </filter>
    <clipPath id="clip-path">
      <rect id="Rectangle_1683" data-name="Rectangle 1683" width="260" height="240" rx="6" transform="translate(431 149)" fill="none"/>
    </clipPath>
  </defs>
  <g id="Group_1727" data-name="Group 1727" transform="translate(-802 1062)">
    <g id="Group_1722" data-name="Group 1722">
      <g transform="matrix(1, 0, 0, 1, 802, -1062)" filter="url(#Rectangle_1682)">
        <rect id="Rectangle_1682-2" data-name="Rectangle 1682" width="260" height="240" rx="6" transform="translate(9 6)" fill="#00b673"/>
      </g>
      <g id="Mask_Group_6" data-name="Mask Group 6" transform="translate(380 -1205)" clip-path="url(#clip-path)">
        <g id="Group_1718" data-name="Group 1718" transform="translate(80.202 25.675)">
          <ellipse id="Ellipse_122" data-name="Ellipse 122" cx="45.5" cy="45" rx="45.5" ry="45" transform="translate(487.798 103.325)" fill="#2fc690"/>
          <circle id="Ellipse_123" data-name="Ellipse 123" cx="51.5" cy="51.5" r="51.5" transform="translate(529.798 107.325)" fill="#2ca779"/>
        </g>
      </g>
    </g>
  </g>
</svg>
