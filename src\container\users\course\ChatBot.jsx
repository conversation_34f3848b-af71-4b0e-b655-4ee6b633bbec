/* eslint-disable react/prop-types */
import React, { useState, useEffect, useRef } from 'react';
import { Box, TextField, Button, IconButton, Typography, CircularProgress } from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import MinimizeIcon from '@mui/icons-material/Minimize';
import CloseFullscreenIcon from '@mui/icons-material/CloseFullscreen';
import OpenInFullIcon from '@mui/icons-material/OpenInFull';
import SendIcon from '@mui/icons-material/Send';
import axios from 'axios'
import AutoAwesomeIcon from '@mui/icons-material/AutoAwesome';
import Draggable from 'react-draggable';
import LoaderGif from '../../../assets/Images/threedot.gif';
import adminServices from '../../../services/adminServices'


const ChatBotWindow = ({ open, close, userId }) => {
    const [inputValue, setInputValue] = useState('');
    const [chats, setChats] = useState([]);
    const [isMinimized, setIsMinimized] = useState(false);
    const [isLoading, setIsLoading] = useState(false);
    const chatContainerRef = useRef(null);

    const handleSendMessage = async () => {
        const userQuestion = inputValue.trim();
        if (!userQuestion) return;
        const newChats = [...chats, { question: userQuestion, answer: '...' }];
        setChats(newChats);
        setInputValue('');
        setIsLoading(true);
        try {
        //  const payload = {
        //     "input_value": userQuestion,
        //     "user_id": userId
        // };
        // const externalResponse = await axios.post('https://competency-restore.keyskillset.com/api', payload, {
            // headers: {
            //     "accept": "application/json",
            //     "Content-Type": "application/json"
            // }
        // });
        
        //   console.log(externalResponse,"externalResponse");
          
        //     const outputText = externalResponse.body.text; 
        //     const answer = outputText || 'No response received.';
        //     setChats(prevChats => {
        //         const updatedChats = [...prevChats];
        //         updatedChats[updatedChats.length - 1].answer = answer;
        //         return updatedChats;
        //     });


         //    backend api call

         const payload = {
            "input_value": userQuestion,
            "user_id": userId
        };
            const response = await adminServices.getChatResult(payload);
            const answer = response.data || 'No response received.';
            setChats(prevChats => {
                const updatedChats = [...prevChats];
                updatedChats[updatedChats.length - 1].answer = answer;
                return updatedChats;
            });

         

        } catch (error) {
            console.error("Error fetching chat response or saving history:", error);
            setChats(prevChats => {
                const updatedChats = [...prevChats];
                updatedChats[updatedChats.length - 1].answer = "Sorry, I couldn't process your request. Please try again.";
                return updatedChats;
            });

        } finally {
            setIsLoading(false);
        }
    };

    const [bounds, setBounds] = useState({ top: -100, left: -1000, right: 0, bottom: 0 });

    const handleMinimize = () => {
        setIsMinimized(!isMinimized);
    };

    const handleClearChat = () => {
        setChats([]);
    };

    const handleChatClosing = () => {
        setChats([]);
        close(); 
    };

    useEffect(() => {
        if (chatContainerRef.current) {
            chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;
        }
    }, [chats]);

    useEffect(() => {
        const handleKeyDown = (e) => {
            if (e.target.id === 'chat-input' && e.key === ' ') {      
                e.stopPropagation();
            }
        };    
        document.addEventListener('keydown', handleKeyDown);
        
        return () => {
            document.removeEventListener('keydown', handleKeyDown);
        };
    }, []);

    useEffect(() => {
    const updateBounds = () => {
      if (window.innerWidth < 768) {
        setBounds({ top: -50, left: -200, right: 0, bottom: 0 });
      } else if (window.innerWidth < 1200) {
        setBounds({ top: -100, left: -500, right: 0, bottom: 0 });
      } else if (window.innerWidth < 1536) {
        setBounds({ top: -100, left: -1000, right: 0, bottom: 0 });
      } else  if (window.innerWidth < 1940 && window.innerWidth > 1536){
       setBounds({ top: -250, left: -1400, right: -100, bottom: -100 });
      }
    };

    updateBounds(); 
    window.addEventListener('resize', updateBounds);

    return () => window.removeEventListener('resize', updateBounds); 
  }, []);

    if (!open) return null;

    return (
        <Draggable bounds={bounds}
         cancel="#no-drag, .MuiInputBase-root, #chat-input">
            <Box sx={{  position: 'fixed', bottom: 20, right: 20, width: isMinimized ? '300px' : '350px',
                height: isMinimized ? '50px' : '450px', boxShadow: '0 0 20px rgba(0,0,0,0.2)', borderRadius: '8px',
                display: 'flex', cursor: 'move', flexDirection: 'column', overflow: 'hidden',
                backgroundColor: '#f5f5f5', zIndex: 1200 }}>
                <Box sx={{display: 'flex', justifyContent: 'space-between', alignItems: 'center',
                    // padding: '10px',
                    backgroundColor: '#345ca1',color: 'white', borderRadius: '0px',borderBottomRightRadius: '0px',
                    margin: '0px', padding: '6px 10px !important', maxWidth: '100%', }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', padding: '10px', margin: '0', borderRadius: '0' }}>
                        <AutoAwesomeIcon sx={{ mr: 1 }} />
                        <Typography variant="h6">AI Chatbot</Typography>
                    </Box>
                    <Box>
                        <IconButton size="small" onClick={handleMinimize} sx={{ color: 'white', mr: 1 }}>
                           {isMinimized ? <OpenInFullIcon /> : <CloseFullscreenIcon />}
                        </IconButton>
                        <IconButton size="small" onClick={handleChatClosing} sx={{ color: 'white' }}>
                            <CloseIcon />
                        </IconButton>
                    </Box>
                </Box>

                <Box ref={chatContainerRef}  id="noDrag"  sx={{ flex: 1, overflowY: 'auto', padding: '15px', backgroundColor: '#f5f5f5' }}
                >
                    {chats?.length === 0 ? (
                        <Box sx={{display: 'flex', flexDirection: 'column', alignItems: 'center',
                            justifyContent: 'center', height: '100%', color: '#666'}}>
                            <AutoAwesomeIcon sx={{ fontSize: 40, mb: 2, color: '#345ca1' }} />
                            <Typography variant="body1">How can I help you today?</Typography>
                            <Typography variant="body2" sx={{ mt: 1 }}>Ask me anything about this course!</Typography>
                        </Box>
                    ) : (
                        chats.map((chat, index) => (
                            <Box key={index} sx={{ mb: 2 }}>
                                <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 1,    marginTop: '10px', marginBottom: 0 }}>
                                    <Box sx={{
                                        backgroundColor: '#3f51b5',
                                        color: 'white',
                                        padding: '10px',
                                        borderRadius: '10px',
                                        borderBottomRightRadius: '0px',
                                        margin: '5px',
                                        marginRight: '8px',
                                        maxWidth: '90%'
                                    }}>
                                        <Typography style={{padding: '12px 10px'}} variant="body2">{chat.question}</Typography>
                                    </Box>
                                </Box>
                                <Box sx={{ display: 'flex', mb: 1,   marginTop: '5px', marginBottom: 0 }}>
                                    <Box sx={{
                                        backgroundColor: 'white',
                                        borderRadius: '10px',
                                        borderBottomLeftRadius: '0px',
                                        margin: '5px',
                                        marginLeft: '8px',
                                        padding: '10px',
                                        maxWidth: '90%',
                                        boxShadow: '0 1px 2px rgba(0,0,0,0.1)'
                                    }}>
                                        <Typography style={{padding: '12px 10px'}} variant="body2">
                                            {chat.answer === '...' ? (
                                                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                                  <img src={LoaderGif} alt="loading..." style={{width: '40px'}} />
                                                  <Box />
                                               </Box>
                                            ) : (
                                                chat.answer
                                            )}
                                        </Typography>
                                    </Box>
                                </Box>
                            </Box>
                        ))
                    )}
                </Box>

                <Box id="noDrag"
                    sx={{
                        display: 'flex',
                        padding: '10px',
                        alignItems: 'center',
                        backgroundColor: '#f5f5f5',
                        width: '90%',
                        margin: 'auto',
                        borderRadius: '10px',
                        marginBottom: '6px'
                    }}>
                    <TextField
                        id="chat-input"
                        fullWidth
                        variant="outlined"
                        placeholder="Ask anything.."
                        size="small"
                        value={inputValue}
                        onChange={(e) => setInputValue(e.target.value)}
                        onKeyDown={(e) => {
                            if (e.key === 'Enter') {
                                e.preventDefault();
                                handleSendMessage();
                            }
                        }}
                        disabled={isLoading}
                        inputProps={{
                            style: { 
                                whiteSpace: 'pre-wrap',
                                wordBreak: 'break-word',
                                userSelect: 'text'
                            }
                        }}
                        sx={{ 
                            mr: 1,
                            '& .MuiInputBase-input': {
                                cursor: 'text'
                            }
                        }}
                    />
                    <Button id="noDrag" variant="contained" color="primary"  onClick={handleSendMessage}
                    disabled={isLoading || !inputValue.trim()}
                        sx={{
                            minWidth: 'unset', borderRadius: '50%', p: '8px', width: '40px',
                            height: '40px', backgroundColor: '#345ca1','&:disabled': { backgroundColor: '#c5c1c1' }
                        }}
                    >
                        <SendIcon />
                    </Button>
                </Box>

                {chats && chats?.length > 0 && (
                    <Box  id="noDrag"   sx={{ padding: '5px 10px', borderTop: '1px solid #eee', textAlign: 'center'}} >
                        <Button
                            variant="text"
                            size="small"
                            onClick={handleClearChat}
                            sx={{ fontSize: '0.75rem', color: '#666', paddingTop: '2px', paddingBottom: "2px" }}
                        >
                            Clear Chat
                        </Button>
                    </Box>
                )}
            </Box>
        </Draggable>
    );
};

export default ChatBotWindow;
