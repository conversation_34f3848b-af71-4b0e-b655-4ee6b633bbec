import * as Actions from './Constant';

export const initialState = {
    ExcelDetails: [],
    WordDetails: [],
    AccountDetails: [],
    CodeDetails: [],
    RecommendedDetails: [],
    CertificateDetails: []
};

/* eslint-disable default-param-last */
const Reducer = (state = initialState, action) => {
    switch (action.type) {
        // Excel details
        case Actions.GetExcelDetailPending:
            return {
                ...state,
            };

        case Actions.GetExcelDetailSuccess:
            return {
                ...state,
                ExcelDetails: action.payload,
            };

        case Actions.GetExcelDetailFailure:
            return {
                ...state,
            };

        // Word details
        case Actions.GetWordDetailsPending:
            return {
                ...state,
            };
        case Actions.GetWordDetailsSuccess:
            return {
                ...state,
                WordDetails: action.payload,
            };

        case Actions.GetWordDetailsFailure:
            return {
                ...state,
            };

        // Account details
        case Actions.GetAccDetailsPending:
            return {
                ...state,
            };
        case Actions.GetAccDetailsSuccess:
            return {
                ...state,
                AccountDetails: action.payload,
            };

        case Actions.GetAccDetailsFailure:
            return {
                ...state,
            };

        // Code details
        case Actions.GetCodeDetailsPending:
            return {
                ...state,
            };
        case Actions.GetCodeDetailsSuccess:
            return {
                ...state,
                CodeDetails: action.payload,
            };

        case Actions.GetCodeDetailsFailure:
            return {
                ...state,
            };

        // Recommended details
        case Actions.GetRecomendedPending:
            return {
                ...state,
            };
        case Actions.GetRecomendedSuccess:
            return {
                ...state,
                RecommendedDetails: action.payload,
            };

        case Actions.GetRecomendedFailure:
            return {
                ...state,
            };

        // Certificate details
        case Actions.GetCertificatePending:
            return {
                ...state,
            };
        case Actions.GetCertificateSuccess:
            return {
                ...state,
                CertificateDetails: action.payload,
            };

        case Actions.GetCertificateFailure:
            return {
                ...state,
            };

        default:
            return state;
    }
};

export default Reducer;