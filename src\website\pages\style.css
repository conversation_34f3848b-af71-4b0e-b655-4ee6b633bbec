.Googlelogo{
    flex-direction: row-reverse;
    justify-content: center;
    padding: 0 6px !important;
    width: 120px;
    margin-right: 10px;
    margin-top: 10px;
    border-radius: 6px !important;
    box-shadow: rgba(0, 0, 0, 0.16) 0px 3px 6px, rgba(0, 0, 0, 0.23) 0px 3px 6px !important;
}
.textLabel{
    width: 100%; 
    text-align: center; 
    border-bottom: 1px solid #212b21; 
    line-height: 0.1em;
    margin: 10px 0 20px;  
}
.textLabel span{
    background:#fff; 
    padding:0 10px; 
}
.Googlelogo div{
    margin: 0px !important;
    padding: 0px !important;
}

.Googlelogo span{
    padding: 0px !important;
    margin-right: 6px !important;
    font-size: 14px !important;
    font-family: 'Plus Jakarta Sans';
}
.logoSection{
    /* display: flex; */
    display: none !important;
  
    justify-content: center;
    flex-wrap: wrap;
}
.logoSection button{
    width: 120px;
    margin-right: 10px;
    color: #838383 !important;
    padding : 0 6px !important;
    margin-top: 10px;
    border-radius: 5px;
    box-shadow: rgba(0, 0, 0, 0.16) 0px 3px 6px, rgba(0, 0, 0, 0.23) 0px 3px 6px;
}
.logoSection button:hover{
    box-shadow: rgba(0, 0, 0, 0.16) 0px 3px 6px, rgba(0, 0, 0, 0.23) 0px 3px 6px;
}

.languageSwitcher {
    width: 150px;
    position: relative;
  }
  
  .languageSelect {
    width: 100%;
    padding: 11px 16px;
    font-size: 16px;
    border: 1px solid #ddd;
    border-radius: 10px;
    background-color: #fff;
    color: #333;
    appearance: none; 
    -webkit-appearance: none;
    -moz-appearance: none;
    background-image: url("data:image/svg+xml;utf8,<svg fill='gray' height='24' viewBox='0 0 24 24' width='24' xmlns='http://www.w3.org/2000/svg'><path d='M7 10l5 5 5-5z'/></svg>");
    background-repeat: no-repeat;
    background-position: right 12px center;
    background-size: 16px 16px;
    cursor: pointer;
  }
  
  .languageSelect:focus {
    outline: none;
    border-color: none;
   
  }
  .languageSelect:disabled {
    cursor: not-allowed;
    background-color: #f5f5f5;
  }
  .language-switcher.disabled {
    opacity: 0.7;
  }
  [class*="lang-kn"] h4 {
    font-size: 1.1rem !important;
    word-spacing: 4px;
  }
  [class*="lang-kn"] h5 {
    font-size: 1.05rem !important;
    word-spacing: 4px;
  }
  [class*="lang-kn"] h6 {
    font-size: 0.95rem !important;
    word-spacing: 4px;
    font-weight: 550;
  }
  [class*="lang-kn"] span {
    font-size: 0.8rem !important;
    word-spacing: 4px;
  }
  [class*="lang-kn"] p{
    word-spacing: 2px !important;
  }


