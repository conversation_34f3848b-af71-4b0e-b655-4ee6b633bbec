/* eslint-disable react/jsx-key */
import React, { useState } from 'react';
import {Paper, Typography, Button, Table, TableBody, TableCell, TableContainer, TableHead,
  TableRow, Modal, TextField, Select, MenuItem, IconButton, Box
} from '@mui/material';
import { makeStyles } from '@mui/styles';
import { Delete as DeleteIcon, CloudUpload as UploadIcon } from '@mui/icons-material';
import adminServices from '../../../services/adminServices';
import Page from '../../../components/Page';
import PageHeader from '../../../components/PageHeader';

const useStyles = makeStyles((theme) => ({
  paper: {
    padding: '8px 14px',
    position: 'relative',
    background: '#F9F9F9',
    boxShadow: 'none',
    border: '1px solid #e5e1e1',
    minHeight: '116px',
    cursor: 'pointer',
    '&:hover': {
      background: theme.palette.primary.main,
      color: '#fff',
    },
  },
  modalStyle: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    width: 500,
    backgroundColor: 'white',
    padding: 20,
    boxShadow: 24,
    borderRadius: 8,
  },
  fileInput: {
    marginTop: 10,
    marginBottom: 10,
  },
}));

const QuestionBank = () => {
  const classes = useStyles();
  const isGoBack = true;

  const [openModal, setOpenModal] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    version: '',
    file: null,
    description: '',
    status: '',
  });
  const [errors, setErrors] = useState({});
  const [uploadedData, setUploadedData] = useState([]);
  const [pdfPreview, setPdfPreview] = useState(null);

  const validate = () => {
    const err = {};
    if (!formData.name) err.name = 'Name is required';
    if (!formData.version) err.version = 'Version is required';
    if (!formData.file) err.file = 'PDF file is required';
    if (!formData.description) err.description = 'Description is required';
    if (!formData.status) err.status = 'Status is required';
    setErrors(err);
    return Object.keys(err).length === 0;
  };

  const handleOpen = () => setOpenModal(true);
  const handleClose = () => {
    setFormData({ name: '', version: '', file: null, description: '', status: '' });
    setErrors({});
    setPdfPreview(null);
    setOpenModal(false);
  };

  const handleFileChange = (e) => {
    const file = e.target.files[0];
    if (file?.type === 'application/pdf') {
      setFormData({ ...formData, file });
      setPdfPreview(URL.createObjectURL(file));
    } else {
      setErrors({ ...errors, file: 'Only PDF files are allowed' });
    }
  };

  const handleSubmit = async() => {
    if (!validate()) return;

      const data = new FormData();
       
        data.append('excelfile', formData.file);
        const response = await adminServices.ChatBotUploads(data);
        console.log('response is =>',response?.data?.url)

  };

  const handleDelete = (index) => {
    const updated = [...uploadedData];
    updated.splice(index, 1);
    setUploadedData(updated);
  };

  return (
    <Page title="Chatbot Uploads">
      <PageHeader pageTitle="ChatBot Uploads" goBack={isGoBack} />

      <Box sx={{ textAlign: 'right', mb: 2 }}>
        <Button variant="contained" startIcon={<UploadIcon />} onClick={handleOpen}>
          Upload PDF
        </Button>
      </Box>

      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Name</TableCell>
              <TableCell>Version</TableCell>
              <TableCell>Uploaded Date</TableCell>
              <TableCell>Uploaded By</TableCell>
              <TableCell>Description</TableCell>
              <TableCell>Status</TableCell>
              <TableCell>Action</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {uploadedData.map((row, index) => (
              <TableRow key={index}>
                <TableCell>{row.name}</TableCell>
                <TableCell>{row.version}</TableCell>
                <TableCell>{row.uploadedDate}</TableCell>
                <TableCell>{row.uploadedBy}</TableCell>
                <TableCell>{row.description}</TableCell>
                <TableCell>{row.status}</TableCell>
                <TableCell>
                  <IconButton onClick={() => handleDelete(index)} color="error">
                    <DeleteIcon />
                  </IconButton>
                </TableCell>
              </TableRow>
            ))}
            {uploadedData.length === 0 && (
              <TableRow>
                <TableCell colSpan={7} align="center">
                  No uploads yet
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </TableContainer>

      <Modal open={openModal} onClose={handleClose}>
        <Box className={classes.modalStyle}>
          <Typography variant="h6" gutterBottom>Upload PDF</Typography>

          <TextField
            label="Name"
            fullWidth
            margin="normal"
            value={formData.name}
            onChange={(e) => setFormData({ ...formData, name: e.target.value })}
            error={!!errors.name}
            helperText={errors.name}
          />

          <TextField
            label="Version"
            fullWidth
            margin="normal"
            value={formData.version}
            onChange={(e) => setFormData({ ...formData, version: e.target.value })}
            error={!!errors.version}
            helperText={errors.version}
          />

          <Button variant="outlined" component="label" className={classes.fileInput}>
            Upload PDF
            <input type="file" hidden onChange={handleFileChange} accept="application/pdf" />
          </Button>
          {errors.file && <Typography color="error" variant="body2">{errors.file}</Typography>}

          {pdfPreview && (
            <Box mt={2}>
              <Typography variant="body2" gutterBottom>PDF Preview:</Typography>
              <iframe src={pdfPreview} width="100%" height="200px" title="PDF Preview" />
            </Box>
          )}

          <TextField
            label="Description"
            fullWidth
            margin="normal"
            multiline
            rows={3}
            value={formData.description}
            onChange={(e) => setFormData({ ...formData, description: e.target.value })}
            error={!!errors.description}
            helperText={errors.description}
          />

          <Select
            fullWidth
            displayEmpty
            value={formData.status}
            onChange={(e) => setFormData({ ...formData, status: e.target.value })}
            error={!!errors.status}
          >
            <MenuItem value="" disabled>Select Status</MenuItem>
            <MenuItem value="Active">Active</MenuItem>
            <MenuItem value="Inactive">Inactive</MenuItem>
          </Select>
          {errors.status && <Typography color="error" variant="body2">{errors.status}</Typography>}

          <Box mt={3} display="flex" justifyContent="space-between">
            <Button variant="contained" onClick={handleSubmit}>Add</Button>
            <Button onClick={handleClose}>Cancel</Button>
          </Box>
        </Box>
      </Modal>
    </Page>
  );
};

export default QuestionBank;
