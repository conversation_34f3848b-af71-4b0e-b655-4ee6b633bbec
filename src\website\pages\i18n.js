// i18n.js
import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import translationEN from '../../locales/en/translation.json';
import translationKN from '../../locales/ka/translation.json'; 
import dashboardEN from '../../locales/en/dashboard.json';
import dashboardKN from '../../locales/ka/dashboard.json'; 
import userEN from '../../locales/en/user.json';
import userKN from '../../locales/ka/user.json'; 
import courseEN from '../../locales/en/course.json';
import courseKN from '../../locales/ka/course.json'; 
import productsPlansEN from '../../locales/en/productsPlans.json';
import productsPlansKN from '../../locales/ka/productsPlans.json'; 
import reportEN from '../../locales/en/report.json';
import reportKN from '../../locales/ka/report.json'; 
import ticketEN from '../../locales/en/ticket.json';
import ticketKN from '../../locales/ka/ticket.json'; 
import navEN from '../../locales/en/nav.json'
import navKN from '../../locales/ka/nav.json'; 

const resources = {
  en: {
    translation: translationEN,
    dashboard: dashboardEN,
    user: userEN,
    course: courseEN,
    productsPlans: productsPlansEN,
    report: reportEN,
    ticket: ticketEN,
    nav: navEN
  },
  fr: {
    translation: translationEN,
    dashboard: dashboardEN,
    user: userEN,
    course: courseEN,
    productsPlans: productsPlansEN,
    report: reportEN,
    ticket: ticketEN,
    nav: navEN
  },
  es: {
    translation: translationEN,
    dashboard: dashboardEN,
    user: userEN,
    course: courseEN,
    productsPlans: productsPlansEN,
    report: reportEN,
    ticket: ticketEN,
    nav: navEN
  },
  as: {
    translation: translationEN,
    dashboard: dashboardEN,
    user: userEN,
    course: courseEN,
    productsPlans: productsPlansEN,
    report: reportEN,
    ticket: ticketEN,
    nav: navEN
  },
  kn: { 
    translation: translationKN,
    dashboard: dashboardKN,
    user: userKN,
    course: courseKN,
    productsPlans: productsPlansKN,
    report: reportKN,
    ticket: ticketKN,
    nav: navKN
  }

};

i18n
  .use(initReactI18next)
  .init({
    resources,
    lng: 'en', // Default language
    fallbackLng: 'en', // Fallback language
    interpolation: {
      escapeValue: false, // React already sanitizes the output
    },
    debug: false, 
  });


const savedLanguage = localStorage.getItem('selectedLanguage');
if (savedLanguage) {
  i18n.changeLanguage(savedLanguage);
}

export default i18n;
