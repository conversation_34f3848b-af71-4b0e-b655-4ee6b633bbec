import { Card, CardActions, CardContent, Typography, Button, stepButtonClasses } from '@mui/material';
import React from 'react';
import { useLocation } from 'react-router-dom';
import { useTranslation } from 'react-i18next';

import { Steps } from '../website/pages/Steps';

export const CustomeTooltip = ({
  continuous,
  index,
  step,
  tooltipProps,
  primaryProps,
  backProps,
  closeProps,
  ...items
}) => {
  const location = useLocation();
  const lastIndex = Steps(location?.pathname)?.length - 1;
  const { t } = useTranslation('translation');
  console.log(Steps(location?.pathname).length, 'pathnaaa');
  return (
    <Card {...tooltipProps}>
      <CardContent sx={{ padding: '16px 16px 0px' }}>
        <Typography
          gutterBottom
          variant="h5"
          sx={{ fontSize: '1rem', fontWeight: '600' }}
          //    variant="h5" component="h5"
        >
          {step.title}
        </Typography>
        <Typography variant="body2">{step.content}</Typography>
      </CardContent>
      <CardActions>
        {index > 0 && (
          <Button color="secondary" variant="text" sx={{ color: '#FE7000' }} {...backProps}>
            {t("Back")}
          </Button>
        )}
        {index === lastIndex ? (
          <Button
            {...backProps}
            color="primary"
            variant="text"
            sx={{
              backgroundColor: '#FE7000',
              color: 'white',
              '&:hover': {
                backgroundColor: '#FE7000',
                color: 'white',
              },
            }}
            {...primaryProps}
          >
            {t("End")}
          </Button>
        ) : (
          <Button
            color="primary"
            variant="text"
            sx={{
              backgroundColor: '#FE7000',
              color: 'white',
              '&:hover': {
                backgroundColor: '#FE7000',
                color: 'white',
              },
            }}
            {...primaryProps}
          >
            {t("Next")}
          </Button>
        )}
      </CardActions>
    </Card>
  );
};
