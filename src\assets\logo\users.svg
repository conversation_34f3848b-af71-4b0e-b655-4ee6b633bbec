<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="278" height="258" viewBox="0 0 278 258">
  <defs>
    <filter id="Rectangle_1679" x="0" y="0" width="278" height="258" filterUnits="userSpaceOnUse">
      <feOffset dy="3" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="3" result="blur"/>
      <feFlood flood-opacity="0.161"/>
      <feComposite operator="in" in2="blur"/>
      <feComposite in="SourceGraphic"/>
    </filter>
    <clipPath id="clip-path">
      <rect id="Rectangle_1680" data-name="Rectangle 1680" width="260" height="240" rx="6" transform="translate(431 149)" fill="#6333b0"/>
    </clipPath>
  </defs>
  <g id="Component_75_2" data-name="Component 75 – 2" transform="translate(9 6)">
    <g transform="matrix(1, 0, 0, 1, -9, -6)" filter="url(#Rectangle_1679)">
      <rect id="Rectangle_1679-2" data-name="Rectangle 1679" width="260" height="240" rx="6" transform="translate(9 6)" fill="#008ae5"/>
    </g>
    <g id="Mask_Group_5" data-name="Mask Group 5" transform="translate(-431 -149)" clip-path="url(#clip-path)">
      <g id="Group_1717" data-name="Group 1717" transform="translate(80.202 25.675)">
        <ellipse id="Ellipse_120" data-name="Ellipse 120" cx="45.5" cy="45" rx="45.5" ry="45" transform="translate(487.798 103.325)" fill="#0078d3"/>
        <circle id="Ellipse_121" data-name="Ellipse 121" cx="51.5" cy="51.5" r="51.5" transform="translate(529.798 107.325)" fill="#0067c0"/>
      </g>
    </g>
  </g>
</svg>
