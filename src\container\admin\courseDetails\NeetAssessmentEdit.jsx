/* eslint-disable radix */
/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable react/no-danger */
/* eslint-disable jsx-a11y/role-has-required-aria-props */
/* eslint-disable consistent-return */
/* eslint-disable no-unused-vars */
/* eslint-disable react/jsx-key */
/* eslint-disable arrow-body-style */
/* eslint-disable func-names */
/* eslint-disable no-alert */

import React, { useState, useEffect, useRef } from 'react';
import MenuItem from '@mui/material/MenuItem';
import {
    TextField, Button, Box, Badge, Avatar, Typography, IconButton, FormHelperText, InputLabel, Select, FormControl, Grid, Dialog,ListItemText,
    DialogActions, DialogContent, Tooltip,
    Checkbox, CardContent, CircularProgress,
    DialogTitle, Alert, Radio, RadioGroup, FormControlLabel, FormGroup, CardActionArea, Card, Stack
} from "@mui/material";
import { v4 as uuidv4 } from 'uuid';
import AddCircleIcon from '@mui/icons-material/AddCircle';
import EditIcon from '@mui/icons-material/Edit';
import DeleteOutlinedIcon from '@mui/icons-material/DeleteOutlined';
import VisibilityIcon from '@mui/icons-material/Visibility';
import ReactQuill from 'react-quill';
import 'react-quill/dist/quill.snow.css';
import { LoadingButton } from '@mui/lab';
import CloseIcon from '@mui/icons-material/Close';
import ClearIcon from '@mui/icons-material/Clear';
import { useSelector } from 'react-redux';
import { useNavigate,useLocation } from 'react-router-dom'
import { DropzoneArea } from 'material-ui-dropzone';
import DOMPurify from 'dompurify';
import { makeStyles } from '@mui/styles';
import katex from "katex";
import KeyboardDoubleArrowLeftIcon from '@mui/icons-material/KeyboardDoubleArrowLeft';
import KeyboardDoubleArrowRightIcon from '@mui/icons-material/KeyboardDoubleArrowRight';
import SnackBar from '../../../components/snackbar/snackbar';
import adminServices from '../../../services/adminServices';
import Page from '../../../components/Page'
import PageHeader from '../../../components/PageHeader';
import DialogModal from '../../../components/modal/DialogModal';
import './styles.css'
import "katex/dist/katex.min.css";

window.katex = katex;

const modules = {
    toolbar: [
        ["bold", "italic", "underline"],
        [{ list: "ordered" }, { list: "bullet" }],
        [{ script: "sub" }, { script: "super" }],
        [{ header: [1, 2, 3, false] }],
        ["image", { formula: { customClass: 'qlformula' } }],
        [{ color: [] }, { background: [] }],
        [{ align: [] }],
        ["clean"],
    ],
    clipboard: {
        matchVisual: false,
    },
};

const NEET = () => {
    const userInfo = useSelector((state) => state.userInfo && state.userInfo);
    const location = useLocation();
    const AssessmentData = location.state;

    

    const navigate = useNavigate();
    const classes = useStyles();
    const [loading, setLoading] = useState(false);
    const [openSnackbar, setOpenSnackbar] = useState(false);
    const [snackbarTitle, setSnackbarTitle] = useState('');
    const [name, setName] = useState('');
    const [marks, setMarks] = useState('');
    const [nameError, setNameError] = useState('');
    const [marksError, setMarksError] = useState('');
    const [ImageError, setImageError] = useState('');
    const [subjectError, setSubjectError] = useState('');
    const [chapterError, setChapterError] = useState('');
    const [descriptionError, setDescriptionError] = useState('');
    const [search, setSearch] = useState('');
    // eslint-disable-next-line no-unused-vars
    const [error, setError] = useState(false);
    const [requirederrors, setrequiredErrors] = useState({
        moduleName: "",
        points: "",
        questionid: "",
    });
    const [nodata, setNodata] = useState(true);
    const [thumbImage, setThumbImage] = useState(null);
    const [thumbPreview, setThumbPreview] = useState(null);
    const [editorValue, setEditorValue] = useState('');
    const [modulesArray, setModulesArray] = useState([]);
    const [errorMessage, setErrorMessage] = useState("");
    const [errorMessageAll, setErrorMessageAll] = useState("");
    const [loadingnew, setLoadingnew] = useState(false);
    const [submitted, setSubmitted] = useState(false);
    const [subjectList, setSubjectList] = useState([]);
    const [chapterList, setChapterList] = useState([]);

    console.log(chapterList,"chapterList");
    
    const [questionCount, setQuestionCount] = useState('');
    const [questionCountError, setQuestionCountError] = useState('');
    const [details, setDetails] = useState({ level: "", questionType: "Neet", subject: [], chapter: [] });

    console.log(details,"detailsdetails");
    
    useEffect(() => {
        getSubjectList()
        // if (details.subject){
        //     getChapterList()
        // }
    }, [])

    useEffect(() => {
        if (details?.subject?.length !== 0)
            getChapterList()
    }, [details.subject])


    const getSubjectList = async () => {
        const result = await adminServices.getNEETSubjectList();
        if (result.ok) {
            setSubjectList(result.data)
        }

    }

    const getChapterList = async () => {
        const result = await adminServices.getNEETChaptersList(details.subject);
        if (result.ok) {
            setChapterList(result.data)
        }

    }
    useEffect(() => {
        if (AssessmentData)
            setDetails({ level: AssessmentData.complexity_level, questionType: "Neet", subject: AssessmentData.module_details[0].subject, chapter: AssessmentData.module_details[1].chapters })
        setName(AssessmentData.title);
        setEditorValue(AssessmentData.short_description);
        setThumbImage(AssessmentData.image_name);
        setThumbPreview(AssessmentData.image_name);
        setQuestionCount(AssessmentData.module_details[2]?.count);
        setMarks(AssessmentData.module_details[3]?.maxMarks)
    }, [AssessmentData])
    const handleChangeDescription = (value) => {
        const cleanedValue = value
            .replace(/<p><br><\/p>/g, '')
            .replace(/<p><\/p>/g, '')
            .trim();
        setDescriptionError("");
        setEditorValue(cleanedValue);
    };

    useEffect(() => {
        setLoading(false);
    }, [search]);



    const handleFileChange = (file) => {
        setImageError("")
        if (file[0]?.size < 2097152) {
            imageWidthAndHeight(file[0]).then((res) => {
                if (res.width >= 360 && res.height >= 200) {
                    const url = URL.createObjectURL(file[0]);
                    setThumbPreview(url);
                    setThumbImage(file[0]);
                } else {
                    alert("Image dimensions must be at least 360x200px.");
                }
            });
        }
    };

    const imageWidthAndHeight = (file) => {
        return new Promise((resolve) => {
            const img = new Image();
            const reader = new FileReader();

            reader.onload = function () {
                img.onload = function () {
                    resolve({ width: img.width, height: img.height });
                };
                img.src = reader.result;
            };
            reader.readAsDataURL(file);
        });
    };

    const Validation = () => {    
        const isEditorContentEmpty = (html) => {
            const text = html?.replace(/<(.|\n)*?>/g, '').trim();
            return !text;
        };
        if (!name) {
            setNameError("Name field is required");
            return false;
        }
        if (!marks) {
            setMarksError("Marks field is required");
            return false;
        }
        if (!questionCount) {
            setQuestionCountError("Question field is required");
            return false;
        }
        if (isEditorContentEmpty(editorValue)) {
            setDescriptionError("Description field is required");
            return false;
        }
        if (parseInt(marks) !== parseInt(questionCount) * 4) {
            setMarksError("Marks must be 4 times the number of questions");
            return false;
        }


        if (!thumbImage) {
            setImageError("Thumb Image field is required");
            return false;
        }

        if (!details.subject || details.subject.length === 0) {
            setSubjectError("Subject field is required");
            return false;
        }
        if (!details.chapter || details.chapter.length === 0) {
            setChapterError("Chapter field is required");
            return false;
        }

        if (editorValue?.length > 255) {
            setDescriptionError("Description cannot be more than 255 characters");
            return false;
        }
        return true;
    };

    
    const handleSubmit = async () => {
        const valid = Validation()

        if (valid) {
            setLoading(true);
            const totalSeconds = (time.hours * 3600) + (time.minutes * 60) + time.seconds;
            const modulesData = [
                { subject: details.subject },
                { chapters: details.chapter },
                {count:questionCount},
                {maxMarks:marks}
            ]; const formData = new FormData();
            formData.append('name', name);
            formData.append('description', editorValue);
            formData.append('time_in_mins', 7920);
            formData.append('thumbImage', thumbImage);
            formData.append('is_published', false);
            formData.append('userId', userInfo.id);
            formData.append('is_free', false);
            formData.append('modulesData', JSON.stringify(modulesData));
            try {
                const response = await adminServices.updateNEETAssessmentData(AssessmentData.id, formData);
                if (response.ok) {
                    setSnackbarTitle('NEET Assessment created successfully');
                    setOpenSnackbar(true);
                    navigate('/app/NEET',{state:{comingfrom:1}});
                    setName('')
                    setLoading(false);
                }

            } catch (error) {
                console.log(error);
            }

        }
    };


    const [time, setTime] = useState({
        hours: 0,
        minutes: 0,
        seconds: 0,
    });

    const handleChange = (event) => {
        const { name, value } = event.target;
        setTime((prevTime) => ({
            ...prevTime,
            [name]: value,
        }));
    };

    const hoursArray = Array.from({ length: 24 }, (_, i) => i);
    const minutesArray = Array.from({ length: 60 }, (_, i) => i);
    const secondsArray = Array.from({ length: 60 }, (_, i) => i);


    const stripHtml = (html) => {
        const div = document.createElement("div");
        div.innerHTML = html;
        return div.textContent || div.innerText || "";
    };



    return (
        <>
            <Page title="Edit NEET Assessment">
                <PageHeader pageTitle="Edit NEET Assessment"  />
                <Grid container spacing={2} className='GACognitivesection' sx={{ mb: 2, padding: '15px 20px' }}>
                    <Grid item xs={12} sm={6} sx={{ marginBottom: '18px', paddingRight: '18px' }}>
                        <TextField
                            variant="outlined"
                            inputProps={{ maxLength: 50 }}
                            fullWidth
                            id="addname"
                            label="Name*"
                            type="search"
                            value={name}
                            onChange={(e) => { setName(e.target.value); setNameError(''); }}
                            sx={{
                                // bgcolor: "#f0f0f0",
                                borderRadius: 1,
                                height: 36,
                                '& .MuiInputBase-input': {
                                    fontSize: 14,
                                    padding: "8px 12px",
                                },
                            }}
                            error={!!nameError}
                        />
                        {nameError && <FormHelperText error>{nameError}</FormHelperText>}
                    </Grid>
  <Grid item xs={12} sm={6} sx={{ marginBottom: '18px', paddingRight: '18px' }}>
                        <TextField
                            variant="outlined"
                            inputProps={{ min: 0 }}
                            fullWidth
                            id="questionsCount"
                            label="No. of Questions*"
                            type="number"
                            value={questionCount}
                            onChange={(e) => {
                                setQuestionCount(e.target.value);
                                setQuestionCountError('');
                            }}
                            sx={{
                                borderRadius: 1,
                                height: 36,
                                '& .MuiInputBase-input': {
                                    fontSize: 14,
                                    padding: "8px 12px",
                                },
                            }}
                            error={!!questionCountError}
                        />
                        {questionCountError && <FormHelperText error>{questionCountError}</FormHelperText>}
                    </Grid>
                    <Grid className="unique" item xs={12} sm={6} sx={{ marginBottom: '0px', paddingRight: '18px' }}>
                        {thumbPreview === null ? (
                            <FormControl style={{ height: '100%' }}
                                required
                                component="fieldset"
                                color="primary"
                                variant="outlined"
                                fullWidth
                                name="thumbImage"
                            >
                                <Typography variant="subtitle1">Thumb Image* <span style={{
                                    fontSize: '12px',
                                    float: 'inline-end', paddingBottom: '0', marginBottom: '0', position: 'relative', top: '5px'
                                }}>required resolution (360X200)</span></Typography>
                                <DropzoneArea className="dropTextArea"
                                    acceptedFiles={['image/jpeg', 'image/png', 'image/bmp']}
                                    showPreviews={false}
                                    dropzoneText="Drag and Drop Image or Browse File"
                                    showPreviewsInDropzone={false}
                                    maxFileSize={300000000}
                                    filesLimit={1}
                                    showAlerts={false}
                                    styles={{
                                        height: '100%', minHeight: '100%',
                                        display: 'flex',
                                        flexDirection: 'column',
                                        justifyContent: 'center'
                                    }}
                                    onChange={handleFileChange}
                                    useChipsForPreview
                                    previewGridProps={{ container: { spacing: 1, direction: 'row' } }}
                                    showFileNamesInPreview
                                />
                                {ImageError && <FormHelperText error>{ImageError}</FormHelperText>}
                            </FormControl>


                        ) : (
                            <div className={classes.imgPreviewRoot}>
                                <Typography variant="subtitle1">Thumb Image</Typography>
                                <Badge
                                    badgeContent={
                                        <CloseIcon id='ThumbPreview'
                                            className={classes.badgeAlign}
                                            onClick={() => {
                                                setThumbPreview(null);
                                                setThumbImage(null);
                                            }}
                                        />
                                    }
                                >
                                    <Avatar
                                        variant="rounded"
                                        src={thumbPreview}
                                        style={{ minHeight: '150px !important' }}
                                        className={thumbPreview !== null && classes.fileImgSIze}
                                    />
                                </Badge>
                            </div>
                        )}
                    </Grid>


                    <Grid item xs={12} sm={6} sx={{ marginBottom: '15px', paddingRight: '18px' }}>
                        <Typography variant="subtitle1">Neet Description *</Typography>
                         {descriptionError && (
                            <FormHelperText error sx={{ marginTop: '4px', marginLeft: '8px' }}>
                                {descriptionError}
                            </FormHelperText>
                        )}
                        <ReactQuill

                            value={editorValue}
                            onChange={(content) => {
                                if (content.length > 255) {
                                    handleChangeDescription(content.slice(0, 255));
                                } else {
                                    handleChangeDescription(content);
                                }
                            }}
                            theme="snow"
                            id='questionTxt'
                            name="question"
                            onPaste={(e) => {
                                e.preventDefault();
                                const clipboardText = e.clipboardData.getData('text').slice(0, 255);
                                handleChangeDescription(clipboardText);
                            }}
                            // fullWidth
                            style={{ height: '150px', marginBottom: '30px' }}
                        />

                    </Grid>
                        
                     <Grid item xs={6} fullWidth>
                                        <FormControl className={classes.formControl} style={{ width: "100%" }}>
                                            <InputLabel id="subject-label">Select Subject *</InputLabel>
                                            <Select
                                                name="subject"
                                                labelId="subject-label"
                                                id="subject"
                                                multiple
                                                value={details.subject || []}
                                                onChange={(e) =>{
                                                    setDetails((prevDetails) => ({
                                                        ...prevDetails,
                                                        subject: e.target.value, 
                                                    }))
                                                    setSubjectError('');
                                                }
                                                }
                                                renderValue={(selected) =>
                                                    subjectList
                                                        .filter((subj) => selected.includes(subj.id))
                                                        .map((subj) => subj.title)
                                                        .join(', ')
                                                }
                                            >
                                                {subjectList.map((subject) => (
                                                    <MenuItem key={subject.id} value={subject.id}>
                                                        <Checkbox checked={details.subject?.includes(subject.id)} />
                                                        <ListItemText primary={subject.title} />
                                                    </MenuItem>
                                                ))}
                                            </Select>
                                        </FormControl>
                                        {subjectError && <FormHelperText error>{subjectError}</FormHelperText>}
                                    </Grid>

                    


                
                                    <Grid item xs={6} fullWidth>
                                        <FormControl className={classes.formControl} style={{ width: "100%" }}>
                                            <InputLabel id="demo-simple-select-standard-label">Select Chapter *</InputLabel>
                
                                            <Select
                                                multiple
                                                name="chapter"
                                                labelId="demo-simple-select-standard-label"
                                                id="chapter"
                                                value={details.chapter || []}
                                                onChange={(e) =>{
                                                    setDetails(prevDetails => ({
                                                        ...prevDetails,
                                                        chapter: e.target.value
                                                    }))
                                                    setChapterError('');
                                                }
                                                }
                                                renderValue={(selected) =>
                                                    chapterList
                                                        .filter(chapter => selected.includes(chapter.id))
                                                        .map(chapter => chapter.module_name)
                                                        .join(', ')
                                                }
                                                displayEmpty
                                            >
                                                <MenuItem value="" disabled>Select a chapter</MenuItem>
                                                {chapterList.map((chapter) => (
                                                    <MenuItem key={chapter.id} value={chapter.id}>
                                                        <Checkbox checked={details.chapter?.includes(chapter.id)} />
                                                        <ListItemText primary={chapter.module_name} />
                                                    </MenuItem>
                                                ))}
                                            </Select>
                                        </FormControl>
                                        {chapterError && <FormHelperText error>{chapterError}</FormHelperText>}

                                    </Grid>
                    <Grid item xs={6} fullWidth>
                        <FormControl className={classes.formControl} style={{ width: "100%" }}>
                            <InputLabel id="demo-simple-select-standard-label">Level*</InputLabel>
                            <Select
                                // disabled={submitted}
                                name="level"
                                labelId="demo-simple-select-standard-label"
                                id="level"
                                label="Level"
                                value={details.level}
                                onChange={(e) => setDetails(prevDetails => ({
                                    ...prevDetails,
                                    level: e.target.value
                                }))}
                                displayEmpty
                            >
                                <MenuItem value="easy">Easy</MenuItem>
                                <MenuItem value="medium">Medium</MenuItem>
                                <MenuItem value="complex">Complex</MenuItem>
                            </Select>
                        </FormControl>
                    </Grid>
                    <Grid item xs={6} fullWidth>
                       
                            <TextField
                            variant="outlined"
                            inputProps={{ maxLength: 50 }}
                            fullWidth
                            id="addmarks"
                            label="Marks*"
                            type="number"
                            value={marks}
                            onChange={(e) => { setMarks(e.target.value); setMarksError(''); }}
                            sx={{
                                // bgcolor: "#f0f0f0",
                                borderRadius: 1,
                                height: 36,
                                '& .MuiInputBase-input': {
                                    fontSize: 14,
                                    padding: "8px 12px",

                                },
                            }}
                            error={!!marksError}
                        />
                        {marksError && <FormHelperText error>{marksError}</FormHelperText>}
                    </Grid>

                    <Grid item xs={12} sx={{ display: 'flex', justifyContent: 'end', paddingTop: '15px' }}>
                     
                        <Stack direction="column" spacing={2} sx={{ marginRight: '15px', mt: 2 }}>
                            
                            <LoadingButton
                                type="submit"
                                id="addassessmentgeneral"
                                onClick={handleSubmit}
                                variant="contained"
                                color="primary"
                                loading={loading}
                            >
                                Update
                            </LoadingButton>
                        </Stack>

                    </Grid>
                    {errorMessage && (
                        <Alert severity="error" sx={{ marginBottom: '10px' }}>
                            {errorMessage}
                        </Alert>
                    )}
                    {errorMessageAll && (
                        <Alert severity="error" sx={{ marginBottom: '10px' }}>
                            {errorMessageAll}
                        </Alert>)}
                </Grid>



                <SnackBar open={openSnackbar} snackbarTitle={snackbarTitle} close={() => setOpenSnackbar(false)} />


            </Page>
        </>
    );

}

const useStyles = makeStyles(() => ({
    imgPreviewRoot: {
        borderRadius: '10px',
        padding: '0.8rem;',
    },
    fileImgSIze: {
        width: '100%',
        height: '120px',
        objectFit: 'cover',
        objectPosition: 'center',
        border: '1px solid #fff',
        borderRadius: '5px',
        boxShadow: '0 3px 10px rgb(0 0 0 / 20%)',
    },
    badgeAlign: {
        boxShadow: '0 2px 8px -5px #ff0000',
        color: '#FF0000',
        fontSize: '1.2rem',
        backgroundColor: '#fff',
        padding: '2px',
        borderRadius: '10px',
        cursor: 'pointer',
    },
    deleteLabel: {
        width: 'max-content',
        cursor: 'pointer',
    }
}));
export default NEET;