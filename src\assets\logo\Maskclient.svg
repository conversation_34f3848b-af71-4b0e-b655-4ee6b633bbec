<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="260" height="240" viewBox="0 0 260 240">
  <defs>
    <clipPath id="clip-path">
      <rect id="Rectangle_1691" data-name="Rectangle 1691" width="260" height="240" rx="6" transform="translate(445 -755)" fill="none"/>
    </clipPath>
    <filter id="Rectangle_1631" x="-9" y="-6" width="278" height="258" filterUnits="userSpaceOnUse">
      <feOffset dy="3" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="3" result="blur"/>
      <feFlood flood-opacity="0.161"/>
      <feComposite operator="in" in2="blur"/>
      <feComposite in="SourceGraphic"/>
    </filter>
    <filter id="Rectangle_1690" x="-9" y="-6" width="278" height="258" filterUnits="userSpaceOnUse">
      <feOffset dy="3" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="3" result="blur-2"/>
      <feFlood flood-opacity="0.161"/>
      <feComposite operator="in" in2="blur-2"/>
      <feComposite in="SourceGraphic"/>
    </filter>
  </defs>
  <g id="Mask_Group_8" data-name="Mask Group 8" transform="translate(-445 755)" clip-path="url(#clip-path)">
    <g transform="matrix(1, 0, 0, 1, 445, -755)" filter="url(#Rectangle_1631)">
      <rect id="Rectangle_1631-2" data-name="Rectangle 1631" width="260" height="240" rx="6" fill="#6333b0"/>
    </g>
    <g transform="matrix(1, 0, 0, 1, 445, -755)" filter="url(#Rectangle_1690)">
      <rect id="Rectangle_1690-2" data-name="Rectangle 1690" width="260" height="240" rx="6" fill="#6333b0"/>
    </g>
    <path id="Subtraction_1" data-name="Subtraction 1" d="M139.5,310a45.613,45.613,0,0,1-32.174-13.18,44.825,44.825,0,0,1-9.751-14.3A44.646,44.646,0,0,1,101.661,240h75.677a44.628,44.628,0,0,1-5.665,56.819A45.613,45.613,0,0,1,139.5,310Z" transform="translate(488 -995)" fill="#552da8"/>
    <path id="Subtraction_3" data-name="Subtraction 3" d="M51.5,104A51.5,51.5,0,0,1,14.19,17H81V94.724A51.338,51.338,0,0,1,51.5,104Z" transform="translate(624 -772)" fill="#48269f"/>
  </g>
</svg>
