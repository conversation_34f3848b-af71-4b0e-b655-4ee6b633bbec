/* eslint-disable react/no-danger */
/* eslint-disable no-unused-vars */
/* eslint-disable react/jsx-key */
/* eslint-disable consistent-return */
/* eslint-disable arrow-body-style */
/* eslint-disable react/prop-types */

import React, { useState, useEffect, useMemo, useRef } from 'react';
import MenuItem from '@mui/material/MenuItem';
import { Button,  Typography, IconButton, FormHelperText, InputLabel, Select, FormControl, Grid, Dialog,DialogActions, DialogContent, Tooltip,Checkbox,DialogTitle, Radio, RadioGroup, FormControlLabel} from "@mui/material";
import { v4 as uuidv4 } from 'uuid';
import ReactQuill from 'react-quill';
import 'react-quill/dist/quill.snow.css';
import ClearIcon from '@mui/icons-material/Clear';
import { useSelector } from 'react-redux';
import {useLocation } from 'react-router-dom'
import KeyboardDoubleArrowLeftIcon from '@mui/icons-material/KeyboardDoubleArrowLeft';
import KeyboardDoubleArrowRightIcon from '@mui/icons-material/KeyboardDoubleArrowRight';
import DOMPurify from 'dompurify';
import { makeStyles } from '@mui/styles';
import katex from "katex";
import SnackBar from '../../../components/snackbar/snackbar';
import adminServices from '../../../services/adminServices';
import './styles.css'
import "katex/dist/katex.min.css";


window.katex = katex;

const modules = {
    toolbar: [
        ["bold", "italic", "underline"],
        [{ list: "ordered" }, { list: "bullet" }],
        [{ script: "sub" }, { script: "super" }],
        [{ header: [1, 2, 3, false] }],
        ["image", { formula: { customClass: 'qlformula' } }],
        [{ color: [] }, { background: [] }],
        [{ align: [] }],
        ["clean"],
    ],
    clipboard: {
        matchVisual: false,
    },
};

const formats = [
    "header",
    "font",
    "size",
    "bold",
    "italic",
    "underline",
    "strike",
    "blockquote",
    "list",
    "bullet",
    "indent",
    "link",
    "image",
    "video",
    "formula",
    "color",
    "background",
    "align",
    "code-block",
    "script",
    "clean",
];


const AddQuestionModel = ({ open, handleClose, handleSubmit, handleSelectQuestions, selectedQuestion }) => {
    const userInfo = useSelector((state) => state.userInfo && state.userInfo);
    const location = useLocation();
    const classes = useStyles();
    const [loading, setLoading] = useState(false);
    const [questionList, setQuestionList] = useState([]);
    const [openSnackbar, setOpenSnackbar] = useState(false);
    const [snackbarTitle, setSnackbarTitle] = useState('');
    const [questionid, setQuestionId] = useState('');
    const [questionDetails, setQuestionDetails] = useState({
        justification: '',
        level: '',
        options: [],
        question_text: '',
        question_type: '',
    });
    const [editDetails, setEditDetails] = useState('');
    const [complexity, setComplexity] = useState('');
    const [nameError, setNameError] = useState('');
    const [ImageError, setImageError] = useState('');
    const [descriptionError, setDescriptionError] = useState('');
    const [selectedQuestions, setSelectedQuestions] = useState([]);
    console.log(selectedQuestions, 'selectedQuestions');

    const [ComplexityError, setComplexityError] = useState('');
    // eslint-disable-next-line no-unused-vars
    const [error, setError] = useState(false);
    const [requiredErrors, setRequiredErrors] = useState({
        moduleName: "",
        questionid: "",
    });

    const [selectIndex, setSelectIndex] = useState('');
    const [submitQuestionClicked, setSubmitQuestionClicked] = useState(false);
    const [satvalues, setSatValues] = useState({
        question: '',
        questionType: 'screenLevelMcq',
        mcqOptions: [{ option: '', isCorrect: false }],
    });

    const [visible, setVisible] = useState(true);
    const [selectedOption, setSelectedOption] = useState('create');
    const [loadingnew, setLoadingnew] = useState(false);
    const [submitted, setSubmitted] = useState(false);
    const [query, setQuery] = useState('');

    const [selectedOptionnew, setSelectedOptionnew] = useState(null);




    const [details, setDetails] = useState({ level: "", questionType: "screenLevelMcq" });
    const [explanation, satExplanation] = useState('');



    useMemo(() => {
        if (editDetails) {
            const optionsData = JSON.parse(editDetails?.options);
            const formattedOptions = optionsData.mcqOptions.map((optionText, index) => ({
                option: optionText,
                isCorrect: optionsData.correctAnswer[index] || false,
            }));

            const cognitiveSkillMap = {
                1: "Knowledge",
                2: "Comprehension",
                3: "Application",
                4: "Analysis",
                5: "Synthesis",
                6: "Evaluation"
            };
            setQuestionDetails({
                justification: editDetails.justification,
                level: editDetails.level,
                options: formattedOptions,
                question_text: editDetails.question_text,
                question_type: editDetails.question_type,
                cognitive_skill_id: cognitiveSkillMap[editDetails.cognitive_skill_id]
            });
        }

    }, [editDetails])

    useEffect(() => {
        if (selectedQuestion) {
            setSelectedQuestions(selectedQuestion);
        }
    }, [selectedQuestion]);

    const [loadingQuestion, setLoadingQuestion] = useState(false);

    const [search, setSearch] = useState('');
    const [page, setPage] = useState(0);

    console.log(page, "page");

    const [questionError, setQuestionError] = useState({
        level: "",
        question: "",
        option: "",
        Objective: '',
        explanation: ""
    });






    const handleLeftArrowClick = () => {
        setLoadingQuestion(true)
        setPage(prev => prev - 1);
        // getQuestionList(details.questionType, search,page-1)
    }

    const handleRightArrowClick = () => {
        setLoadingQuestion(true)
        setPage(prev => prev + 1);
        // getQuestionList(details.questionType, search,page+1)
    }



    useEffect(() => {
        setLoadingQuestion(true)
        setLoading(false);
        getQuestionList(details.questionType, search, page);
    }, [details.questionType, search, page]);

    const sanitizeConfig = {
        ALLOWED_TAGS: ['b', 'i', 'em', 'a', 'ul', 'ol', 'li'],
    };

    const getQuestionList = async (data, search, page) => {
        const result = await adminServices.getSatQuestion(data, search, page)

        setQuestionList(result.data)
        setLoadingQuestion(false)

    };



    const mcqOptionsRef = useRef(satvalues.mcqOptions);

    useEffect(() => {
        mcqOptionsRef.current = satvalues.mcqOptions;
    }, [satvalues.mcqOptions]);




    const handleMCQOptionChangeEnglish = (index, field, value) => {

        const newMCQOptions = [...mcqOptionsRef.current];
        newMCQOptions[index][field] = value;
        setSatValues((prevState) => ({
            ...prevState,
            mcqOptions: newMCQOptions,
        }));

    };



    const handleRemoveOptionEnglish = (index) => {
        const newMCQOptions = [...satvalues.mcqOptions];
        newMCQOptions.splice(index, 1);
        setSatValues((prevState) => ({
            ...prevState,
            mcqOptions: newMCQOptions,
        }));
    };



    const handleChangeQuestionEnglish = (field, value) => {
        setSatValues((prevState) => ({
            ...prevState,
            [field]: value,
        }));
    };

    const handleAddOptionEnglish = () => {
        const newOption = {
            id: uuidv4(),
            option: '',
            isCorrect: false,
        };
        setSatValues((prevState) => ({
            ...prevState,
            mcqOptions: [...prevState.mcqOptions, newOption],
        }));
    };


    const QuestionValidationSubmit = () => {

        const stripHtml = (html) => {
            const div = document.createElement("div");
            div.innerHTML = html;
            return div.textContent || div.innerText || "";
        };

        if (!details.level &&
            !stripHtml(explanation).trim() &&
            !stripHtml(satvalues.question).trim() &&
            !satvalues.mcqOptions.every(option => stripHtml(option.option).trim())
        ) {

            setQuestionError(prev => ({
                ...prev,
                level: "This field is required",
                question: "Please submit question and answer!",
                explanation: 'This field is required',
                options: "Please fill all the options!",
            }));
            return false
        }
        if (!details.level) {
            setQuestionError(prev => ({
                ...prev,
                level: 'This field is required'
            }));
            return false
        }
        if (!stripHtml(explanation).trim()) {
            setQuestionError(prev => ({
                ...prev,
                explanation: 'This field is required'
            }));
            return false
        }

        if (!stripHtml(satvalues.question).trim()) {
            setQuestionError(prev => ({
                ...prev,
                question: "Please submit question and answer!"
            }));
            return false
        }
        if (!satvalues.mcqOptions.every(option => stripHtml(option.option).trim())) {
            setQuestionError(prev => ({
                ...prev,
                options: "Please fill all the options!",
            }));
            return false
        }
        if (satvalues.mcqOptions?.length < 4) {
            setQuestionError(prev => ({
                ...prev,
                option: "Please add at least 4 options"
            }));
            return false
        }


        if (!satvalues.mcqOptions.some(option => option.isCorrect)) {
            setQuestionError(prev => ({
                ...prev,
                question: "Please select at least one correct answer!"
            }));
            return false
        }
        return true
    };

    const handleSubmitQuestion = async () => {
        if (submitted) return;
        const valid = QuestionValidationSubmit()

        if (valid) {
            setSubmitQuestionClicked(true);

            try {
                setLoadingnew(true);

                const keysData = new FormData();
                keysData.append('question', satvalues.question);
                keysData.append('level', complexity);
                keysData.append('questionType', 'screenLevelMcq');
                keysData.append('explanation', explanation);
                const mcqdata = {
                    question: satvalues.question,
                    mcqOptions: satvalues.mcqOptions.map(option => option.option),
                    correctAnswer: satvalues.mcqOptions.map(option => option.isCorrect),
                };
                keysData.append('mcqData', JSON.stringify(mcqdata));
                const response = await adminServices.createQuestions(keysData);

                if (response.ok) {
                    // setQuestionId(response.data.id.id);
                    setPage(0)
                    setSelectedQuestions(prev => [...prev, response.data.id]);
                    setSubmitted(true);

                    // setDetails({ level: "", questionType: "screenLevelMcq" });
                    // // setSubmitQuestionClicked(false);
                    // setQuestionDetails('');
                    // setRequiredErrors({
                    //     questionid: "",
                    // })
                    //     setQuestionError({
                    //         level: "",
                    //         question: "",
                    //         option: "",
                    //         Objective: '',
                    //         explanation: ""
                    //     });
                    //     setSatValues({});

                    //     setTimeout(() => {
                    //             satExplanation('');
                    //             setSatValues({
                    //             question: '',
                    //             questionType: 'screenLevelMcq',
                    //             mcqOptions: [{ option: '', isCorrect: false }],
                    //         });
                    //     }, 0);

                } else {
                    console.error("Error:", response);
                }
            } catch (error) {
                console.error("An error occurred:", error);
            } finally {
                setLoadingnew(false);
            }
        } else {
            setSubmitQuestionClicked(false);
        }
    };

    const handleCloseNew = () => {
        handleClose();
        setPage(0)
        setSubmitted(false);
        setSelectedOption('create')
        setSelectedQuestions([])
        satExplanation('');
        setSatValues({});
        setTimeout(() => {
            setSatValues({
                question: '',
                questionType: 'screenLevelMcq',
                mcqOptions: [{ option: '', isCorrect: false }],
            });

        }, 0);
        setRequiredErrors({})
        setSubmitted(false);
        setVisible(true);
        setQuestionError({
            passage: "",
            question: "",
            option: "",
            Objective: '',
            explanation: ''


        });
        setRequiredErrors({
            questionid: "",
        })
    };

    const CreateQuestion = (e) => {
        setSearch(e.target.value)
        // setQuestionId(e.target.value.id)
        setQuestionDetails(e.target.value)
        setVisible(true);

    }

    const handleModuleSubmitCreateQuestion = () => {
        if (selectedQuestions?.length > 0 && submitQuestionClicked) {
            handleSelectQuestions(selectedQuestions);
            setDetails({ level: "", questionType: "screenLevelMcq" });
            setSubmitted(false);
            setSubmitQuestionClicked(false);
            satExplanation('')
            setQuestionDetails('')
            setVisible(true);
            setSelectedOption('create')
            setSatValues({})
            setPage(0)
            setTimeout(() => {
                setSatValues({
                    question: '',
                    questionType: 'English',
                    mcqOptions: [{ option: '', isCorrect: false }],
                });
            }, 0);
        }
        else {
            setRequiredErrors({
                questionid: "Please Submit question and Answer!",
            })

        }
    };

    let buttonText = "Submit Question";
    if (loadingnew) {
        buttonText = "Submitting...";
    } else if (submitted) {
        buttonText = "Submitted";
    }

    const handleChangeOption = (e) => {
        setSelectedOption(e.target.value)
        if (e.target.value === 'select') {
            setSubmitQuestionClicked(true);
        }
        setRequiredErrors({
            moduleName: "",
            questionid: "",
        });
    }


    const handleAddQuestion = (question) => {
        setSelectedQuestions((prev) => {
            if (prev.find(q => q.id === question.id)) return prev;
            setQuestionDetails(question.question_text)
            // setQuestionId((prev) => [...prev, question.id]);
            return [...prev, question];
        });
    };


    const handleDeselect = (id) => {
        setSelectedQuestions((prev) => prev.filter((q) => q.id !== id));
        // setQuestionId((prev) => prev.filter((q) => q !== id));
    };


    return (
        <>
            <Dialog open={open} onClose={() => {
                setSubmitted(false);
                handleClose();
            }} fullWidth>
                <DialogTitle style={{ paddingBottom: '0px' }}>Add Screen-level Question</DialogTitle>
                <DialogContent className='GACognitivesection' sx={{ paddingTop: '25px !important' }}>

                    <FormControl component="fieldset">
                        <RadioGroup
                            row
                            value={selectedOption}
                            onChange={(e) => handleChangeOption(e)}
                        >

                            <FormControlLabel value="create" control={<Radio />} sx={{ marginRight: '40px' }} label="Create Question" />
                            <FormControlLabel value="select" control={<Radio />} label="Select Question" />
                        </RadioGroup>
                    </FormControl>

                    {selectedOption === 'select' ? <Grid container spacing={2}>





                        <Grid item xs={12}>
                            <div className="search-select-container">
                                <div className="search-select-container" style={{ display: 'flex', }}>
                                    {page > 0 && <IconButton aria-label="Previous" onClick={handleLeftArrowClick}>
                                        <KeyboardDoubleArrowLeftIcon />
                                    </IconButton>}
                                    <input
                                        type="text"
                                        readOnly={submitted}
                                        onChange={CreateQuestion}
                                        placeholder="Search or Select"
                                        aria-label="Search or select an option"
                                        style={{ flex: 1, padding: '8px', borderRadius: '4px', border: '1px solid #ccc' }}
                                    />
                                    <IconButton disabled={questionList?.length === 0} aria-label="Next" onClick={handleRightArrowClick}>
                                        <KeyboardDoubleArrowRightIcon />
                                    </IconButton>
                                </div>
                                {loadingQuestion &&
                                    <Typography style={{ justifyContent: 'center', alignItems: 'center', fontSize: '20' }}>
                                        Loading.....
                                    </Typography>
                                }

                                {visible && !loadingQuestion && (
                                    <ul className="dropdown" role="listbox" aria-expanded={query.length > 0}>
                                        {questionList && questionList.length > 0 ? (
                                            questionList.map((item) => {
                                                const sanitizedQuestion = item.question_text
                                                    .replace(/<p>/g, '<span style="display: flex;">')
                                                    .replace(/<\/p>/g, '</span>')
                                                    .replace(/&nbsp;/g, ' ');
                                                const sanitizedPassage = DOMPurify.sanitize(item.passage, sanitizeConfig).replace(/<[^>]*>/g, '').trim().replace(/&nbsp;/g, ' ');

                                                const isSelected = selectedQuestions && selectedQuestions?.some(q => q.id === item.id);

                                                return (
                                                    <li
                                                        key={item.id}
                                                        role="option"
                                                        className="dropdown-item"
                                                        tabIndex={0}
                                                        aria-selected={selectedOptionnew?.id === item.id}
                                                        title={sanitizedPassage}
                                                        style={{
                                                            backgroundColor: isSelected ? '#e0ffe0' : 'transparent',
                                                            padding: '8px',
                                                            borderRadius: '4px',
                                                            marginBottom: '4px',
                                                        }}
                                                    >
                                                        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                                                            <div id='searchQuestionsTag' dangerouslySetInnerHTML={{ __html: sanitizedQuestion }} />


                                                            {!isSelected && (
                                                                <Button
                                                                    id="questionadd"
                                                                    variant="outlined"
                                                                    color="primary"
                                                                    onClick={(e) => {
                                                                        e.stopPropagation();
                                                                        handleAddQuestion(item);
                                                                    }}
                                                                    sx={{
                                                                        fontSize: '0.75rem',
                                                                        minWidth: '24px',
                                                                        minHeight: '24px',
                                                                        padding: '2px',
                                                                        borderRadius: '12px',
                                                                    }}
                                                                >
                                                                    +
                                                                </Button>
                                                            )}
                                                        </div>
                                                    </li>
                                                );
                                            })
                                        ) : (
                                            <li className="dropdown-item" >
                                                No results found
                                            </li>
                                        )}

                                    </ul>
                                )}
                                {
                                    !loadingQuestion && selectedQuestions && selectedQuestions.length > 0 && (
                                        <>
                                            <Typography>Selected Questions:</Typography>
                                            {selectedQuestions.map((item, index) => {
                                                const sanitizedQuestion = item.question_text
                                                    .replace(/<p>/g, '<span style="display: flex;">')
                                                    .replace(/<\/p>/g, '</span>')
                                                    .replace(/&nbsp;/g, ' ');


                                                const sanitizedPassage = DOMPurify.sanitize(item.passage, sanitizeConfig).replace(/<[^>]*>/g, '').trim().replace(/&nbsp;/g, ' ');
                                                return (
                                                    <Tooltip
                                                        key={index}
                                                        title={sanitizedPassage}
                                                        placement="top"
                                                    >
                                                        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                                                            <Typography
                                                                id="QuestionArea"
                                                                dangerouslySetInnerHTML={{ __html: sanitizedQuestion }}
                                                            />
                                                            <Button onClick={() => handleDeselect(item.id)}>
                                                                x
                                                            </Button>
                                                        </div>
                                                    </Tooltip>
                                                );
                                            }
                                            )}

                                        </>
                                    )
                                }
                            </div>
                        </Grid>
                    </Grid>

                        :
                        <Grid container spacing={2}>
                            <Grid item xs={6} fullWidth>
                                <FormControl className={classes.formControl} style={{ width: "100%" }}
                                // error={touched.questionType && Boolean(errors.questionType)}
                                >
                                    <InputLabel id="demo-simple-select-standard-label">Level*</InputLabel>
                                    <Select
                                        disabled={submitted}
                                        name="level"
                                        labelId="demo-simple-select-standard-label"
                                        id="level"
                                        label="Level"
                                        value={details.level}
                                        onChange={(e) => {
                                            setDetails(prevDetails => ({
                                                ...prevDetails,
                                                level: e.target.value
                                            })); setQuestionError(prev => ({
                                                ...prev,
                                                level: ''
                                            }));
                                        }}
                                        displayEmpty
                                    >
                                        <MenuItem value="easy">Easy</MenuItem>
                                        <MenuItem value="medium">Medium</MenuItem>
                                        <MenuItem value="complex">Complex</MenuItem>
                                    </Select>
                                    {questionError?.level && (
                                        <FormHelperText error>{questionError?.level}</FormHelperText>
                                    )}
                                </FormControl>
                            </Grid>

                            <>



                                {details?.questionType === 'screenLevelMcq' &&
                                    <>                                        <div >
                                        <Grid item xs={12}>
                                            <FormControl className={classes.formControl}>
                                                <Typography className={classes.background} gutterBottom variant="subtitle1">
                                                    Create Question*
                                                </Typography>

                                                <ReactQuill
                                                    readOnly={submitted}
                                                    theme="snow"
                                                    id={`questionText`}
                                                    name="question"
                                                    value={satvalues.question}
                                                    onChange={(value) => {
                                                        handleChangeQuestionEnglish('question', value); setQuestionError({
                                                            Objective: ''
                                                        });
                                                    }}

                                                    modules={modules}
                                                    formats={formats}
                                                    fullWidth
                                                />

                                            </FormControl>
                                        </Grid>


                                        {satvalues?.mcqOptions?.map((opt, index) => (
                                            <div key={opt.id} style={{ position: 'relative', marginTop: '10px' }}>
                                                <Grid container spacing={2} alignItems="center">
                                                    <Grid item xs={12} style={{ display: 'flex', alignItems: 'end', marginLeft: 40 }}>
                                                        <ReactQuill
                                                            readOnly={submitted}
                                                            theme="snow"
                                                            id={`optiontext`}
                                                            name={`mcqQuestion`}
                                                            value={opt.option}
                                                            onChange={(value) => { handleMCQOptionChangeEnglish(index, 'option', value); setQuestionError({ option: '', }) }}
                                                            modules={modules}
                                                            formats={formats}
                                                            placeholder="Option"
                                                            style={{ marginTop: 10, flex: 1 }}
                                                        />
                                                        <IconButton
                                                            disabled={submitted}
                                                            aria-label="delete"
                                                            color="error"
                                                            onClick={() => handleRemoveOptionEnglish(index)}
                                                            style={{ marginLeft: '-8px', marginTop: '-8px' }}
                                                        >
                                                            <ClearIcon fontSize="small" />
                                                        </IconButton>
                                                        <FormControlLabel
                                                            control={
                                                                <Checkbox
                                                                    id='checkBoxMCQ'
                                                                    name={`mcqOptionsisCorrect`}
                                                                    checked={opt.isCorrect}
                                                                    onChange={() => handleMCQOptionChangeEnglish(index, 'isCorrect', !opt.isCorrect)}
                                                                    disabled={!opt.option.trim() || submitted}
                                                                />
                                                            }
                                                            label="Correct"
                                                        />
                                                    </Grid>
                                                </Grid>
                                            </div>
                                            // )
                                        ))}
                                        {questionError && questionError.question && <FormHelperText error>{questionError?.question}</FormHelperText>}
                                        {questionError && questionError.option && <FormHelperText error >{questionError?.option}</FormHelperText>}
                                        {questionError && questionError.correctAnswer && <FormHelperText error >{questionError?.correctAnswer}</FormHelperText>}

                                        <Button
                                            disabled={submitted}
                                            variant="contained"
                                            color="primary"
                                            id='AddOptionschk'
                                            onClick={handleAddOptionEnglish}
                                            style={{ width: '120px', backgroundColor: 'rgb(63, 186, 150)', marginTop: '10px', borderRadius: '6px' }}
                                        >
                                            Add Option
                                        </Button>
                                        {requiredErrors.questionid && (
                                            <FormHelperText error>{requiredErrors.questionid}</FormHelperText>
                                        )}
                                    </div>
                                        <Grid item xs={12} sm={12} sx={{ marginBottom: '15px', paddingRight: '18px' }}>
                                            <Typography variant="subtitle1">Explanation *</Typography>
                                            <ReactQuill
                                                readOnly={submitted}
                                                theme="snow"
                                                id="explanation"
                                                name="explanation"
                                                modules={modules}
                                                formats={formats}
                                                value={explanation}
                                                onChange={(content) => {
                                                    satExplanation(content);
                                                    setQuestionError({
                                                        explanation: ''
                                                    });
                                                }}
                                            />
                                            {questionError && questionError.explanation && <FormHelperText error  >{questionError?.explanation}</FormHelperText >}
                                        </Grid>
                                    </>
                                }
                            </>


                            <Grid item xs={12}>
                                <Button id={`btnsumbition${buttonText}`} onClick={handleSubmitQuestion} type="submit"
                                    disabled={loadingnew || submitted}
                                    variant="contained" color="primary" fullWidth>
                                    {buttonText}
                                </Button>
                            </Grid>
                            {requiredErrors.questionid && (
                                <FormHelperText error>{requiredErrors.questionid}</FormHelperText>
                            )}
                        </Grid>}
                </DialogContent>


                <DialogActions>
                    <Button id='ButtonIsSubmition' onClick={() => handleModuleSubmitCreateQuestion(selectIndex)} color="secondary">
                        Submit
                    </Button>
                    <Button id='ButtonIsCancel' onClick={() => {
                        setSubmitted(false);
                        handleClose();
                    }} color="primary">
                        Cancel
                    </Button>
                </DialogActions>


            </Dialog>
            <SnackBar open={openSnackbar} snackbarTitle={snackbarTitle} close={() => setOpenSnackbar(false)} />
        </>
    );
};

const useStyles = makeStyles(() => ({
    boxItem: {
        border: '1px solid gray',
        padding: '4px 8px',
        fontSize: '0.8rem',
    },
}));

export default AddQuestionModel;