/* eslint-disable camelcase */
/* eslint-disable object-shorthand */
/* eslint-disable react/no-danger */
/* eslint-disable jsx-a11y/role-has-required-aria-props */
/* eslint-disable no-unused-vars */
/* eslint-disable react/jsx-key */
/* eslint-disable consistent-return */
/* eslint-disable func-names */
/* eslint-disable arrow-body-style */
/* eslint-disable no-alert */
/* eslint-disable react-hooks/exhaustive-deps */

import React, { useState, useEffect, useMemo, useRef } from 'react';
import MenuItem from '@mui/material/MenuItem';
import {
    TextField, Button, Box, Badge, Avatar, Typography, IconButton, FormHelperText, InputLabel, Select, FormControl, Grid, Dialog,
    DialogActions, DialogContent, Tooltip,
    Checkbox,
    CardContent, CircularProgress,
    DialogTitle, Alert, Radio, RadioGroup, FormControlLabel, FormGroup, CardActionArea, Card,
    ListItemText
} from "@mui/material";
import { v4 as uuidv4 } from 'uuid';
import AddCircleIcon from '@mui/icons-material/AddCircle';
import AddIcon from '@mui/icons-material/Add';
import EditIcon from '@mui/icons-material/Edit';
import DeleteOutlinedIcon from '@mui/icons-material/DeleteOutlined';
import VisibilityIcon from '@mui/icons-material/Visibility';
import ReactQuill from 'react-quill';
import 'react-quill/dist/quill.snow.css';
import { LoadingButton } from '@mui/lab';
import CloseIcon from '@mui/icons-material/Close';
import ClearIcon from '@mui/icons-material/Clear';
import { useSelector } from 'react-redux';
import { useNavigate, useLocation } from 'react-router-dom'
import { DropzoneArea } from 'material-ui-dropzone';
import KeyboardDoubleArrowLeftIcon from '@mui/icons-material/KeyboardDoubleArrowLeft';
import KeyboardDoubleArrowRightIcon from '@mui/icons-material/KeyboardDoubleArrowRight';
import DOMPurify from 'dompurify';
import { makeStyles } from '@mui/styles';
import katex from "katex";
import SnackBar from '../../../components/snackbar/snackbar';
import adminServices from '../../../services/adminServices';
import Page from '../../../components/Page'
import PageHeader from '../../../components/PageHeader';
import DialogModal from '../../../components/modal/DialogModal';
import './styles.css'
import "katex/dist/katex.min.css";


window.katex = katex;

const modules = {
    toolbar: [
        ["bold", "italic", "underline"],
        [{ list: "ordered" }, { list: "bullet" }],
        [{ script: "sub" }, { script: "super" }],
        [{ header: [1, 2, 3, false] }],
        ["image", { formula: { customClass: 'qlformula' } }],
        [{ color: [] }, { background: [] }],
        [{ align: [] }],
        ["clean"],
    ],
    clipboard: {
        matchVisual: false,
    },
};

const formats = [
    "header",
    "font",
    "size",
    "bold",
    "italic",
    "underline",
    "strike",
    "blockquote",
    "list",
    "bullet",
    "indent",
    "link",
    "image",
    "video",
    "formula",
    "color",
    "background",
    "align",
    "code-block",
    "script",
    "clean",
];

const useStyles = makeStyles((theme) => ({
    formControl: {
        margin: theme.spacing(1),
        minWidth: 120,
    },
    selectEmpty: {
        marginTop: theme.spacing(2),
    },
    background: {
        color: '#000',
        fontWeight: 'bold',
        fontSize: '14px',
        marginBottom: '8px'
    },
    card: {
        maxWidth: 345,
        margin: theme.spacing(2),
    },
    media: {
        height: 140,
    },
}));


const CLATTestUpdate = () => {
    const userInfo = useSelector((state) => state.userInfo && state.userInfo);
    const navigate = useNavigate();
    const location = useLocation();
    const classes = useStyles();
    const [loading, setLoading] = useState(false);
    const [questionList, setQuestionList] = useState([
        {
            id: 1,
            question_text: "What is the fundamental right guaranteed under Article 21 of the Indian Constitution?",
            passage: "Article 21 of the Indian Constitution provides protection of life and personal liberty.",
            name: "Legal Reasoning",
            type: "Legal Reasoning",
            options: [
                { text: "Right to Life and Personal Liberty", isCorrect: true },
                { text: "Right to Equality", isCorrect: false },
                { text: "Right to Freedom", isCorrect: false },
                { text: "Right to Education", isCorrect: false }
            ]
        },
        {
            id: 2,
            question_text: "Which of the following is a synonym for 'ubiquitous'?",
            passage: "The word ubiquitous means existing or being everywhere at the same time.",
            name: "English Language",
            type: "English Language",
            options: [
                { text: "Rare", isCorrect: false },
                { text: "Omnipresent", isCorrect: true },
                { text: "Limited", isCorrect: false },
                { text: "Specific", isCorrect: false }
            ]
        },
        {
            id: 3,
            question_text: "Who is the current Chief Justice of India (as of 2024)?",
            passage: "The Chief Justice of India is the head of the judiciary in India.",
            name: "Current Affairs",
            type: "Current Affairs",
            options: [
                { text: "Justice D.Y. Chandrachud", isCorrect: true },
                { text: "Justice N.V. Ramana", isCorrect: false },
                { text: "Justice S.A. Bobde", isCorrect: false },
                { text: "Justice Ranjan Gogoi", isCorrect: false }
            ]
        },
        {
            id: 4,
            question_text: "If A is taller than B, and B is taller than C, then:",
            passage: "This is a logical reasoning question about height comparison.",
            name: "Logical Reasoning",
            type: "Logical Reasoning",
            options: [
                { text: "A is taller than C", isCorrect: true },
                { text: "C is taller than A", isCorrect: false },
                { text: "A and C are of equal height", isCorrect: false },
                { text: "Cannot be determined", isCorrect: false }
            ]
        },
        {
            id: 5,
            question_text: "What is 25% of 80?",
            passage: "Calculate the percentage value.",
            name: "Quantitative Techniques",
            type: "Quantitative Techniques",
            options: [
                { text: "15", isCorrect: false },
                { text: "20", isCorrect: true },
                { text: "25", isCorrect: false },
                { text: "30", isCorrect: false }
            ]
        }
    ]);
    const [category, setCategory] = useState('');
    const [openSnackbar, setOpenSnackbar] = useState(false);
    const [snackbarTitle, setSnackbarTitle] = useState('');
    const [validationError, setValidationError] = useState(''); 
    const [name, setName] = useState('');
    const [questionid, setQuestionid] = useState('');
    const [questionDetails, setQuestionDetails] = useState({
        justification: '',
        level: '',
        options: [],
        question_text: '',
        question_type: '',
        cognitive_skill_id: ''
    });
    const [editDetails, setEditDetails] = useState('');
    const [complexity, setComplexity] = useState('');
    const [nameError, setNameError] = useState('');
    const [ImageError, setImageError] = useState('');
    const [descriptionError, setDescriptionError] = useState('');
    const [selectedQuestions, setSelectedQuestions] = useState([]);
    const [ComplexityError, setComplexityError] = useState('');
    const [error, setError] = useState(false);
    const [requiredErrors, setRequiredErrors] = useState({
        moduleName: "",
        points: "",
        questionid: "",
    });
    const [detailsError, setDetailsError] = useState({
        level: '',
        questionType: ''
    });
    const [thumbImage, setThumbImage] = useState(null);
    const [thumbPreview, setThumbPreview] = useState(null);
    const [editorValue, setEditorValue] = useState('');
    const [openDialog, setOpenDialog] = useState(false);
    const [OpenDialogNew, setOpenDialogNew] = useState(false);
    const [editData, setEditData] = useState("");
    const [editIndex, setEditIndex] = useState("");
    const [editIndexnew, setEditIndexnew] = useState("");
    const [shortdescription, setShortDescription] = useState('');
    const [editDialog, setEditDialog] = useState(false);
    const [moduleName, setModuleName] = useState('');
    const [selectIndex, setSelectIndex] = useState('');
    const [passage, setPassage] = useState('');
    const [points, setPoints] = useState('');
    const [submitQuestionClicked, setSubmitQuestionClicked] = useState(false);
    const [clatvalues, setClatValues] = useState({
        question: '',
        questionType: 'Legal Reasoning',
        mcqOptions: [{ option: '', isCorrect: false }],
    });
    const [visible, setVisible] = useState(true);
    const [deleteOpen, setDeleteOpen] = useState(false);
    const [deleteData, setDeleteData] = useState('');
    const [open, setOpen] = useState(false);
    const [moduleData, setModuleData] = useState([]);
    const [errorMessage, setErrorMessage] = useState("");
    const [errorMessageAll, setErrorMessageAll] = useState("");
    const [selectedOption, setSelectedOption] = useState('create');
    const [selectedSkills, setSelectedSkills] = useState('');
    const [loadingnew, setLoadingnew] = useState(false);
    const [submitted, setSubmitted] = useState(false);
    const [query, setQuery] = useState('');
    const [isFree, setIsFree] = useState(false);
    const [selectedOptionnew, setSelectedOptionnew] = useState(null);
    const [hasCertificate, setHasCertificate] = useState(false);
    const [Preview, setPreview] = useState(false);
    const [previewData, setPreviewData] = useState('');
    const [details, setDetails] = useState({ level: "", questionType: "Legal Reasoning" });
    const [clatData, setClatData] = useState('');
    const [explanation, clatExplanation] = useState('');
    const [explanationError, setExplanationError] = useState('');
    const [submitError, setSubmitError] = useState('');
    const [clatmodel, setClatModel] = useState(false);
        const [satvalues, setSatValues] = useState({
            question: '',
            questionType: 'English',
            mcqOptions: [{ option: '', isCorrect: false }],
        });
    const [requirederrors, setrequiredErrors] = useState({
        moduleName: "",
        points: "",
        questionid: "",
    });
    const [time, setTime] = useState({
        hours: 0,
        minutes: 0,
        seconds: 0,
    });
    const [loadingQuestion, setLoadingQuestion] = useState(false);
    const [loadingQuestionnew, setLoadingQuestionnew] = useState(false);
    const [search, setSearch] = useState('');
    const [page, setPage] = useState(0);
    const [questionError, setQuestionError] = useState({
        passage: "",
        question: "",
        option: "",
        Objective: '',
        explanation: ""
    });
    const [moduleOptions] = useState(["English Language", "Current Affairs", "Legal Reasoning", "Logical Reasoning", "Quantitative Techniques"]);
    const [weightageIs, setWeightageIs] = useState('');
    const [passageList, setPassageList] = useState([]);
    const [passageError, setPassageError] = useState('')

    const handleDialogOpen = (data) => {
        setErrorMessageAll("")
        if (!name || !complexity || !editorValue) {
            setErrorMessageAll("Please fill out the Above Details.");
        } else {
            setDetails(prevDetails => ({
                ...prevDetails,
                questionType: data
            }));
            setOpen(true);
        }
    };

    const handleClose = () => {
        setPage(0)
        setModuleName('');
        setPoints("")
        setSubmitted(false);
        setQuestionid('')
        setSelectedQuestions([])
        setVisible(true);
        setSelectedOption('create')
        setDetails({ level: "", questionType: "Legal Reasoning" });
        setSelectedSkills("")
        setPassage("")
        clatExplanation('');
        setClatValues({});
        setTimeout(() => {
            setClatValues({
                question: '',
                questionType: 'Legal Reasoning',
                mcqOptions: [{ option: '', isCorrect: false }],
            });
        }, 0);
        setSelectedSkills("");
        setOpen(false);
        setRequiredErrors({})
        setQuestionError({
            passage: "",
            question: "",
            option: "",
            Objective: '',
            explanation: ''
        });
        setRequiredErrors({
            questionid: "",
        })
    };
    const handleModuleNameChange = (event) => {
        const selectedModule = event.target.value;
        setModuleName(selectedModule);
        

        let defaultWeightage = "";
        switch(selectedModule) {
            case "English Language":
                defaultWeightage = "20";
                break;
            case "Current Affairs":
                defaultWeightage = "25";
                break;
            case "Legal Reasoning":
                defaultWeightage = "25";
                break;
            case "Logical Reasoning":
                defaultWeightage = "20";
                break;
            case "Quantitative Techniques":
                defaultWeightage = "10";
                break;
            default:
                defaultWeightage = "";
        }
        

        setWeightageIs(defaultWeightage);
        setPoints(defaultWeightage);
        
     
        setRequiredErrors(prev => ({
            ...prev,
            moduleName: "",
            points: ""
        }));
    };
    const handleModuleSubmitCreate = () => {
  
        if (!moduleName || moduleName.trim() === '') {
            setRequiredErrors(prev => ({
                ...prev,
                moduleName: "Module Name is required!"
            }));
            return;
        }
        
        if (!weightageIs || parseInt(weightageIs, 10) <= 0) {
            setRequiredErrors(prev => ({
                ...prev,
                points: "Module Weight must be greater than 0"
            }));
            return;
        }
        
  
        const newModule = {
            name: moduleName,
            weightage: parseInt(weightageIs, 10),
            questions_list: [],
            moduleId: uuidv4(),
            type: moduleName
        };
        
      
        setModuleData(prev => [...prev, newModule]);
        
        console.log("Module submitted successfully");
        handleClose();
    };

    const handleMCQOptionChangeEnglish = (index, field, value) => {
        const newMCQOptions = [...clatvalues.mcqOptions];
        if (!newMCQOptions[index]) {
            newMCQOptions[index] = {};
        }
        newMCQOptions[index][field] = value;
        setClatValues((prevState) => ({
            ...prevState,
            mcqOptions: newMCQOptions,
        }));
    };

    const handleRemoveOptionEnglish = (index) => {
        const newMCQOptions = [...clatvalues.mcqOptions];
        newMCQOptions.splice(index, 1);
        setClatValues((prevState) => ({
            ...prevState,
            mcqOptions: newMCQOptions,
        }));
    };

    const handleAddOptionEnglish = () => {
        const newOption = {
            id: uuidv4(),
            option: '',
            isCorrect: false,
        };
        setClatValues((prevState) => ({
            ...prevState,
            mcqOptions: [...prevState.mcqOptions, newOption],
        }));
    };

    const handleChangeQuestionEnglish = (field, value) => {
        setQuestionError({
            Objective: ''
        });
        setClatValues((prevState) => ({
            ...prevState,
            [field]: value,
        }));
    };

    const ClearError = () => {
        setRequiredErrors({
            moduleName: "",
            points: "",
            questionid: "",
        });
        setDetailsError({
            level: '',
            questionType: ''
        });
        setQuestionError({
            passage: "",
            question: "",
            option: "",
            Objective: '',
            explanation: ""
        });
    };

    const handlePoints = (e) => {
         const value = e.target.value.replace(/\D/, '');
        if (value === '0') {
            setrequiredErrors({
                points: "Value cannot be 0",
            })
        } else {
            setrequiredErrors({
                points: "",
            })
        }
        setPoints(value);
    };

    const handleChangeOption = (e) => {
        setSelectedOption(e.target.value)
        if (e.target.value === 'select') {
            setSubmitQuestionClicked(true);
        }
        setRequiredErrors({
            moduleName: "",
            points: "",
            questionid: "",
        });
    };

    const handleChangeCheck = (event) => {
        setQuestionError({
            Objective: ''
        });
        setSelectedSkills(event.target.value);
    };

    const handleLeftArrowClick = () => {
        setLoadingQuestion(true)
        setPage(prev => prev - 1);
        // getQuestionList(details.questionType, search) 
    };

    const handleRightArrowClick = () => {
        setLoadingQuestion(true)
        setPage(prev => prev + 1);
        // getQuestionList(details.questionType, search)
    };

    const CreateQuestion = (e) => {
        setSearch(e.target.value)
        setQuestionDetails(e.target.value)
        setVisible(true);
    };


    const filteredQuestions = questionList.filter(question =>
        question.question_text.toLowerCase().includes(search.toLowerCase()) ||
        question.name.toLowerCase().includes(search.toLowerCase()) ||
        question.passage.toLowerCase().includes(search.toLowerCase())
    );

    const handleAddQuestion = (question) => {
        setSelectedQuestions((prev) => {
            if (prev.find(q => q.id === question.id)) return prev;
            setQuestionDetails(question.question_text)
            setSelectedSkills(question?.name ? question?.name : '')
            setQuestionid((prev) => [...prev, question.id]);
            return [...prev, question];
        });
    };

    const handleDeselect = (id) => {
        setSelectedQuestions((prev) => prev.filter((q) => q.id !== id));
        setQuestionid((prev) => prev.filter((q) => q !== id));
    };

    const validateModuleFields = () => {
    
        if (!moduleName || moduleName.trim() === '') {
            setValidationError("Module Name is required");
            setOpenSnackbar(true);
            return false;
        }

        if (!points || points.trim() === '') {
            setValidationError("Module Weight is required");
            setOpenSnackbar(true);
            return false;
        } if (parseInt(points, 10) <= 0) {
            setValidationError("Module Weight must be greater than 0");
            setOpenSnackbar(true);
            return false;
        }


        if (!details.level || details.level.trim() === '') {
            setValidationError('Level is required');
            setOpenSnackbar(true);
            return false;
        }

  
        if (!selectedSkills || selectedSkills.trim() === '') {
            setValidationError('Please select an objective');
            setOpenSnackbar(true);
            return false;
        }


        if (details.questionType === 'Clat' && (!passage || passage.trim() === '' || passage === '<p><br></p>')) {
            setValidationError('Passage is required for CLAT questions');
            setOpenSnackbar(true);
            return false;
        }

       
        if (selectedOption === 'select' && (!questionid || questionid.length === 0)) {
            setValidationError("Please select at least one question");
            setOpenSnackbar(true);
            return false;
        }

        return true;
    };

    const SubmitQuestion = async () => {
        if (!validateModuleFields()) {
            return;
        }
        console.log("Submit question functionality - to be implemented");
        setSubmitted(true);
    };

    const sanitizeConfig = {
        ALLOWED_TAGS: ['b', 'i', 'em', 'a', 'ul', 'ol', 'li'],
    };


    const [mathsvalues, setMathsValues] = useState({
        question: '',
        questionType: 'Quantitative Techniques',
        mcqOptions: [{ option: '', isCorrect: false }],
    });

    const [questionsArray, setQuestionsArray] = useState([
        {
            id: uuidv4(),
            question: '',
            questionType: 'Legal Reasoning',
            mcqOptions: [
                { id: uuidv4(), option: '', isCorrect: false }
            ]
        }
    ]);

    const handleChangeMathsQuestion = (field, value) => {
        setQuestionError({
            Objective: '',
            question: ''
        });
        setMathsValues((prevState) => ({
            ...prevState,
            [field]: value,
        }));
    };

    const handleMCQOptionChangeMaths = (index, field, value) => {
        const newMCQOptions = [...mathsvalues.mcqOptions];
        if (!newMCQOptions[index]) {
            newMCQOptions[index] = {};
        }
        newMCQOptions[index][field] = value;
        setMathsValues((prevState) => ({
            ...prevState,
            mcqOptions: newMCQOptions,
        }));
    };

    const handleRemoveOptionMaths = (index) => {
        const newMCQOptions = [...mathsvalues.mcqOptions];
        newMCQOptions.splice(index, 1);
        setMathsValues((prevState) => ({
            ...prevState,
            mcqOptions: newMCQOptions,
        }));
    };

    const handleAddOptionMaths = () => {
        const newOption = {
            id: uuidv4(),
            option: '',
            isCorrect: false,
        };
        setMathsValues((prevState) => ({
            ...prevState,
            mcqOptions: [...prevState.mcqOptions, newOption],
        }));
    };

    
    const handleAddNewQuestion = () => {
        const newQuestion = {
            id: uuidv4(),
            question: '',
            questionType: 'Legal Reasoning',
            mcqOptions: [
                { id: uuidv4(), option: '', isCorrect: false }
            ]
        };
        setQuestionsArray(prev => [...prev, newQuestion]);
    };

    const handleRemoveQuestionFromArray = (questionId) => {
        setQuestionsArray(prev => prev.filter(q => q.id !== questionId));
    };

    const handleQuestionArrayChange = (questionId, field, value) => {
        setQuestionsArray(prev => prev.map(q =>
            q.id === questionId ? { ...q, [field]: value } : q
        ));


        if (field === 'question' && value.trim() !== '') {
            setQuestionError(prev => ({
                ...prev,
                question: ''
            }));
        }
    };

    const handleQuestionArrayOptionChange = (questionId, optionIndex, field, value) => {
        setQuestionsArray(prev => prev.map(q => {
            if (q.id === questionId) {
                const newOptions = [...q.mcqOptions];
                if (!newOptions[optionIndex]) {
                    newOptions[optionIndex] = {};
                }
                newOptions[optionIndex][field] = value;
                return { ...q, mcqOptions: newOptions };
            }
            return q;
        }));
    };

    const handleAddQuestionArrayOption = (questionId) => {
        const newOption = {
            id: uuidv4(),
            option: '',
            isCorrect: false,
        };
        setQuestionsArray(prev => prev.map(q =>
            q.id === questionId
                ? { ...q, mcqOptions: [...q.mcqOptions, newOption] }
                : q
        ));
    };

    const handleRemoveQuestionArrayOption = (questionId, optionIndex) => {
        setQuestionsArray(prev => prev.map(q => {
            if (q.id === questionId) {
                const newOptions = [...q.mcqOptions];
                newOptions.splice(optionIndex, 1);
                return { ...q, mcqOptions: newOptions };
            }
            return q;
        }));
    };

    
    useEffect(() => {
        const hardcodedClatData = {
            assessmentData: {
                title: "CLAT Mock Test 2024",
                complexity_level: "intermediate",
                time_in_mins: 7200, 
                image_name: "https://via.placeholder.com/400x200?text=CLAT+Assessment",
                short_description: "Comprehensive CLAT preparation test covering all sections",
                highlighted_section: "Focus on Legal Reasoning and Current Affairs"
            },
            detailedAssessmentData: [
                {
                    module_name: "Legal Reasoning",
                    weightage: 50,
                    am_questions_list: [1, 2, 3, 4, 5],
                    id: 1,
                    module_type: "Legal Reasoning"
                },
                {
                    module_name: "English Language",
                    weightage: 40,
                    am_questions_list: [6, 7, 8, 9],
                    id: 2,
                    module_type: "English Language"
                },
                {
                    module_name: "Current Affairs",
                    weightage: 60,
                    am_questions_list: [10, 11, 12, 13, 14, 15],
                    id: 3,
                    module_type: "Current Affairs"
                },
                {
                    module_name: "Logical Reasoning",
                    weightage: 30,
                    am_questions_list: [16, 17, 18],
                    id: 4,
                    module_type: "Logical Reasoning"
                },
                {
                    module_name: "Quantitative Techniques",
                    weightage: 20,
                    am_questions_list: [19, 20],
                    id: 5,
                    module_type: "Quantitative Techniques"
                }
            ]
        };
        setClatData(hardcodedClatData);
    }, []);

    useMemo(() => {
        if (clatData && clatData?.assessmentData) {
            setName(clatData?.assessmentData?.title)
            setComplexity(clatData?.assessmentData?.complexity_level)
            const totalSeconds = clatData?.assessmentData?.time_in_mins
            const hours = Math.floor(totalSeconds / 3600);
            const minutes = Math.floor((totalSeconds % 3600) / 60);
            const remainingSeconds = totalSeconds % 60;
            setTime({
                hours: hours || 0,
                minutes: minutes || 0,
                seconds: remainingSeconds || 0,
            })
            setThumbPreview(clatData?.assessmentData?.image_name)
            setThumbImage(clatData?.assessmentData?.image_name)
            setEditorValue(clatData?.assessmentData?.short_description)
            setShortDescription(clatData?.assessmentData?.highlighted_section)
        }

        if (clatData && clatData?.detailedAssessmentData) {
            const questionList = clatData?.detailedAssessmentData?.map(({ module_name, weightage, am_questions_list, id, module_type }) => ({
                name: module_name,
                weightage: parseInt(weightage, 10),
                questions_list: am_questions_list,
                moduleId: id,
                type: module_type
            }));
            setModuleData(questionList)
        }
    }, [clatData])


    const validateRequiredFields = () => {
        if (!name || name.trim() === '') {
            setValidationError('CLAT Name is required');
            setOpenSnackbar(true);
            return false;
        }

        if (!complexity || complexity.trim() === '') {
            setValidationError('Complexity Level is required');
            setOpenSnackbar(true);
            return false;
        }

        if (!thumbImage && !thumbPreview) {
            setValidationError('Thumb Image is required');
            setOpenSnackbar(true);
            return false;
        }

        if (!editorValue || editorValue.trim() === '' || editorValue === '<p><br></p>') {
            setValidationError('CLAT Description is required');
            setOpenSnackbar(true);
            return false;
        }

  
        if (questionsArray && questionsArray.length === 0) {
            setValidationError('Please add at least one module');
            setOpenSnackbar(true);
            return false;
        }

        return true;
    };

    const handleUpdate = async () => {
        if (!validateRequiredFields()) {
            return;
        }
        
        setLoading(true);
        const totalSeconds = (time.hours * 3600) + (time.minutes * 60) + time.seconds;
        const selectedQuestions = moduleData && moduleData?.map(module => module.questions_list).flat()
        
        moduleData.forEach((data) => {
            delete data.questiondetails;
        });
        
        const formData = new FormData();
        formData.append('name', name);
        formData.append('complexity_level', complexity);
        formData.append('description', editorValue);
        formData.append('time_in_mins', totalSeconds);
        formData.append('thumbImage', thumbImage);
        formData.append('modules', JSON.stringify(moduleData));
        formData.append('is_published', false);
        formData.append('userId', userInfo?.id || '');
        formData.append('selectedQuestions', selectedQuestions);
        formData.append('is_free', isFree);
        formData.append('hasCertificate', hasCertificate);
        formData.append("short_description", shortdescription);

        try {
            setTimeout(() => {
                setSnackbarTitle('CLAT Assessment updated successfully');
                setOpenSnackbar(true);
                setTimeout(() => {
                    navigate("/app/CLAT")
                }, 2000);
                setLoading(false);
            }, 1000);
        } catch (error) {
            console.log(error);
            setValidationError('Failed to update CLAT Assessment');
            setOpenSnackbar(true);
            setLoading(false);
        }
    };

    const handleFileChange = (file) => {
        setImageError("")
        if (file && file.length > 0) {
            if (file[0]?.size < 2097152) {
                const url = URL.createObjectURL(file[0]);
                setThumbPreview(url);
                setThumbImage(file[0]);
           
                setImageError('');
            } else {
                setImageError("File size should be less than 2MB")
            }
        }
    };

    return (
        <>

                        <Dialog open={open} onClose={handleClose} fullWidth
                            sx={{
                                '& .MuiDialog-paper': {
                                    maxHeight: '75vh !important',
                                    overflow: 'hidden !important'
                                }
                            }}
                        >
                            <DialogTitle style={{ paddingBottom: '0px' }}>Add Module</DialogTitle>
                            <DialogContent className='GACognitivesection' sx={{ paddingTop: '25px !important' }}>
                                <Box sx={{ marginBottom: '18px' }}>
                                    {/* <TextField
                                        variant="outlined"
                                        inputProps={{ maxLength: 50 }}
                                        fullWidth
                                        id="addname"
                                        label="Module Name *"
                                        type="search"
                                        value={moduleName}
                                        onChange={(e) => { setModuleName(e.target.value); ClearError() }}
                                        sx={{
                                            // bgcolor: "#f0f0f0",
                                            borderRadius: 1,
        
                                            height: 36,
                                            '& .MuiInputBase-input': {
                                                fontSize: 14,
                                                padding: "8px 12px",
                                            },
        
                                        }}
                                    /> */}
                                     <FormControl fullWidth variant="outlined" sx={{ borderRadius: 1 }}>
                                    
                                    <InputLabel id="module-name-label">Module Name *</InputLabel>
                                    <Select
                                        labelId="module-name-label"
                                        id="addname"
                                        value={moduleName}
                                        onChange={handleModuleNameChange} 
                                        label="Module Name *"
                                        sx={{
                                            borderRadius: 1,
                                            height: 36,
                                            '& .MuiSelect-select': {
                                                fontSize: 14,
                                                padding: "8px 12px",
                                                width: '100%',
                                         
                                            },
                                        }}
                                        error={!!requiredErrors.moduleName}
                                    >
                                        {moduleOptions.map((option) => (
                                            <MenuItem key={option} value={option}>
                                                {option}
                                            </MenuItem>
                                        ))}
                                    </Select>
                                    {requiredErrors.moduleName && (
                                        <FormHelperText error>{requiredErrors.moduleName}</FormHelperText>
                                    )}
                                    </FormControl>
                                </Box>
                                <Box sx={{ marginBottom: '15px', marginTop  : '5px' }}>
                                    <TextField
                                        variant="outlined"
                                        inputProps={{ 
                                            maxLength: 50, 
                                            inputMode: 'numeric', 
                                            pattern: '[0-9]*',
                                            readOnly: true 
                                        }}
                                        fullWidth
                                        id="addweight"
                                        label="Module Weight *"
                                        type="number"
                                        value={weightageIs}  
                                        sx={{
                                            borderRadius: 1,
                                            height: 36,
                                            '& .MuiInputBase-input': {
                                                fontSize: 14,
                                                padding: "8px 12px",
                                            },
                                        }}
                                    />
                                    {requiredErrors.points && (
                                        <FormHelperText style={{ marginBottom: '5px' }} error>{requiredErrors.points}</FormHelperText>
                                    )}
                                </Box>
                                {errorMessage && (
                                    <Alert severity="error" sx={{ marginBottom: '10px' }}>
                                        {errorMessage}
                                    </Alert>
                                )}
        
                                <FormControl component="fieldset">
                                    <RadioGroup
                                        row
                                        value={selectedOption}
                                        onChange={(e) => handleChangeOption(e)}
                                    >
        
                                        <FormControlLabel value="create" id={`createRadio${details?.questionType}`} control={<Radio />} sx={{ marginRight: '40px' }} label="Create Question" />
                                        <FormControlLabel value="select" id={`radioSelect${details?.questionType}`} control={<Radio />} label="Select Question" />
                                    </RadioGroup>
                                </FormControl>
        
                                {selectedOption === 'select' ? (
                            <Box display="flex" flexDirection="column" gap={2}>
                                <Box style={{ marginTop: "10px" }}>
                                    <Grid container spacing={2}>


                                        <Grid item xs={6}>
                                            <FormControl style={{ display: "block", width: "100%" }} className={classes.formControl}
                                            // error={touched.questionType && Boolean(errors.questionType)}
                                            >
                                                <InputLabel id="demo-simple-select-standard-label">Level*</InputLabel>
                                                <Select name="level" labelId="demo-simple-select-standard-label"
                                                    disabled={submitted}
                                                    style={{ width: "100%" }}
                                                    id="level"
                                                    label="Level"
                                                    value={details.level}
                                                    onChange={(e) => { 
                                                    setDetails(prevDetails => ({
                                                        ...prevDetails,
                                                        level: e.target.value
                                                    })); 
                                                    // setDetailsError({level:''}); 
                                                }}
                                                    displayEmpty
                                                >
                                                    <MenuItem value="easy">Easy</MenuItem>
                                                    <MenuItem value="medium">Medium</MenuItem>
                                                    <MenuItem value="complex">Complex</MenuItem>
                                                </Select>

                                                
                                               {detailsError.level && (
                                                <FormHelperText error>{detailsError.level}</FormHelperText>
                                                )}

                                            </FormControl>
                                        </Grid>


                                        <Grid item>
                                            <FormControl fullWidth variant="outlined">

                                                <Typography style={{ marginBottom: '0px' }} color="primary" className={classes.background} gutterBottom variant="subtitle1">
                                                    Select Level of Objective</Typography>
                                                <FormGroup className='FormCheck'>
                                                    {['Knowledge', 'Comprehension', 'Application', 'Analysis', 'Synthesis', 'Evaluation'].map((skill) => (
                                                        <FormControlLabel
                                                            readOnly={submitted}
                                                            key={skill}
                                                            id={`radio${skill}Get`}
                                                            control={
                                                                <Radio
                                                                    disabled={submitted}
                                                                    id={`radioVal${skill}`}
                                                                    checked={selectedSkills === skill}
                                                                    onChange={handleChangeCheck}
                                                                    value={skill}
                                                                />
                                                            }
                                                            label={skill}
                                                        />
                                                    ))}
                                                </FormGroup>
                                                {questionError && questionError.Objective && <FormHelperText error  >{questionError?.Objective}</FormHelperText >}

                                            </FormControl>
                                        </Grid>

                                                   
                                                                                               <Grid item xs={12} sm={12} sx={{ marginBottom: '0px', paddingRight: '18px' }}>
                                                                                                   <InputLabel id="passage-label">Passage *</InputLabel>
                                                                                                     <Select
                                                                                                           name="passage"
                                                                                                           labelId="passage-label"
                                                                                                           id="passage"
                                                                                                           // multiple
                                                                                                           value={passage || ''}
                                                                                                           onChange={(e) =>{
                                                                                                               setPassage(e.target.value);
                                                                                                               setPassageError('');
                                                                                                           }}
                                                                                                           renderValue={(selectedId) => { 
                                                                                                               const selectedPassage = passageList.find(
                                                                                                                   (passageItem) => passageItem.id === selectedId
                                                                                                               );
                                                                                                               return selectedPassage ? selectedPassage.title : ''; 
                                                                                                           }}
                                                                                                       >
                                                                                                       {passageList?.length > 0 && passageList.map((passage) => (
                                                                                                           <MenuItem key={passage.id} value={passage.id}>
                                                                                                               <Checkbox checked={passage?.includes(passage.id)} />
                                                                                                               <ListItemText primary={passage.title} />
                                                                                                           </MenuItem>
                                                                                                       ))}
                                                                                                   </Select>     
                                                                                                   {questionError && questionError.passage && <FormHelperText error  >{questionError?.passage}</FormHelperText >}
                                               
                                                                                               </Grid>
                                                                                       

                                        {details?.questionType === 'Clat' ?
                                            <>
                                                <div style={{ paddingLeft: "15px", width: "100%" }}>

                                                    <Grid item xs={12}>
                                                        <div className="search-select-container" style={{ display: 'flex', flexDirection: "column" }}>
                                                            <div className="search-select-container" style={{ display: 'flex', }}>
                                                                {page > 0 && <IconButton aria-label="Previous" onClick={handleLeftArrowClick}>
                                                                    <KeyboardDoubleArrowLeftIcon />
                                                                </IconButton>}
                                                                <input
                                                                    type="text"
                                                                    readOnly={submitted}
                                                                    onChange={CreateQuestion}
                                                                    placeholder="Search or Select 1111"
                                                                    aria-label="Search or select an option"
                                                                    style={{ flex: 1, padding: '8px', borderRadius: '4px', border: '1px solid #ccc' }}
                                                                />
                                                                <IconButton aria-label="Next" onClick={handleRightArrowClick}>
                                                                    <KeyboardDoubleArrowRightIcon />
                                                                </IconButton>
                                                            </div>
                                                            {loadingQuestion &&
                                                                <Typography style={{ justifyContent: 'center', alignItems: 'center', fontSize: '20' }}>
                                                                    Loading.....
                                                                </Typography>
                                                            }
                                                            {visible && !loadingQuestion && (
                                                                <ul className="dropdown" role="listbox" aria-expanded={query.length > 0}>
                                                                    {filteredQuestions && filteredQuestions?.length > 0 ? (
                                                                        filteredQuestions.map((item) => {
                                                                            const sanitizedQuestion = item.question_text
                                                                                .replace(/<p>/g, '<span style="display: flex;">')
                                                                                .replace(/<\/p>/g, '</span>')
                                                                                .replace(/&nbsp;/g, ' ');
                                                                            const sanitizedText = DOMPurify.sanitize(item.question_text, sanitizeConfig).replace(/<[^>]*>/g, '').trim().replace(/&nbsp;/g, ' ');
                                                                            const sanitizedPassage = DOMPurify.sanitize(item.passage, sanitizeConfig).replace(/<[^>]*>/g, '').trim().replace(/&nbsp;/g, ' ');
                                                                            const isSelected = questionid && questionid?.some(q => q === item.id);

                                                                            return (
                                                                                <li
                                                                                    key={item.id}
                                                                                    role="option"
                                                                                    className="dropdown-item"
                                                                                    tabIndex={0}
                                                                                    aria-selected={selectedOptionnew?.id === item.id}
                                                                                    title={sanitizedPassage}
                                                                                    style={{
                                                                                        backgroundColor: isSelected ? '#e0ffe0' : 'transparent',
                                                                                        padding: '8px',
                                                                                        borderRadius: '4px',
                                                                                        marginBottom: '4px',
                                                                                    }}
                                                                                >
                                                                                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                                                                                        <span dangerouslySetInnerHTML={{ __html: sanitizedQuestion }} />

                                                                                        {!isSelected && (
                                                                                            <Button
                                                                                                id="questionadd"
                                                                                                variant="outlined"
                                                                                                color="primary"
                                                                                                onClick={(e) => {
                                                                                                    e.stopPropagation();
                                                                                                    handleAddQuestion(item);
                                                                                                }}
                                                                                                sx={{
                                                                                                    fontSize: '0.75rem',
                                                                                                    minWidth: '24px',
                                                                                                    minHeight: '24px',
                                                                                                    padding: '2px',
                                                                                                    borderRadius: '12px',
                                                                                                }}
                                                                                            >
                                                                                                +
                                                                                            </Button>
                                                                                        )}
                                                                                    </div>
                                                                                </li>
                                                                            );
                                                                        })
                                                                    ) : (
                                                                        <li className="dropdown-item" role="option">
                                                                            No results found
                                                                        </li>
                                                                    )}

                                                                </ul>

                                                            )}
                                                            {
                                                                !loadingQuestion && selectedQuestions && selectedQuestions.length > 0 && (
                                                                    <>
                                                                        <Typography>Selected Questions:</Typography>
                                                                        {selectedQuestions.map((item, index) => {
                                                                            const sanitizedQuestion = item.question_text
                                                                                .replace(/<p>/g, '<span style="display: flex;">')
                                                                                .replace(/<\/p>/g, '</span>')
                                                                                .replace(/&nbsp;/g, ' ');
                                                                            const sanitizedPassage = DOMPurify.sanitize(item.passage, sanitizeConfig).replace(/<[^>]*>/g, '').trim().replace(/&nbsp;/g, ' ');
                                                                            return (
                                                                                <Tooltip
                                                                                    key={index}
                                                                                    title={sanitizedPassage}
                                                                                    placement="top"
                                                                                >
                                                                                    <div style={{ display: 'flex' }}>
                                                                                        <Typography
                                                                                            id="QuestionArea"
                                                                                            dangerouslySetInnerHTML={{ __html: sanitizedQuestion }}
                                                                                        />
                                                                                        <Button onClick={() => handleDeselect(item.id)}>
                                                                                            x
                                                                                        </Button>
                                                                                    </div>
                                                                                </Tooltip>
                                                                            );
                                                                        }
                                                                        )}
                                                                    </>
                                                                )
                                                            }



                                                        </div>
                                                    </Grid>

                                                </div>
                                            </>
                                            :
                                            <>
                                                <div style={{ paddingLeft: "15px", width: "100%" }}>

                                                    <Grid item xs={12}>
                                                        <div className="search-select-container" style={{ display: 'flex', flexDirection: "column" }}>
                                                            <div className="search-select-container" style={{ display: 'flex', }}>
                                                                {page > 0 && <IconButton disabled={questionList?.length === 0}  aria-label="Previous" onClick={handleLeftArrowClick}>
                                                                    <KeyboardDoubleArrowLeftIcon />
                                                                </IconButton>}
                                                                <input
                                                                    type="text"
                                                                    readOnly={submitted}
                                                                    onChange={CreateQuestion}
                                                                    placeholder="Search or Select 2222"
                                                                    aria-label="Search or select an option"
                                                                    style={{ flex: 1, padding: '8px', borderRadius: '4px', border: '1px solid #ccc' }}
                                                                />
                                                                <IconButton aria-label="Next" disabled={questionList?.length === 0}  onClick={handleRightArrowClick}>
                                                                    <KeyboardDoubleArrowRightIcon />
                                                                </IconButton>
                                                            </div>
                                                            {loadingQuestion &&
                                                                <Typography style={{ justifyContent: 'center', alignItems: 'center', fontSize: '20' }}>
                                                                    Loading.....
                                                                </Typography>
                                                            }
                                                            {visible && !loadingQuestion && (
                                                                <ul className="dropdown" role="listbox" aria-expanded={query.length > 0}>
                                                                    {filteredQuestions && filteredQuestions?.length > 0 ? (
                                                                        filteredQuestions.map((item) => {
                                                                            const sanitizedQuestion = item.question_text
                                                                                .replace(/<p>/g, '<span style="display: flex;">')
                                                                                .replace(/<\/p>/g, '</span>')
                                                                                .replace(/&nbsp;/g, ' ');
                                                                            const sanitizedText = DOMPurify.sanitize(item.question_text, sanitizeConfig).replace(/<[^>]*>/g, '').trim().replace(/&nbsp;/g, ' ');
                                                                            const sanitizedPassage = DOMPurify.sanitize(item.passage, sanitizeConfig).replace(/<[^>]*>/g, '').trim().replace(/&nbsp;/g, ' ');
                                                                            const isSelected = questionid && questionid?.some(q => q === item.id);

                                                                            return (
                                                                                <li
                                                                                    key={item.id}
                                                                                    role="option"
                                                                                    className="dropdown-item"
                                                                                    tabIndex={0}
                                                                                    aria-selected={selectedOptionnew?.id === item.id}
                                                                                    title={sanitizedPassage}
                                                                                    style={{
                                                                                        backgroundColor: isSelected ? '#e0ffe0' : 'transparent',
                                                                                        padding: '8px',
                                                                                        borderRadius: '4px',
                                                                                        marginBottom: '4px',
                                                                                    }}
                                                                                >
                                                                                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                                                                                        <span dangerouslySetInnerHTML={{ __html: sanitizedQuestion }} />

                                                                                        {!isSelected && (
                                                                                            <Button
                                                                                                id="questionadd"
                                                                                                variant="outlined"
                                                                                                color="primary"
                                                                                                onClick={(e) => {
                                                                                                    e.stopPropagation();
                                                                                                    handleAddQuestion(item);
                                                                                                }}
                                                                                                sx={{
                                                                                                    fontSize: '0.75rem',
                                                                                                    minWidth: '24px',
                                                                                                    minHeight: '24px',
                                                                                                    padding: '2px',
                                                                                                    borderRadius: '12px',
                                                                                                }}
                                                                                            >
                                                                                                +
                                                                                            </Button>
                                                                                        )}
                                                                                    </div>
                                                                                </li>
                                                                            );
                                                                        })
                                                                    ) : (
                                                                        <li className="dropdown-item" role="option">
                                                                            No results found
                                                                        </li>
                                                                    )}

                                                                </ul>

                                                            )}

                                                            {
                                                                !loadingQuestion && selectedQuestions && selectedQuestions.length > 0 && (
                                                                    <>
                                                                        <Typography>Selected Questions:</Typography>
                                                                        {selectedQuestions.map((item, index) => {
                                                                            const sanitizedQuestion = item.question_text
                                                                                .replace(/<p>/g, '<span style="display: flex;">')
                                                                                .replace(/<\/p>/g, '</span>')
                                                                                .replace(/&nbsp;/g, ' ');
                                                                            const sanitizedPassage = DOMPurify.sanitize(item.passage, sanitizeConfig).replace(/<[^>]*>/g, '').trim().replace(/&nbsp;/g, ' ');
                                                                            return (
                                                                                <Tooltip
                                                                                    key={index}
                                                                                    title={sanitizedPassage}
                                                                                    placement="top"
                                                                                >
                                                                                    <div style={{ display: 'flex' }}>
                                                                                        <Typography
                                                                                            id="QuestionArea"
                                                                                            dangerouslySetInnerHTML={{ __html: sanitizedQuestion }}
                                                                                        />
                                                                                        <Button onClick={() => handleDeselect(item.id)}>
                                                                                            x
                                                                                        </Button>
                                                                                    </div>
                                                                                </Tooltip>
                                                                            );
                                                                        }
                                                                        )}


                                                                    </>
                                                                )
                                                            }
                                                        </div>
                                                    </Grid>

                                                </div>
                                            </>
                                        }

                                        {selectedOption === 'create' &&
                                            <Grid item xs={12}>
                                                <LoadingButton
                                                    id="subMitButton"
                                                    type="submit"
                                                    variant="contained"
                                                    color="primary"
                                                    onClick={CreateQuestion}
                                                    loading={loadingnew}
                                                    fullWidth
                                                >
                                                    Submit
                                                </LoadingButton>
                                            </Grid>}
                                    </Grid>
                                </Box>

                            </Box>
                        ): (
        
                                    <Box display="flex" flexDirection="column" style={{ marginTop: "10px" }} gap={2}>
                                        <Grid container spacing={2}>
                                            <Grid item xs={6} fullWidth >
                                                <FormControl className={classes.formControl}
                                                    style={{ width: "100%" }}
                                                // error={touched.questionType && Boolean(errors.questionType)}
                                                >
                                                    <InputLabel id="demo-simple-select-standard-label">Level*</InputLabel>
                                                    <Select
                                                        disabled={submitted}
                                                        name="level"
                                                        labelId="demo-simple-select-standard-label"
                                                        id="level"
                                                        label="Level"
                                                        value={details.level}
                                                        onChange={(e) => setDetails(prevDetails => ({
                                                            ...prevDetails,
                                                            level: e.target.value
                                                        }))}
                                                        displayEmpty
                                                    >
                                                        <MenuItem value="easy">Easy</MenuItem>
                                                        <MenuItem value="medium">Medium</MenuItem>
                                                        <MenuItem value="complex">Complex</MenuItem>
                                                    </Select>
                                                    {detailsError.level && (
                                                    <FormHelperText error>{detailsError.level}</FormHelperText>
                                                    )}
                                                    
        
                                                </FormControl>
                                            </Grid>
        
        
                                            <Grid item>
                                                <FormControl fullWidth variant="outlined">
        
                                                    <Typography style={{ marginBottom: '0px' }} color="primary" className={classes.background} gutterBottom variant="subtitle1">
                                                        Select Level of Objective</Typography>
                                                    <FormGroup className='FormCheck'>
                                                        {['Knowledge', 'Comprehension', 'Application', 'Analysis', 'Synthesis', 'Evaluation'].map((skill) => (
                                                            <FormControlLabel
                                                                key={skill}
                                                                id={`Label${skill}`}
                                                                control={
                                                                    <Radio
                                                                        id={skill}
                                                                        checked={selectedSkills === skill}
                                                                        onChange={handleChangeCheck}
                                                                        value={skill}
                                                                    />
                                                                }
                                                                label={skill}
                                                            />
                                                        ))}
                                                    </FormGroup>
                                                    {questionError && questionError.Objective && <FormHelperText error  >{questionError?.Objective}</FormHelperText >}
        
                                                </FormControl>
                                            </Grid>
        
                                           
                                                      <Grid item xs={12} sm={12} sx={{ marginBottom: '0px', paddingRight: '18px' }}>
                                                                                                   <InputLabel id="passage-label">Passage *</InputLabel>
                                                                                                     <Select
                                                                                                           name="passage"
                                                                                                           labelId="passage-label"
                                                                                                           id="passage"
                                                                                                           // multiple
                                                                                                           value={passage || ''}
                                                                                                           onChange={(e) =>{
                                                                                                               setPassage(e.target.value);
                                                                                                               setPassageError('');
                                                                                                           }}
                                                                                                           renderValue={(selectedId) => { 
                                                                                                               const selectedPassage = passageList.find(
                                                                                                                   (passageItem) => passageItem.id === selectedId
                                                                                                               );
                                                                                                               return selectedPassage ? selectedPassage.title : ''; 
                                                                                                           }}
                                                                                                       >
                                                                                                       {passageList.map((passage) => (
                                                                                                           <MenuItem key={passage.id} value={passage.id}>
                                                                                                               <Checkbox checked={passage?.includes(passage.id)} />
                                                                                                               <ListItemText primary={passage.title} />
                                                                                                           </MenuItem>
                                                                                                       ))}
                                                                                                   </Select>     
                                                                                                   {questionError && questionError.passage && <FormHelperText error  >{questionError?.passage}</FormHelperText >}
                                               
                                                                                               </Grid>
        
             <DialogContent>
                                <Box sx={{ mt: 2 }}>
                                    <Typography variant="h6" sx={{ mb: 2, fontWeight: 'bold' }}>
                                        Questions List
                                    </Typography>

                                    {questionsArray?.length > 0 && questionsArray.map((question, questionIndex) => (
                                        <Card key={question.id} sx={{ mb: 3, p: 2, border: '1px solid #e0e0e0' }}>
                                            <Grid container spacing={2}>
                                                {/* Question Header */}
                                                <Grid item xs={12} sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                                                    <Typography variant="subtitle1" fontWeight="bold">
                                                        Question {questionIndex + 1}
                                                    </Typography>
                                                    <IconButton
                                                        color="error"
                                                        onClick={() => handleRemoveQuestionFromArray(question.id)}
                                                        size="small"
                                                    >
                                                        <DeleteOutlinedIcon />
                                                    </IconButton>
                                                </Grid>

                                               
                                                <Grid item xs={12}>
                                                    <Typography variant="subtitle2" sx={{ mb: 1 }}>
                                                        Question Text *
                                                    </Typography>
                                                    <ReactQuill
                                                        theme="snow"
                                                        value={question.question}
                                                        onChange={(value) => handleQuestionArrayChange(question.id, 'question', value)}
                                                        modules={modules}
                                                        formats={formats}
                                                        placeholder="Enter question text..."
                                                        style={{ marginBottom: '10px' }}
                                                    />
                                                </Grid>

                                                {/* MCQ Options */}
                                                <Grid item xs={12}>
                                                    <Typography variant="subtitle2" sx={{ mb: 1 }}>
                                                        Options
                                                    </Typography>
                                                    {question.mcqOptions.map((option, optionIndex) => (
                                                        <Box key={option.id} sx={{ mb: 2, p: 1, border: '1px solid #f0f0f0', borderRadius: 1 }}>
                                                            <Grid container spacing={1} alignItems="center">
                                                                <Grid item xs={9}>
                                                                    <ReactQuill
                                                                        theme="snow"
                                                                        value={option.option}
                                                                        onChange={(value) => handleQuestionArrayOptionChange(question.id, optionIndex, 'option', value)}
                                                                        modules={modules}
                                                                        formats={formats}
                                                                        placeholder={`Option ${optionIndex + 1}`}
                                                                        style={{ fontSize: '14px' }}
                                                                    />
                                                                </Grid>
                                                                <Grid item xs={2}>
                                                                    <FormControlLabel
                                                                        control={
                                                                            <Checkbox
                                                                                checked={option.isCorrect}
                                                                                onChange={() => handleQuestionArrayOptionChange(question.id, optionIndex, 'isCorrect', !option.isCorrect)}
                                                                                size="small"
                                                                            />
                                                                        }
                                                                        label="Correct"
                                                                    />
                                                                </Grid>
                                                                <Grid item xs={1}>
                                                                    <IconButton
                                                                        color="error"
                                                                        size="small"
                                                                        onClick={() => handleRemoveQuestionArrayOption(question.id, optionIndex)}
                                                                        disabled={question.mcqOptions.length <= 1}
                                                                    >
                                                                        <ClearIcon fontSize="small" />
                                                                    </IconButton>
                                                                </Grid>
                                                            </Grid>
                                                        </Box>
                                                    ))}

                                                    {/* Add Option Button */}
                                                    <Button
                                                        variant="outlined"
                                                        size="small"
                                                        onClick={() => handleAddQuestionArrayOption(question.id)}
                                                        sx={{ mt: 1 }}
                                                    >
                                                        Add Option
                                                    </Button>
                                                </Grid>
                                            </Grid>
                                        </Card>
                                    ))}

                                    {/* Add New Question Button */}
                                    <Button
                                        variant="contained"
                                        color="primary"
                                        onClick={handleAddNewQuestion}
                                        sx={{ mt: 2 }}
                                        startIcon={<AddIcon />}
                                    >
                                        Add New Question
                                    </Button>
                                </Box>
                            </DialogContent>
        
                                                    
                                                 
                                                            <Grid item xs={12} sm={12} sx={{ marginBottom: '15px', paddingRight: '18px' }}>
                                                                <Typography variant="subtitle1">Explanation</Typography>
                                                                <ReactQuill
                                                                    readOnly={submitted}
                                                                    theme="snow"
                                                                    id="explanation"
                                                                    name="explanation"
                                                                    modules={modules}
                                                                    formats={formats}
                                                                    defaultValue={explanation}
                                                                    onChange={(content) => {
                                                                        clatExplanation(content);
                                                                        setQuestionError({
                                                                            explanation: ''
                                                                        });
                                                                    }}
                                                                />
                                                                {questionError && questionError.explanation && <FormHelperText error  >{questionError?.explanation}</FormHelperText >}
                                                            </Grid>
                                                    
        
        
        
        
                                              
        
        
                                            {submitted ? <Grid item xs={12}>
                                                <LoadingButton
                                                    id="subMitButton"
                                                    type="submit"
                                                    variant="contained"
                                                    color="primary"
                                                    disabled
                                                    loading={loadingnew}
                                                    fullWidth
                                                >
                                                    submitted
                                                </LoadingButton>
                                            </Grid> :
                                                <Grid item xs={12}>
                                                    <LoadingButton
                                                        id="subMitButton"
                                                        type="submit"
                                                        variant="contained"
                                                        color="primary"
                                                        // onClick={CreateQuestion}
                                                        onClick={SubmitQuestion}
                                                        loading={loadingnew}
                                                        fullWidth
                                                    >
                                                        Submit Question
                                                    </LoadingButton>
                                                </Grid>}
                                        </Grid>
                                    </Box>
        
                                )}
                            </DialogContent>

                         
                       

                            <DialogActions>
                                <Button id='modulepopSubmits' onClick={handleModuleSubmitCreate} color="secondary">
                                    Submit
                                </Button>
                                <Button id='modulepopCancel' onClick={handleClose} color="primary">
                                    Cancel
                                </Button>
                            </DialogActions>
        
        
                        </Dialog>
            <Page title="Edit CLAT Assessment">
                <PageHeader variant="h4" style={{fontSize: {md: '24px', sm: '14px', xs: '12px'}, fontWeight: 'bold'}} pageTitle="Edit CLAT Assessment" submodule="submodule" />
                <Grid container spacing={2} className='GACognitivesection' sx={{ mb: 2, padding: '15px 20px' }}>
                    <Grid item xs={12} sm={4} sx={{ marginBottom: '18px', paddingRight: '18px' }}>
                        <TextField
                            variant="outlined"
                            inputProps={{ maxLength: 50 }}
                            fullWidth
                            id="addname"
                            label="CLAT Name *"
                            type="search"
                            value={name}
                            onChange={(e) => {
                                setName(e.target.value);
                            }}
                            sx={{
                                borderRadius: 1,
                                height: 36,
                                '& .MuiInputBase-input': {
                                    height: '36px',
                                    padding: '0 14px',
                                },
                            }}
                        />
                    </Grid>

                    <Grid item xs={12} sm={4} sx={{ marginBottom: '18px', paddingRight: '18px' }}>
                        <FormControl fullWidth>
                            <InputLabel id="complexity-label">Complexity Level *</InputLabel>
                            <Select
                                labelId="complexity-label"
                                id="complexity"
                                value={complexity}
                                label="Complexity Level *"
                                onChange={(e) => {
                                    setComplexity(e.target.value);
                                }}
                                sx={{ height: 36 }}
                            >
                                <MenuItem value="beginner">Beginner</MenuItem>
                                <MenuItem value="intermediate">Intermediate</MenuItem>
                                <MenuItem value="advanced">Advanced</MenuItem>
                            </Select>
                        </FormControl>
                    </Grid>

                <Grid item xs={12} sm={4} sx={{ marginBottom: '15px', paddingRight: '18px' }}>
                        {/* <InputLabel id="complexity-level-label">Sat Time</InputLabel> */}

                        <Grid container className='AssessmentTime' spacing={2}>

                            <Grid item xs={4}>
                                <FormControl fullWidth>
                                    <InputLabel>Hours</InputLabel>
                                    <Select
                                        name="hours"
                                        value={time.hours}
                                        // onChange={handleChange}
                                        disabled
                                        // label="Hours"
                                        className="dropHours"
                                        MenuProps={{
                                            PaperProps: {
                                                style: {
                                                    maxHeight: 158,
                                                }
                                            }
                                        }}
                                    >
                                        {/* {hoursArray.map((hour) => (
                                            <MenuItem key={hour} value={hour}>{hour}</MenuItem>
                                        ))} */}
                                    </Select>
                                </FormControl>
                            </Grid>
                            <Grid item xs={4}>
                                <FormControl fullWidth>
                                    <InputLabel>Minutes</InputLabel>
                                    <Select
                                        name="minutes"
                                        value={time.minutes}
                                        // onChange={handleChange}
                                        disabled
                                        // label="Minutes"
                                        className="dropHours"
                                        MenuProps={{
                                            PaperProps: {
                                                style: {
                                                    maxHeight: 158,
                                                }
                                            }
                                        }}
                                    >
                                        {/* {minutesArray.map((minute) => (
                                            <MenuItem key={minute} value={minute}>{minute}</MenuItem>
                                        ))} */}
                                    </Select>
                                </FormControl>
                            </Grid>

                            <Grid item xs={4}>
                                <FormControl fullWidth>
                                    <InputLabel>Seconds</InputLabel>
                                    <Select
                                        name="seconds"
                                        value={time.seconds}
                                        disabled
                                        // onChange={handleChange}
                                        // label="Seconds"
                                        className="dropHours"
                                        MenuProps={{
                                            PaperProps: {
                                                style: {
                                                    maxHeight: 228,
                                                }
                                            }
                                        }}
                                    >
                                        {/* {secondsArray.map((second) => (
                                            <MenuItem key={second} value={second}>{second}</MenuItem>
                                        ))} */}
                                    </Select>
                                </FormControl>
                            </Grid>
                        </Grid>
                    </Grid>

                      <Grid className="unique" item xs={12} sm={6} sx={{ marginBottom: '0px', paddingRight: '18px' }}>
                                       {thumbPreview === null ? (
                                           <FormControl style={{ height: '100%' }}
                                               required
                                               component="fieldset"
                                               color="primary"
                                               variant="outlined"
                                               fullWidth
                                               name="thumbImage"
                                           >
                                               <Typography variant="subtitle1">Thumb Image* <span style={{
                                                   fontSize: '12px',
                                                   float: 'inline-end', paddingBottom: '0', marginBottom: '0', position: 'relative', top: '5px'
                                               }}>required resolution (360X200)</span></Typography>
                                               <DropzoneArea className="dropTextArea"
                                                   acceptedFiles={['image/jpeg', 'image/png', 'image/bmp']}
                                                   showPreviews={false}
                                                   dropzoneText="Drag and Drop Image or Browse File"
                                                   showPreviewsInDropzone={false}
                                                   maxFileSize={300000}
                                                   filesLimit={1}
                                                   showAlerts={false}
                                                   styles={{
                                                       height: '100%', minHeight: '100%',
                                                       display: 'flex',
                                                       flexDirection: 'column',
                                                       justifyContent: 'center'
                                                   }}
                                                   onChange={handleFileChange}
                                                   useChipsForPreview
                                                   previewGridProps={{ container: { spacing: 1, direction: 'row' } }}
                                                   showFileNamesInPreview
                                               />
                                               {ImageError && <FormHelperText error>{ImageError}</FormHelperText>}
                                           </FormControl>
               
               
                                       ) : (
                                           <div className={classes.imgPreviewRoot}>
                                               <Typography variant="subtitle1">Thumb Image</Typography>
                                          <Badge
                                    badgeContent={
                                        <CloseIcon id='ThumbPreview'
                                            className={classes.badgeAlign}
                                            onClick={() => {
                                                setThumbPreview(null);
                                                setThumbImage(null);
                                            }}
                                        />
                                    }
                                >
                                    <Avatar
                                        variant="rounded"
                                        src={thumbPreview}
                                        style={{ minHeight: '150px !important' }}
                                        className={thumbPreview !== null && classes.fileImgSIze}
                                    />
                                </Badge>
                                           </div>
                                       )}
                                   </Grid>

                    <Grid item xs={12} sm={6} sx={{ marginBottom: '18px' }}>
                        <Typography>CLAT Description *</Typography>
                        <ReactQuill
                                                   theme="snow"
                                                   id='questionTxt'
                                                   name="question"
                                                   value={editorValue}
                                                   onChange={(content) => {
                                                       if (content.length > 255) {
                                                           setEditorValue(content.slice(0, 255));
                                                       } else {
                                                           setEditorValue(content);
                                                       }
                                                  
                                                       if (content.trim() !== '' && content !== '<p><br></p>') {
                                                           setDescriptionError('');
                                                       }
                                                   }}
                                                   onPaste={(e) => {
                                                       e.preventDefault();
                                                       const clipboardText = e.clipboardData.getData('text').slice(0, 255);
                                                    //    handleChangeDescription(clipboardText);
                                                   }}
                                                   // fullWidth
                                                   style={{ height: '150px', marginBottom: '30px' }}
                                               />
                        {descriptionError && <FormHelperText error>{descriptionError}</FormHelperText>}
                    </Grid>

                 
                                     <Grid item xs={6}>
                                         <Typography gutterBottom variant="subtitle1">
                                             Short Description
                                         </Typography>
                                         <Box>
                                             <textarea
                                                 id="shortdescription"
                                                 value={shortdescription}
                                                 onChange={(event) => setShortDescription(event.target.value)}
                                                 placeholder="Enter shortdescription"
                                                 rows="5"
                                                 cols="40"
                                                 maxLength={250}
                                                 style={{
                                                     width: "100%",
                                                     height: "150px",
                                                     padding: "10px",
                                                     fontSize: "16px",
                                                     border: "1px solid #ccc",
                                                     borderRadius: "4px",
                                                     outline: "none",
                                                     resize: "both",
                                                     transition: "border-color 0.3s",
                                                 }}
                                             />
                                         </Box>
                                     </Grid>

         
                    {/* Action Buttons */}
                    <Grid item xs={12} sx={{ mt: 3, display: 'flex', gap: 2, justifyContent: 'flex-end' }}>
                        <Button
                            variant="contained"
                            onClick={handleUpdate}
                            loading={loading}
                            loadingPosition="start"
                        >
                            Update
                        </Button>
                          <Button id='AddModule' sx={{ marginRight: '15px', paddingBottom: '0px' }} variant="contained" color="primary" onClick={(data) => handleDialogOpen(data)}>
                           Add Module
                          </Button>
                    </Grid>

                    {errorMessage && (
                        <Grid item xs={12}>
                            <Alert severity="error" sx={{ mt: 2 }}>
                                {errorMessage}
                            </Alert>
                        </Grid>
                    )}
                </Grid>

                <SnackBar
                    open={openSnackbar}
                    snackbarTitle={validationError || snackbarTitle}
                    close={() => setOpenSnackbar(false)}
                    severity={validationError ? "error" : "success"}
                />
            </Page>
        </>
    );
};

export default CLATTestUpdate;




