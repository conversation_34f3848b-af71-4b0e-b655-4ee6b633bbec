/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable no-unused-vars */
import React, { useEffect, useState } from 'react';

import { Typo<PERSON>, Breadcrumbs, LinearProgress, Chip,TextField,Grid, Box,Table,TableBody,Button,InputAdornment,InputLabel,Select,MenuItem, TableCell,TableContainer,TableHead,TableRow,TablePagination,FormControl, } from '@mui/material';
import { Link as RouterLink } from 'react-router-dom';
import MUIDataTable from 'mui-datatables';
import { useTranslation } from 'react-i18next';
import moment from 'moment';
import { makeStyles } from '@mui/styles';
import CheckCircleRoundedIcon from '@mui/icons-material/CheckCircleRounded';
import CancelRoundedIcon from '@mui/icons-material/CancelRounded';
import SearchIcon from '@mui/icons-material/Search';
import adminServices from '../../../services/adminServices';
import Page from '../../../components/Page';
import PageHeader from '../../../components/PageHeader';
import reports from '../../../services/report/reports';

const SATReport = () => {
    const classes = useStyles();
    const { t } = useTranslation('report');
    const [data, setData] = useState([]);
    const [loading, setLoading] = useState(true);
    const [page, setPage] = useState(0);
    const [rowsPerPage, setRowsPerPage] = useState(10);
    const [searchedDetails, setSearchedDetails] = useState('')
    const [sortOption, setSortOption] = useState("date_started");
    const [SATData, setSATData] = useState([]);
    const [totalCount, setTotalCount] = useState(0);
    const [SATLoading, setSATLoading] = useState(false)


 useEffect(() =>{
     getDashBoardChart()
   },[searchedDetails,page,rowsPerPage,sortOption])


const getDashBoardChart = async () => {
    setSATLoading(true)
    try {
      const result = await adminServices.getSATDashBoard(
        searchedDetails,
        page,
        rowsPerPage,
        sortOption
      );
    setSATData(result.data?.rows || []);
    setTotalCount(result.data?.total||0)
    if (result.ok) {
      setSATLoading(false);
    }
    setSATLoading(false)

  } catch (error) {
    setSATLoading(false)
    console.log(error);
  }
};
const getScore = (score, totalScore) => {
  if(totalScore === null) return "NA"

  if (!totalScore || totalScore === 0) return 0;
  return Math.round((score / totalScore) * 1600);
};
  const Breadcrumb = () => (
    <Breadcrumbs aria-label="breadcrumb" separator="›">
      <Typography
        sx={{ textDecoration: 'none' }}
        variant="body2"
        color="primary"
        component={RouterLink}
        to="/app/dashboard"
      >
        {'dashboard'}
      </Typography>
      <Typography variant="body2" color="textPrimary">
        {'SAT User Report '}
      </Typography>
    </Breadcrumbs>
  );

  return (
    <Page title="SAT-user-report">
      <PageHeader pageTitle={'SAT-report'} breadcrumbs={<Breadcrumb />} />
        <Grid item xs={12}>
                <div style={{ border: '1px solid #80808038', borderRadius: '4px', padding: '16px' }}>
                  <Typography variant="h5" sx={{ fontWeight: '550', mb: 2 }}>
                    {'SAT User Data'}
                  </Typography>

                  <Grid container spacing={2} justifyContent="space-between" alignItems="center" sx={{ mb: 2 }}>
                    <Grid item>
                      <TextField
                        variant="outlined"
                        placeholder="Search By Assessment or Name "
                        id="searchassessment"
                        size="small"
                        sx={{ width: '350px' }}
                        value={searchedDetails}
                        onChange={(e) => setSearchedDetails(e.target.value)}
                        InputProps={{
                          startAdornment: (
                            <InputAdornment position="start">
                              <SearchIcon id="searchAssessmentIconbtn" />
                            </InputAdornment>
                          ),
                        }}
                      />
                    </Grid>

                    <Grid item>
                      <FormControl size="small" sx={{ minWidth: 180 }}>
                        <InputLabel id="sort-label">Sort By</InputLabel>
                        <Select
                          labelId="sort-label"
                          value={sortOption}
                          label="Sort By"
                          onChange={(e) => setSortOption(e.target.value)}
                        >
                          <MenuItem value="date_started">Date Started</MenuItem>
                          <MenuItem value="attempted_count">Attempts</MenuItem>
                          <MenuItem value="first_name">First Name</MenuItem>
                        </Select>
                      </FormControl>
                    </Grid>
                  </Grid>

                  {/* Table */}
                  <TableContainer>
                    <Table>
                      <TableHead>
                        <TableRow>
                          <TableCell><strong>First Name</strong></TableCell>
                          <TableCell><strong>Email</strong></TableCell>
                          <TableCell><strong>Assessment Name</strong></TableCell>
                          <TableCell><strong>Date Started</strong></TableCell>
                          <TableCell><strong>No.of Attempts</strong></TableCell>
                          <TableCell><strong>Highest Score</strong></TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {SATData && SATData.length > 0 &&!SATLoading? (
                          SATData.map((row) => (
                            <TableRow key={row.related_assessment_id} sx={{ '&:hover': { backgroundColor: '#f1f1f1' } }}>
                             <TableCell>{row.first_name}</TableCell>
                             <TableCell>{row.email}</TableCell>
                              <TableCell>{row.name}</TableCell>
                              <TableCell>{moment(row.date_started).format("DD/MM/YYYY")}</TableCell>
                              <TableCell>{row.max_attempted_count}</TableCell>
                              <TableCell>{getScore(row.score,row.total_score)}</TableCell>
                            </TableRow>
                          ))
                        ) : (
                          <TableRow>
                            <TableCell colSpan={6} align="center">
                              {!loading && "No Data Found"}
                            </TableCell>
                          </TableRow>
                        )}
                      </TableBody>
                    </Table>
                  </TableContainer>

                  <TablePagination
                    rowsPerPageOptions={[5, 10, 25]}
                    component="div"
                    count={totalCount}
                    rowsPerPage={rowsPerPage}
                    page={page}
                    onPageChange={(event, newPage) => setPage(newPage)}
                    onRowsPerPageChange={(event) => {
                      setRowsPerPage(parseInt(event.target.value, 10));
                      setPage(0);
                    }}
                  />
                </div>
              </Grid>
    </Page>
  );
};

export default SATReport;

const useStyles = makeStyles(() => ({
  dateWidth: {
    width: '110px',
    fontSize: '0.95rem',
  },
  customerLink: {
    color: '#000',
    textDecoration: 'none',
    '&:hover': {
      textDecoration: 'underline',
      color: '#000',
    },
  },
  cancelIcon: {
    color: 'grey',
  },
  checkIcon: {
    color: 'rgb(0, 182, 115)',
  },
}));
