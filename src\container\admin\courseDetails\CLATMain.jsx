/* eslint-disable no-unused-vars */
/* eslint-disable react-hooks/exhaustive-deps */
import React, { useState, useEffect } from "react";
import {
    Grid, Button, IconButton, TextField,
    InputAdornment, Table, TableBody, TableCell, TableContainer, Container,FormHelperText,
    TableHead, TableRow, Paper, TablePagination, Switch, Tooltip, DialogActions, DialogContent, DialogTitle, Dialog, Tabs, Tab,
} from '@mui/material';

import DOMPurify from 'dompurify';
import { useTranslation } from 'react-i18next';
import moment from 'moment'
import { useNavigate, useLocation } from 'react-router-dom';
import EditIcon from '@mui/icons-material/Edit';
import BookmarkIcon from '@mui/icons-material/Bookmark';
import DeleteIcon from '@mui/icons-material/Delete';
import SearchIcon from '@mui/icons-material/Search';
import Page from '../../../components/Page'
import PageHeader from '../../../components/PageHeader';
import DeleteAlert from '../../../components/modal/DeleteModal';
import SnackBar from '../../../components/snackbar/snackbar';
import adminServices from '../../../services/adminServices';
import CreateMcqQuestionModel from "./CreateMcqQuestionModel";
import EditQuestionModule from "./EditMcqQuestionsModule";



const CLAT = () => {
    const [page, setPage] = useState(0);
    const [pagePassage, setPagePassage] = useState(0);
    const [rowsPerPage, setRowsPerPage] = useState(10);
    const [rowsPerPagePassage, setRowsPerPagePassage] = useState(10);
    const [openCreateMcq, setOpenCreateMcq] = useState(false);
    const [openEditMcq, setOpenEditMcq] = useState(false);
    const [snackbarTitle, setSnackbarTitle] = useState('');
    const [openSnackbar, setOpenSnackbar] = useState(false);
    const [searchedDetails, setSearchedDetails] = useState('');
    const [satDetails, setSatDetails] = useState([]);
    const [deleteAlert, setDeleteAlert] = useState(false);
    const [deleteModalTitle, setDeleteModalTitle] = useState('');
    const [deleteId, setDeleteId] = useState('')
    const [deleteType, setDeleteType] = useState('')
    const [totalCount, setTotalCount] = useState('')
    const [totalCountPassage, setTotalCountPassage] = useState('')
    
    const [openUserMessage, setOpenUserMessage] = useState(false)
    const [userMessageData, setUserMessageData] = useState('')
    const [userMessageFromDateAndTime, setUserMessageFromDateAndTime] = useState('')
    const [userMessageToDateAndTime, setUserMessageToDateAndTime] = useState('')
    const [currentToggleId, setCurrentToggleId] = useState('')
    const [currentToggleValue, setCurrentToggleValue] = useState('')
    const location = useLocation();
    const queryParams = new URLSearchParams(location.search);
    const tabFromQuery = queryParams.get('tab');
    const [tabValue, setTabValue] = useState(location.state?.comingfrom ? location.state?.comingfrom : 0);
    const [NeetAssessmentData, setNeetAssessmentData] = useState([]);
    const [loading, setLoading] = useState(false);
    const [questionList, setQuestionList] = useState([]);
    const [maxCount, setMaxCount] = useState(0);
    const [searchText, setSearchText] = useState('');
    const [keyObjects, setKeyObjects] = useState([]);
    const [newData, setNewData] = React.useState('');
    const [deleteOpen, setDeleteOpen] = React.useState(false);
    const [openPassage, setOpenPassage] = useState(false);
    const [openEditPassage, setOpenEditPassage] = useState(false);
    const [passage, setPassage] = useState('');
    const [passageError,setPassageError] = useState('');
    const [editPassage, setEditPassage] = useState('');
    const [passageDetails,setPassageDetails] = useState();
    const [passageEditId,setPassageEditId] = useState(null);
    const [deletePassageById,setDeletePassageById] = useState('');




    const handleCreatePassage = () => {
        // setPassage('');
        // setPassageDetails(null);
        setOpenPassage(true);
    };
    
    const handleEditPassage = () => {
        setOpenPassage(true);
        // setPassageDetails(passageDetails);
        // setPassage(passageDetails?.passage_text)

    };




    const handleChange = (event, newValue) => {
        setTabValue(newValue);
        setDeleteOpen(false);
        setDeleteAlert(false);
    };
    const { t } = useTranslation('translation');
    const navigate = useNavigate();

    const handleChangePage = (event, newPage) => {
        setPage(newPage);
    };

    const handleChangeRowsPerPage = (event) => {
        setRowsPerPage(parseInt(event.target.value, 10));
        setPage(0);
    };

     const handleChangePagePassage = (event, newPage) => {
        setPagePassage(newPage);
    };


    const handleChangeRowsPerPagePassage = (event) => {
        setRowsPerPagePassage(parseInt(event.target.value, 10));
        setPagePassage(0);
    };


    

    useEffect(() => {
        getNEETAssessmentDetails();

    }, [page, rowsPerPage, searchedDetails, passage, tabValue, searchText])


    useEffect(() => {
        getPassageDetails();

    }, [pagePassage, rowsPerPagePassage])


    const getSATDetails = async () => {
        const result = await adminServices.getNEETAssessmentAdmin(searchedDetails, page, rowsPerPage,);
        if (result.ok) {
            setSatDetails(result.data.assessments)
            setTotalCount(result.data.pagination)
        }

    }
    const getNEETAssessmentDetails = async () => {
        const result = await adminServices.getNEETAssessmentData(searchedDetails, page, rowsPerPage,);
        console.log('getNEETAssessmentDetails',result);
        
        if (result.ok) {
            setNeetAssessmentData(result.data.data)
            setTotalCount(result.data.pagination)
        }

    }
    const getPassageDetails = async () => {
        try {
            const response = await adminServices.getPassage(pagePassage, rowsPerPagePassage);
            console.log('getPassageDetails',response);
            if (response.ok) {
                setPassageDetails(response.data.data);
                setTotalCountPassage(response.data.total)
            }
            

        } catch (error) {
            console.log("error getting result", error);
            
        }
        

    }

    
    const Validation = () => {
         if(!passage){
            setPassageError("Passage cannot be empty");
            return false
        }
        
        return true

    }

    const handleSubmitPassage = async() => {

        const Valid = Validation();

       if(Valid){
           const body = {
               name:passage,
           }
    
           try {
               const response = await adminServices.createPassage(body);
               if(response.ok) {
                   setSnackbarTitle('Passage created successfully');
                   setOpenPassage(false);
                   setPassage('');
                   getPassageDetails();

               }
           } catch (error) {
               console.error("An error occurred:", error); 
               
           }
       }

    }

    // const handleUpdatePassage = async () => {
    //     const body = {
    //         passage_text: passage,
    //     }
    //     try {
    //         const passageId = passageDetails?.id;
    //         console.log(body, "bodybody");

    //         if (passageId) {
    //             const response = await adminServices.updatePassage(passageId, body);
    //             if (response.ok) {
    //                 setSnackbarTitle('Passage updated successfully');
    //                 setOpenPassage(false);

    //                 getPassageDetails();


    //             }
    //         }
    //     } catch (error) {
    //         console.error("An error occurred:", error);

    //     }
    // }

    
 


    const handleToggleButton = (id, value, type) => {
        if (value === false) {
            setOpenUserMessage(true);
            setCurrentToggleId(id);
            setCurrentToggleValue(value);
            return;
        }
        callUpdateStatusAPI(id, value);
    };

    const callUpdateStatusAPI = async (id, value, userData) => {
        try {
            const result = await adminServices.UpdateGeneralAssessmentStatus(id, value, userData, 'Status');
            if (result.ok) {
                setSnackbarTitle(t('Assessment status updated successfully'));
                setOpenSnackbar(true);
                getSATDetails();
            }
        } catch (error) {
            console.log(error, 'error');
        }
    };

    const handleSubmitUserMessage = () => {
        const userData = {
            message: userMessageData,
            fromDate: userMessageFromDateAndTime,
            toDate: userMessageToDateAndTime,
        };

        callUpdateStatusAPI(currentToggleId, currentToggleValue, userData);

        setOpenUserMessage(false);
        setUserMessageData('');
        setUserMessageFromDateAndTime('');
        setUserMessageToDateAndTime('');
    };


    const handleCreateCLAT = () => {
        navigate("/app/AddCLAT")
    }
    const handleToggleButtonMaintenance = async (id, value, type) => {

        try {
            const result = await adminServices.UpdateGeneralAssessmentMaintenance(id, value, type);
            if (result.ok) {
                setSnackbarTitle(t('CLAT Assessment status updated successfully'));
                setOpenSnackbar(true);
                getSATDetails();
            }
        } catch (error) {
            console.log(error, 'error');
        }
    }

    const handleDelete = (data) => {
        setDeleteAlert(true);
        setDeleteId(data.id);
        setDeleteType(data?.question_type);
        setDeleteModalTitle('Are you sure you want delete this CLAT Assessment?');
    }

    const handleEditAssessment = (data) => {
        navigate('/app/EditCLAT', { state: data?.assessment?.id })
    }

    const handleDeleteNEET = async () => {
        try {
            const response = await adminServices.deleteNEETAssessment(deleteId);
            if (response.ok) {
                setSnackbarTitle('CLAT Assessment deleted successfully');
                setOpenSnackbar(true);
                getSATDetails();
                getAllQuestions();
                getNEETAssessmentDetails();
                setDeleteAlert(false)
                setDeleteId('')
                setDeleteType('')
                setDeleteModalTitle("");
            }
        } catch (error) {
            console.log(error);
        }
    }

    const handleDeletePassageById = async() => {
        try {
            const response = await adminServices.deletePassage(deletePassageById);
            if (response.ok) {
                setDeleteOpen(false)
                setSnackbarTitle('CLAT Passage deleted successfully');
                setOpenSnackbar(true);
                getPassageDetails();
                setDeleteAlert(false);
                setDeletePassageById('');
            }
        } catch (error) {
            console.log(error);
        }
    }
    const getAllQuestions = async () => {
        setLoading(true);
        const result = await adminServices.getAllNEETQuestion(rowsPerPage, page, searchText);
        if (result.ok) {
            console.log(result);
            setMaxCount(result.data.total)
            const sortedQuestions = result.data.newQuestions.sort((a, b) =>
                new Date(b.created_date) - new Date(a.created_date)
            );
            setQuestionList(sortedQuestions);
            setLoading(false);
        }
        setLoading(false);
    };

    const handleCreateQuestion = async (object, values) => {
        setLoading(true);
        try {
            const keysData = new FormData();
            keysData.append('question', values.question);
            keysData.append('explanation', values.explanation);
            keysData.append('level', values.level);
            keysData.append('questionType', values.questionType);
            if (values.selectedSkills) {
                keysData.append('cognitive_skills', values.selectedSkills);
            }
            if (values.points !== null) {
                keysData.append('points', values.points);
            }
            keysData.append('courseCategory', values.courseCategory ? values.courseCategory : null);
            keysData.append('Tag', JSON.stringify(values.keyword ? values.keyword : null));
            keysData.append('questionTag', JSON.stringify(values.studyMaterials || []));
            if (values.questionType === "IQ") {
                const updatedObject = {
                    ...object,
                    keyType: values.keyType
                };
                keysData.append('keyObj', JSON.stringify(updatedObject));
            }
            else if (values.questionType === "MCQ" || values.questionType === "screenLevelMcq" || values.questionType === 'Neet') {
                const mcqdata = {
                    question: values.question,
                    mcqOptions: values.mcqOptions.map(option => option.option),
                    correctAnswer: values.mcqOptions.map(option => option.isCorrect),
                };
                keysData.append('mcqData', JSON.stringify(mcqdata));
            }
            const response = await adminServices.createQuestions(keysData);
            if (response.ok) {
                const newQuestion = {
                    question_text: values.question,
                    question_type: values.questionType,
                    is_active: true,
                    created_date: new Date().toISOString(),
                    mcqOptions: values.mcqOptions,
                };
                setQuestionList(prevList => [newQuestion, ...prevList]);
                setOpenSnackbar(true);
                setSnackbarTitle(response.data.message);
                setOpenCreateMcq(false);
                setLoading(false);
                getAllQuestions()

            } else {
                console.error("Error:", response);
                setLoading(false);
            }
            getAllQuestions();
        } catch (error) {
            console.error("An error occurred:", error);
        }
    };

    const handleSubmitForm = async (values) => {
        const array = keyObjects;
        const object = {
            windows: {
                keyCode: values.combinedWindowKeys,
                keyName: values.combinedWindowKeyName,
            },
            mac: {
                keyCode: values.combinedMacKeys,
                keyName: values.combinedMacKeyName,
            },
            positionIndex: array.length,
        };
        array.push(object);
        setKeyObjects(array);
        handleCreateQuestion(object, values)

    }
    const handleEditCallBack = (values) => {
        const array = keyObjects;
        const object = {
            windows: {
                keyCode: values.combinedWindowKeys,
                keyName: values.combinedWindowKeyName,
            },
            mac: {
                keyCode: values.combinedMacKeys,
                keyName: values.combinedMacKeyName,
            },
            positionIndex: array.length,
        };
        array.push(object);
        handleEditCallBack1(object, values);
    }
    const handleEditCallBack1 = async (object, values) => {
        setLoading(true)
        const mergedArray = values.mcqOptions?.map((option, index) => ({
            option,
            isCorrect: values.curectAnswer[index] || false,
        }));

        try {
            const requestData = {
                question: values.question,
                level: values.level,
                questionType: values.questionType,
                explanation: values.explanation,
                points: values.points !== null ? values.points : undefined,
                courseCategory: values.courseCategory || undefined,
                Tag: values.courseCategory ? JSON.stringify(values.keyword) : JSON.stringify(values.keyword),
                cognitive_skills: values.selectedSkills

            };
            if (values.selectedSkills) {
                requestData.cognitive_skills = values.selectedSkills
            }
            if (values.questionType === "IQ") {
                const updatedObject = {
                    ...object,
                    keyType: values.keyType,
                };
                requestData.keyObj = JSON.stringify(updatedObject);
            }
            else if (values.questionType === "MCQ" || values.questionType === "screenLevelMcq" || values.questionType === 'Neet') {
                requestData.mcqData = {
                    question: values.question,
                    mcqOptions: mergedArray.map(option => option.option),
                    correctAnswer: mergedArray.map(option => option.isCorrect),
                };
            }
            const questionId = newData.id;
            const response = await adminServices.updateQuestion(questionId, requestData);
            if (response.ok) {
                setOpenSnackbar(true);
                setSnackbarTitle(response.data.message);
                setOpenEditMcq(false);
                setLoading(false);
                getAllQuestions();
            } else {
                console.error("Error:", response);
                setLoading(false);
            }
        } catch (error) {
            console.error("An error occurred:", error);
        }
    };
    const handleInputChange = (event) => {
        const newSearch = event.target.value;
        setSearchText(newSearch);
    };
    const handleEdit = (values) => {
        setNewData(values)
        setOpenPassage(true)
    }


    const handleDeleteNeetQuestion = async () => {
        try {
            const response = await adminServices.deleteSATDetails(deleteId, deleteType);
            setDeleteOpen(false)
            if (response.ok) {
                if (response.data.message) {
                    setSnackbarTitle(response.data.message);
                }
                else {
                    setSnackbarTitle('CLAT Question deleted successfully');
                }
                getAllQuestions();
                setDeleteOpen(false);
                setOpenSnackbar(true);
                setDeleteId('')
                setDeleteType('')
                setDeleteModalTitle("");
            }
        } catch (error) {
            console.log(error);
        }
    }

    const handleDeletOpen = (data) => {
        setDeleteOpen(true);
        setDeletePassageById(data.id)
    }
    return (
        <Page title="CLAT Assessment">
            <PageHeader pageTitle="CLAT Assessment" submodule="submodule" />
            <>
                <Tabs value={tabValue} onChange={handleChange} aria-label="CLAT tabs">
                    <Tab label="Passage" />
                    <Tab label="CLAT" />
                </Tabs>
                {tabValue === 0 &&
                    <>
                        <Container maxWidth={false} sx={{ padding: '0 !important' }}>
                            <Grid container spacing={2}>
                                <Grid align="end" item xs={12}>
                                    <TextField
                                        variant="outlined"
                                        placeholder="Search questions"
                                        size="small"
                                        sx={{ width: '250px', height: '40px' }}
                                        InputProps={{
                                            startAdornment: (
                                                <InputAdornment position="start">
                                                    <SearchIcon id="searchQuestionIconbtn" />
                                                </InputAdornment>
                                            ),
                                            sx: { height: '100%' },
                                        }}
                                        value={searchText}
                                        onChange={handleInputChange}
                                    />
                                    <Button
                                        style={{ marginLeft: '15px', height: '40px' }}
                                        id="createQuestionButton"
                                        variant="contained"
                                        color="primary"
                                        onClick={handleCreatePassage}
                                    >
                                        Create Passage
                                    </Button>

                                </Grid>
                                <TableContainer component={Paper}>
                                    <Table>
                                        <TableHead>
                                            <TableRow>
                                                <TableCell>Name</TableCell>
                                                {/* <TableCell>Type</TableCell>
                                                <TableCell>Status</TableCell>
                                                <TableCell>Author</TableCell>
                                                <TableCell>Date</TableCell> */}
                                                <TableCell>Action</TableCell>
                                            </TableRow>
                                        </TableHead>

                                        <TableBody>
                                            {loading && (
                                                <TableRow>
                                                    <TableCell colSpan={5} style={{ textAlign: 'center' }}>
                                                        Loading...
                                                    </TableCell>
                                                </TableRow>
                                            )}

                                            {!loading && passageDetails?.length === 0 && (
                                                <TableRow>
                                                    <TableCell colSpan={5} style={{ textAlign: 'center' }}>
                                                        Sorry, there is no matching data to display
                                                    </TableCell>
                                                </TableRow>
                                            )}
                                            {!loading &&  passageDetails?.length > 0  && passageDetails.map((row, index) => (
                                                <TableRow key={index}>
                                                    {/* <TableCell dangerouslySetInnerHTML={{ __html: DOMPurify.sanitize(row.passage_text) }} /> */}
                                                    <TableCell>{row.passage_text}</TableCell>
                                                    {/* <TableCell style={{ color: row.is_active ? 'green' : 'red' }}>
                                                        {row.is_active ? 'Active' : 'Inactive'}
                                                    </TableCell> */}
                                                    {/* <TableCell>{row?.firstname} {row?.lastname}</TableCell> */}
                                                    {/* <TableCell>{moment(row.created_date).isValid() ? moment(row.created_date).format('MM-DD-YYYY (h:mm A)') : 'Invalid Date'}
                                                    </TableCell> */}
                                                    <TableCell>
                                                        <IconButton id={`edit${row.id}`} color="primary" onClick={() => handleEditPassage(row)}>
                                                            <EditIcon />
                                                        </IconButton>
                                                        <IconButton id={`delete${row.id}`} color="secondary" onClick={() => handleDeletOpen(row)}>
                                                                <DeleteIcon />
                                                            </IconButton>
                                                        {/* {row?.is_linked_to_assessment === false ?
                                                            <IconButton id={`delete${row.id}`} color="secondary" onClick={() => handleDeletOpen(row)}>
                                                                <DeleteIcon />
                                                            </IconButton>
                                                            :
                                                            <>
                                                                <Tooltip title="This Passsage is a part of CLAT !" arrow>
                                                                    <span>
                                                                        <IconButton id={`delete${row.id}`} color="secondary" disabled>
                                                                            <DeleteIcon />
                                                                        </IconButton>
                                                                    </span>
                                                                </Tooltip>
                                                            </>

                                                        } */}
                                                    </TableCell>
                                                </TableRow>
                                            ))}
                                        </TableBody>
                                    </Table>
                                </TableContainer>



                                <Grid item xs="12" sm="12" md="12" lg="12" xl="12">
                                    <div >
                                        <TablePagination
                                            component="div"
                                            count={totalCountPassage}
                                            page={pagePassage}
                                            onPageChange={handleChangePagePassage}
                                            rowsPerPage={rowsPerPagePassage}
                                            onRowsPerPageChange={handleChangeRowsPerPagePassage}
                                            rowsPerPageOptions={[5, 10, 15, 20, 25]}
                                            sx={{ marginTop: 2 }}
                                        />
                                    </div>
                                </Grid>
                            </Grid>
                        </Container>

                        <CreateMcqQuestionModel
                            open={openCreateMcq}
                            modelClose={() => setOpenCreateMcq(!openCreateMcq)}
                            title="Create Questions"
                            handleCreateMcq={handleSubmitForm}
                            loading={loading}
                            searchedDetails={searchedDetails}
                        />
                        <EditQuestionModule
                            open={openEditMcq}
                            modelClose={() => setOpenEditMcq(!openEditMcq)}
                            title="Edit Questions"
                            handleEditMcq={handleEditCallBack}
                            loading={loading}
                            searchedDetails={searchedDetails}
                            data={newData}
                        />
                    </>

                }


                {tabValue === 1 && <Grid container spacing={2}>
                    <Grid align="end" item xs={12}>
                        <TextField
                            variant="outlined"
                            placeholder="Search Name"
                            id="searchassessment"
                            size="small"
                            sx={{ width: '250px', height: '40px' }}
                            value={searchedDetails}
                            onChange={(e) => setSearchedDetails(e.target.value)}
                            InputProps={{
                                startAdornment: (
                                    <InputAdornment position="start">
                                        <SearchIcon id="searchAssessmentIconbtn" />
                                    </InputAdornment>
                                ),
                                sx: { height: '100%' },
                            }}
                        />
                        <Button
                            style={{ marginLeft: '15px', height: '40px' }}
                            id="createassessmentButtongeneral"
                            variant="contained"
                            color="primary"
                            onClick={handleCreateCLAT}
                        >
                            Add CLAT 
                        </Button>
                    </Grid>
                    <TableContainer component={Paper} sx={{ marginTop: 2 }}>
                        <Table sx={{ marginLeft: '15px', width: '98%' }}>
                            <TableHead>
                                <TableRow>
                                    {/* <TableCell sx={{ fontWeight: 'bold', backgroundColor: '#f5f5f5', paddingLeft: '8px !important' }}>Name</TableCell> */}
                                    <TableCell sx={{ fontWeight: 'bold', backgroundColor: '#f5f5f5' }}>Name</TableCell>

                                    {/* <TableCell sx={{ fontWeight: 'bold', backgroundColor: '#f5f5f5' }}>Live</TableCell> */}
                                    {/* <TableCell sx={{ fontWeight: 'bold', backgroundColor: '#f5f5f5' }}>Questions</TableCell> */}
                                    <TableCell sx={{ fontWeight: 'bold', backgroundColor: '#f5f5f5' }}>Level</TableCell>
                                    <TableCell sx={{ fontWeight: 'bold', backgroundColor: '#f5f5f5' }}>Status</TableCell>
                                    <TableCell sx={{ fontWeight: 'bold', backgroundColor: '#f5f5f5' }}>Maintenance</TableCell>
                                    <TableCell sx={{ fontWeight: 'bold', backgroundColor: '#f5f5f5' }}>Author</TableCell>
                                    <TableCell sx={{ fontWeight: 'bold', backgroundColor: '#f5f5f5' }}>Date</TableCell>
                                    <TableCell sx={{ fontWeight: 'bold', backgroundColor: '#f5f5f5' }}>Actions</TableCell>
                                </TableRow>
                            </TableHead>
                            <TableBody>
                                {/* {loading && (
                                    <TableRow>
                                        <TableCell colSpan={5} style={{ textAlign: 'center' }}>
                                            Loading...
                                        </TableCell>
                                    </TableRow>
                                )} */}

                                {satDetails && satDetails?.length > 0 && (
                                    satDetails?.map((row) => (
                                        <TableRow key={row?.assessment?.id} sx={{ '&:hover': { backgroundColor: '#f1f1f1' } }}>
                                            {/* <TableCell sx={{ padding: '10px' }}>{row?.assessment?.title}</TableCell> */}
                                            {/* <TableCell sx={{ marginLeft: '40px' }}>{row?.question_type}</TableCell> */}


                                            <TableCell sx={{ marginLeft: '40px' }}>{row?.assessment?.title}</TableCell>
                                            <TableCell sx={{ marginLeft: '40px' }}>{row?.assessment?.complexity_level}</TableCell>
                                            <TableCell sx={{ marginLeft: '40px' }}>
                                                <Switch id={`statusSwitch${row?.assessment?.id}`}
                                                    checked={row?.assessment.is_published === true}
                                                    sx={{
                                                        '& .MuiSwitch-switchBase': {
                                                            color: row?.assessment.is_published ? 'green' : 'default',
                                                        },
                                                        '& .Mui-checked': {
                                                            color: 'green',
                                                        },
                                                        '& .Mui-Track': {
                                                            backgroundColor: row?.assessment.is_published ? 'green' : 'default',
                                                        },
                                                    }}
                                                    onChange={() => {
                                                        const newValue = !row?.assessment.is_published;
                                                        handleToggleButton(row.assessment.id, newValue, 'Status');
                                                    }}
                                                />
                                            </TableCell>
                                            <TableCell sx={{ marginLeft: '40px' }}>
                                                <Switch id={`statusSwitch${row?.assessment?.id}`}
                                                    checked={row?.assessment.on_maintenance === true}
                                                    sx={{
                                                        '& .MuiSwitch-switchBase': {
                                                            color: row?.assessment.on_maintenance ? 'green' : 'default',
                                                        },
                                                        '& .Mui-checked': {
                                                            color: 'green',
                                                        },
                                                        '& .Mui-Track': {
                                                            backgroundColor: row?.assessment.on_maintenance ? 'green' : 'default',
                                                        },
                                                    }}
                                                    onChange={() => {
                                                        const newValue = !row?.assessment.on_maintenance;
                                                        handleToggleButtonMaintenance(row.assessment.id, newValue, 'Maintenance');
                                                    }}
                                                />
                                            </TableCell>
                                            <TableCell>{row?.assessment?.author_first_name}</TableCell>

                                            <TableCell>
                                                {moment(row.assessment?.created_date_time).format("DD/MM/YYYY")}
                                            </TableCell>
                                            <TableCell sx={{ padding: '10px' }}>
                                                <IconButton
                                                    disabled={row?.assessment.is_published === true}
                                                    id={`editsat${row?.assessment?.id}`}
                                                    onClick={() => handleEditAssessment(row)}
                                                    color="primary">
                                                    <EditIcon />
                                                </IconButton>
                                                <IconButton
                                                    disabled={row?.assessment.is_published === true}
                                                    id={`deletesat${row?.assessment?.id}`}
                                                    onClick={() => handleDelete(row?.assessment)}
                                                    color="primary">
                                                    <DeleteIcon />
                                                </IconButton>
                                            </TableCell>
                                        </TableRow>
                                    ))
                                )
                                    // : (
                                    //     <TableRow>
                                    //         <TableCell colSpan={8} style={{ textAlign: 'center' }}>
                                    //             {!loading && openCreateSat && openCreateSat?.length === 0 && "No Data Found"}
                                    //         </TableCell>
                                    //     </TableRow>
                                    // )
                                }
                            </TableBody>




                        </Table>
                    </TableContainer>

                    <TablePagination
                        component="div"
                        count={totalCount?.total_count}
                        page={page}
                        onPageChange={handleChangePage}
                        rowsPerPage={rowsPerPage}
                        onRowsPerPageChange={handleChangeRowsPerPage}
                        rowsPerPageOptions={[5, 10, 15, 20, 25]}
                        sx={{ marginTop: 2 }}
                    />
                    <DeleteAlert
                        open={deleteAlert}
                        title={deleteModalTitle}
                        confirm={handleDeleteNEET}
                        close={() => setDeleteAlert(false)}
                    />
                    <SnackBar open={openSnackbar} snackbarTitle={snackbarTitle} close={() => setOpenSnackbar(false)} />

                </Grid>}
                
                
            </>
            <Dialog open={openUserMessage} onClose={() => setOpenUserMessage(false)}>
                <DialogTitle>Provide Message and Date Range</DialogTitle>
                <DialogContent>
                    <TextField
                        label="Message"
                        value={userMessageData}
                        onChange={(e) => setUserMessageData(e.target.value)}
                        fullWidth
                        multiline
                        rows={3}
                        margin="normal"
                    />
                    <TextField
                        label="From Date"
                        type="datetime-local"
                        value={userMessageFromDateAndTime}
                        onChange={(e) => setUserMessageFromDateAndTime(e.target.value)}
                        fullWidth
                        margin="normal"
                        InputLabelProps={{ shrink: true }}
                    />
                    <TextField
                        label="To Date"
                        type="datetime-local"
                        value={userMessageToDateAndTime}
                        onChange={(e) => setUserMessageToDateAndTime(e.target.value)}
                        fullWidth
                        margin="normal"
                        InputLabelProps={{ shrink: true }}
                    />
                </DialogContent>
                <DialogActions>
                    <Button onClick={() => setOpenUserMessage(false)}>Cancel</Button>
                    <Button onClick={handleSubmitUserMessage} variant="contained" color="primary">
                        Submit
                    </Button>
                </DialogActions>
            </Dialog >

            <Dialog open={openPassage} onClose={() => setOpenPassage(false)}>
                <DialogTitle>Create Passage</DialogTitle>
                <DialogContent>
                    <TextField
                        label="Passage"
                        value={passage}
                        onChange={(e) => {
                            setPassage(e.target.value);
                            setPassageError('');
                        }}
                        fullWidth
                        multiline
                        rows={6}
                        margin="normal"
                        sx={{ minWidth: 500 }}
                    />
                    {passageError && <FormHelperText error>{passageError}</FormHelperText>}
                </DialogContent>
                <DialogActions>
                    <Button onClick={() => {setOpenPassage(false); setPassage('')}}>Cancel</Button>
                    <Button onClick={handleSubmitPassage} variant="contained" color="primary">
                        Submit Passage
                    </Button>
                </DialogActions>
            </Dialog>
            <Dialog open={openPassage} onClose={() => setOpenPassage(false)}>
                {/* <DialogTitle>{passageDetails.id ? "Update" : "Create"} Passage</DialogTitle> */}
                <DialogTitle>{passageEditId ? "Update" : "Create"} Passage</DialogTitle>
                <DialogContent>
                    <TextField
                        label="Passage"
                        value={passage}
                        onChange={(e) => {
                            setPassage(e.target.value);
                            setPassageError('');
                        }}
                        fullWidth
                        multiline
                        rows={6}
                        margin="normal"
                        sx={{ minWidth: 500 }}
                    />
                    {passageError && <FormHelperText error>{passageError}</FormHelperText>}
                </DialogContent>
                <DialogActions>
                    <Button onClick={() => {setOpenPassage(false); setPassage('')}}>Cancel</Button>
                    {/* <Button onClick={handleUpdatePassage} variant="contained" color="primary">
                    { passageDetails.id ? "Update" : "Submit" } Passage */}
                    <Button onClick={handleSubmitPassage} variant="contained" color="primary">
                    { passageEditId ? "Update" : "Submit" } Passage
                    </Button>
                </DialogActions>
            </Dialog>
            
            {/* <Dialog open={openPassage} onClose={() => setOpenPassage(false)}> */}
            <Dialog open={openEditPassage} onClose={() => setOpenEditPassage(false)}>

                <DialogTitle>Edit Passage</DialogTitle>
                <DialogContent>
                    <TextField
                        label="Passage"
                        value={passage}
                        onChange={(e) => {
                            setPassage(e.target.value);
                            setPassageError('');
                        }}
                        fullWidth
                        multiline
                        rows={6}
                        margin="normal"
                        sx={{ minWidth: 500 }}
                    />
                    
                </DialogContent>
                <DialogActions>
                    {/* <Button onClick={() => setOpenPassage(false)}>Cancel</Button>
                    <Button onClick={handleUpdatePassage} variant="contained" color="primary"> */}
                     <Button onClick={() => setOpenEditPassage(false)}>Cancel</Button>
                    <Button onClick={handleSubmitPassage} variant="contained" color="primary">
                        Update Passage
                    </Button>
                </DialogActions>
            </Dialog>

            <DeleteAlert
                open={deleteOpen}
                title="Are you sure you want delete this Question"
                confirm={handleDeleteNeetQuestion}
                close={() => setDeleteOpen(false)}
            />
            <DeleteAlert
                open={deleteOpen}
                title="Are you sure you want delete this Passage !"
                confirm={handleDeletePassageById}
                close={() => setDeleteOpen(false)}
            />

        </Page>
    )
}

export default CLAT;