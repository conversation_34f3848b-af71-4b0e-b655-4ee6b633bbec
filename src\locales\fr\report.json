{"trialUsers": "Utilisateurs en essai", "dashboard": "Tableau de bord", "subscribedUsers": "Utilisateurs abonnés", "userCourseProgress": "Progrès du cours de l'utilisateur", "all": "Tous", "active": "Actif", "canceled": "<PERSON><PERSON><PERSON>", "expired": "Expiré", "status": "Statut", "plan": "Plan", "createdOn": "<PERSON><PERSON><PERSON>", "customer": "Client", "course": "Cours", "courseProgress": "Progr<PERSON> du cours", "subscribedOn": "<PERSON><PERSON><PERSON><PERSON> le", "simulationStarted": "Simulation commencée", "lastSimulationDate": "Date de la dernière simulation", "caseStudyAttempted": "Étude de cas tentée", "caseStudyStartedOn": "Étude de cas commencée le", "caseStudyResult": "Résultat de l'étude de cas", "certificateSent": "Certificat envoyé", "subscriptionStatus": "Statut de l'abonnement", "phone": "Téléphone", "courses": "Cours", "currentPeriodStart": "Début de la période actuelle", "currentPeriodEnd": "Fin de la période actuelle", "created": "<PERSON><PERSON><PERSON>", "rowsPerPage": "<PERSON><PERSON><PERSON> par page :", "jumpToPage": "Aller à la page :"}